/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { ThemeManager } from '../../themes/themes.js';
import { ChatMessage } from '../../hooks/useChat.js';
import * as fs from 'fs/promises';
import * as path from 'path';

interface ConversationExporterProps {
  themeManager: ThemeManager;
  messages: ChatMessage[];
  isOpen: boolean;
  onClose: () => void;
  onExportComplete?: (filePath: string) => void;
}

type ExportFormat = 'json' | 'markdown' | 'txt' | 'csv';

export const ConversationExporter: React.FC<ConversationExporterProps> = ({
  themeManager,
  messages,
  isOpen,
  onClose,
  onExportComplete,
}) => {
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>('markdown');
  const [fileName, setFileName] = useState('');
  const [isExporting, setIsExporting] = useState(false);
  const [exportStatus, setExportStatus] = useState<string>('');
  const [inputMode, setInputMode] = useState<'format' | 'filename'>('format');

  const formats: { format: ExportFormat; label: string; description: string }[] = [
    { format: 'markdown', label: 'Markdown', description: 'Human-readable format with formatting' },
    { format: 'json', label: 'JSON', description: 'Structured data format for import' },
    { format: 'txt', label: 'Plain Text', description: 'Simple text format' },
    { format: 'csv', label: 'CSV', description: 'Spreadsheet-compatible format' },
  ];

  useInput((input, key) => {
    if (!isOpen || isExporting) return;

    if (key.escape) {
      onClose();
      return;
    }

    if (inputMode === 'format') {
      if (key.upArrow) {
        const currentIndex = formats.findIndex(f => f.format === selectedFormat);
        const newIndex = currentIndex > 0 ? currentIndex - 1 : formats.length - 1;
        setSelectedFormat(formats[newIndex].format);
        return;
      }

      if (key.downArrow) {
        const currentIndex = formats.findIndex(f => f.format === selectedFormat);
        const newIndex = currentIndex < formats.length - 1 ? currentIndex + 1 : 0;
        setSelectedFormat(formats[newIndex].format);
        return;
      }

      if (key.return) {
        setInputMode('filename');
        if (!fileName) {
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
          setFileName(`conversation-${timestamp}.${selectedFormat}`);
        }
        return;
      }
    }

    if (inputMode === 'filename') {
      if (key.return && fileName.trim()) {
        handleExport();
        return;
      }

      if (key.backspace || key.delete) {
        setFileName(prev => prev.slice(0, -1));
        return;
      }

      if (input && input.length === 1 && !key.ctrl && !key.meta) {
        setFileName(prev => prev + input);
      }
    }
  });

  const generateMarkdown = (): string => {
    let content = `# Conversation Export\n\n`;
    content += `**Exported:** ${new Date().toLocaleString()}\n`;
    content += `**Messages:** ${messages.length}\n\n`;
    content += `---\n\n`;

    messages.forEach((message, index) => {
      const timestamp = new Date(message.timestamp).toLocaleString();
      const role = message.role.charAt(0).toUpperCase() + message.role.slice(1);
      
      content += `## Message ${index + 1} - ${role}\n\n`;
      content += `**Time:** ${timestamp}\n\n`;
      
      if (message.error) {
        content += `**Error:** ${message.error}\n\n`;
      }
      
      content += `${message.content}\n\n`;
      content += `---\n\n`;
    });

    return content;
  };

  const generateJSON = (): string => {
    const exportData = {
      exportedAt: new Date().toISOString(),
      messageCount: messages.length,
      messages: messages.map((message, index) => ({
        index,
        role: message.role,
        content: message.content,
        timestamp: message.timestamp,
        error: message.error || null,
        isStreaming: message.isStreaming || false,
      })),
    };

    return JSON.stringify(exportData, null, 2);
  };

  const generatePlainText = (): string => {
    let content = `Conversation Export\n`;
    content += `Exported: ${new Date().toLocaleString()}\n`;
    content += `Messages: ${messages.length}\n\n`;
    content += `${'='.repeat(50)}\n\n`;

    messages.forEach((message) => {
      const timestamp = new Date(message.timestamp).toLocaleString();
      const role = message.role.toUpperCase();
      
      content += `[${timestamp}] ${role}:\n`;
      
      if (message.error) {
        content += `ERROR: ${message.error}\n`;
      }
      
      content += `${message.content}\n\n`;
      content += `${'-'.repeat(30)}\n\n`;
    });

    return content;
  };

  const generateCSV = (): string => {
    let content = 'Index,Role,Timestamp,Content,Error,IsStreaming\n';
    
    messages.forEach((message, index) => {
      const escapedContent = `"${message.content.replace(/"/g, '""')}"`;
      const error = message.error ? `"${message.error.replace(/"/g, '""')}"` : '';
      const isStreaming = message.isStreaming ? 'true' : 'false';
      
      content += `${index},"${message.role}","${new Date(message.timestamp).toISOString()}",${escapedContent},"${error}",${isStreaming}\n`;
    });

    return content;
  };

  const handleExport = async () => {
    if (!fileName.trim()) return;

    setIsExporting(true);
    setExportStatus('Generating export data...');

    try {
      let content: string;
      
      switch (selectedFormat) {
        case 'markdown':
          content = generateMarkdown();
          break;
        case 'json':
          content = generateJSON();
          break;
        case 'txt':
          content = generatePlainText();
          break;
        case 'csv':
          content = generateCSV();
          break;
        default:
          throw new Error(`Unsupported format: ${selectedFormat}`);
      }

      setExportStatus('Writing file...');
      
      const filePath = path.resolve(process.cwd(), fileName);
      await fs.writeFile(filePath, content, 'utf-8');
      
      setExportStatus(`Successfully exported to: ${filePath}`);
      onExportComplete?.(filePath);
      
      setTimeout(() => {
        onClose();
      }, 2000);
      
    } catch (error) {
      setExportStatus(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setTimeout(() => {
        setIsExporting(false);
        setExportStatus('');
      }, 3000);
    }
  };

  if (!isOpen) return null;

  const renderFormatSelection = () => (
    <Box flexDirection="column">
      <Text>{themeManager.primary('Select Export Format:')}</Text>
      <Text></Text>
      {formats.map((format) => (
        <Box key={format.format} marginLeft={2}>
          <Text>
            {selectedFormat === format.format ? themeManager.highlight(' ▶ ') : '   '}
            {themeManager.primary(format.label)}
            {themeManager.muted(` - ${format.description}`)}
          </Text>
        </Box>
      ))}
    </Box>
  );

  const renderFilenameInput = () => (
    <Box flexDirection="column">
      <Text>{themeManager.primary('Enter filename:')}</Text>
      <Text></Text>
      <Box marginLeft={2}>
        <Text>{themeManager.secondary('File: ')}</Text>
        <Text>{themeManager.primary(fileName || '...')}</Text>
      </Box>
      <Text></Text>
      <Box marginLeft={2}>
        <Text>{themeManager.muted('Press Enter to export, Esc to cancel')}</Text>
      </Box>
    </Box>
  );

  const renderExportProgress = () => (
    <Box flexDirection="column">
      <Text>{themeManager.primary('Exporting Conversation...')}</Text>
      <Text></Text>
      <Box marginLeft={2}>
        <Text>{themeManager.info(exportStatus)}</Text>
      </Box>
    </Box>
  );

  const renderSummary = () => (
    <Box flexDirection="column" marginTop={1} borderStyle="single" borderColor="gray" padding={1}>
      <Text>{themeManager.secondary('Export Summary:')}</Text>
      <Text>{themeManager.muted(`Messages: ${messages.length}`)}</Text>
      <Text>{themeManager.muted(`Format: ${selectedFormat.toUpperCase()}`)}</Text>
      {fileName && <Text>{themeManager.muted(`File: ${fileName}`)}</Text>}
    </Box>
  );

  return (
    <Box flexDirection="column" padding={1}>
      <Box borderStyle="round" borderColor="blue" padding={1}>
        <Box flexDirection="column" width="100%">
          <Text>{themeManager.primary('📤 Export Conversation')}</Text>
          <Text></Text>
          
          {isExporting ? (
            renderExportProgress()
          ) : inputMode === 'format' ? (
            renderFormatSelection()
          ) : (
            renderFilenameInput()
          )}
          
          {!isExporting && renderSummary()}
        </Box>
      </Box>
    </Box>
  );
};
