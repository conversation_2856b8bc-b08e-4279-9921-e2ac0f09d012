/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useInput as useInkInput } from 'ink';
import { TerminalInputManager, TerminalInputOptions, TerminalInputState } from '../terminal/TerminalInputManager.js';

export interface UseTerminalInputOptions extends TerminalInputOptions {
  onSubmit?: (value: string) => void;
  onEscape?: () => void;
  onPasteAccepted?: (data: { originalText: string; filteredText: string; newValue: string }) => void;
  onPasteRejected?: (data: { text: string; reason: string }) => void;
  onValidationChanged?: (data: { isValid: boolean; value: string }) => void;
  disabled?: boolean;
}

export interface UseTerminalInputReturn {
  state: TerminalInputState;
  manager: TerminalInputManager;
  clear: () => void;
  setValue: (value: string) => void;
  setDisabled: (disabled: boolean) => void;
  updateOptions: (options: Partial<TerminalInputOptions>) => void;
}

/**
 * React hook that provides advanced terminal input functionality using TerminalInputManager
 */
export const useTerminalInput = (options: UseTerminalInputOptions = {}): UseTerminalInputReturn => {
  const managerRef = useRef<TerminalInputManager | null>(null);
  const [state, setState] = useState<TerminalInputState>({
    value: '',
    displayValue: '',
    isValid: true,
    validationMessage: '',
    isPasteDetected: false,
    cursorPosition: 0,
    isDisabled: options.disabled || false,
  });

  // Initialize manager
  useEffect(() => {
    const { onSubmit, onEscape, onPasteAccepted, onPasteRejected, onValidationChanged, disabled, ...managerOptions } = options;
    
    managerRef.current = new TerminalInputManager(managerOptions);
    
    // Set initial disabled state
    if (disabled) {
      managerRef.current.setDisabled(disabled);
    }

    // Set up event listeners
    const manager = managerRef.current;

    const handleValueChanged = () => {
      setState(manager.getState());
    };

    const handleSubmit = (data: { value: string }) => {
      if (onSubmit) {
        onSubmit(data.value);
      }
    };

    const handleEscape = () => {
      if (onEscape) {
        onEscape();
      }
    };

    const handlePasteAccepted = (data: { originalText: string; filteredText: string; newValue: string }) => {
      if (onPasteAccepted) {
        onPasteAccepted(data);
      }
    };

    const handlePasteRejected = (data: { text: string; reason: string }) => {
      if (onPasteRejected) {
        onPasteRejected(data);
      }
    };

    const handleValidationChanged = (data: { isValid: boolean; value: string }) => {
      if (onValidationChanged) {
        onValidationChanged(data);
      }
    };

    const handlePasteDetectionChanged = () => {
      setState(manager.getState());
    };

    const handleDisabledChanged = () => {
      setState(manager.getState());
    };

    // Register event listeners
    manager.on('valueChanged', handleValueChanged);
    manager.on('submit', handleSubmit);
    manager.on('escape', handleEscape);
    manager.on('pasteAccepted', handlePasteAccepted);
    manager.on('pasteRejected', handlePasteRejected);
    manager.on('validationChanged', handleValidationChanged);
    manager.on('pasteDetectionChanged', handlePasteDetectionChanged);
    manager.on('disabledChanged', handleDisabledChanged);

    // Initial state update
    setState(manager.getState());

    // Cleanup
    return () => {
      manager.destroy();
    };
  }, []); // Empty dependency array - manager is created once

  // Update options when they change
  useEffect(() => {
    if (managerRef.current) {
      const { onSubmit, onEscape, onPasteAccepted, onPasteRejected, onValidationChanged, disabled, ...managerOptions } = options;
      managerRef.current.updateOptions(managerOptions);
      
      if (disabled !== undefined) {
        managerRef.current.setDisabled(disabled);
      }
    }
  }, [options]);

  // Integrate with Ink's useInput
  useInkInput((inputChar, key) => {
    if (managerRef.current) {
      managerRef.current.processInput(inputChar, key);
    }
  });

  // Memoized functions
  const clear = useCallback(() => {
    if (managerRef.current) {
      managerRef.current.clear();
    }
  }, []);

  const setValue = useCallback((value: string) => {
    if (managerRef.current) {
      managerRef.current.setValue(value);
    }
  }, []);

  const setDisabled = useCallback((disabled: boolean) => {
    if (managerRef.current) {
      managerRef.current.setDisabled(disabled);
    }
  }, []);

  const updateOptions = useCallback((newOptions: Partial<TerminalInputOptions>) => {
    if (managerRef.current) {
      managerRef.current.updateOptions(newOptions);
    }
  }, []);

  return {
    state,
    manager: managerRef.current!,
    clear,
    setValue,
    setDisabled,
    updateOptions,
  };
};

/**
 * Specialized hook for API key input with built-in validation
 */
export const useApiKeyTerminalInput = (provider: string, options: Omit<UseTerminalInputOptions, 'masked' | 'validator'> = {}) => {
  const apiKeyValidators: Record<string, (key: string) => boolean> = {
    google: (key: string) => key.startsWith('AIza') && key.length === 39,
    openai: (key: string) => key.startsWith('sk-') && key.length >= 48,
    deepseek: (key: string) => key.startsWith('sk-') && key.length >= 48,
    anthropic: (key: string) => key.startsWith('sk-ant-') && key.length >= 50,
  };

  const validator = apiKeyValidators[provider] || ((key: string) => key.length > 0);

  const characterFilter = (char: string): boolean => {
    const charCode = char.charCodeAt(0);
    return (
      (charCode >= 48 && charCode <= 57) || // 0-9
      (charCode >= 65 && charCode <= 90) || // A-Z
      (charCode >= 97 && charCode <= 122) || // a-z
      charCode === 45 || // -
      charCode === 95    // _
    );
  };

  return useTerminalInput({
    ...options,
    masked: true,
    maskChar: '*',
    validator,
    characterFilter,
    maxLength: 100,
  });
};

/**
 * Specialized hook for chat input with enhanced paste handling
 */
export const useChatTerminalInput = (options: UseTerminalInputOptions = {}) => {
  return useTerminalInput({
    ...options,
    maxLength: 10000, // Allow long messages
    realTimeValidation: false, // Don't validate chat messages in real-time
  });
};
