/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import React from 'react';
import { ThemeManager } from '../../themes/themes.js';
export interface RadioOption {
    value: string;
    label: string;
    description?: string;
    disabled?: boolean;
}
export interface RadioButtonsProps {
    themeManager: ThemeManager;
    options: RadioOption[];
    selectedValue?: string;
    onSelectionChange?: (value: string, option: RadioOption) => void;
    onSubmit?: (value: string, option: RadioOption) => void;
    disabled?: boolean;
    showDescriptions?: boolean;
    showIndicators?: boolean;
    allowDeselect?: boolean;
    variant?: 'default' | 'compact' | 'detailed';
    title?: string;
    helpText?: string;
    width?: number;
    maxHeight?: number;
    scrollable?: boolean;
}
export declare const RadioButtons: React.FC<RadioButtonsProps>;
export declare const CompactRadioButtons: React.FC<Omit<RadioButtonsProps, 'variant'>>;
export declare const DetailedRadioButtons: React.FC<Omit<RadioButtonsProps, 'variant'>>;
//# sourceMappingURL=RadioButtons.d.ts.map