/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import chalk from 'chalk';
export const themes = {
    default: {
        name: 'Default',
        description: 'Clean and professional theme with blue accents',
        colors: {
            primary: chalk.blue,
            secondary: chalk.cyan,
            accent: chalk.magenta,
            success: chalk.green,
            warning: chalk.yellow,
            error: chalk.red,
            info: chalk.blue,
            muted: chalk.gray,
            background: chalk.bgBlack,
            border: chalk.gray,
            highlight: chalk.bgBlue.white,
        },
        symbols: {
            bullet: '•',
            arrow: '→',
            check: '✓',
            cross: '✗',
            info: 'ℹ',
            warning: '⚠',
            question: '?',
            loading: ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'],
        },
    },
    dark: {
        name: 'Dark',
        description: 'Dark theme with purple and green accents',
        colors: {
            primary: chalk.magenta,
            secondary: chalk.green,
            accent: chalk.cyan,
            success: chalk.greenBright,
            warning: chalk.yellowBright,
            error: chalk.redBright,
            info: chalk.cyanBright,
            muted: chalk.gray,
            background: chalk.bgBlack,
            border: chalk.white,
            highlight: chalk.bgMagenta.white,
        },
        symbols: {
            bullet: '▶',
            arrow: '▸',
            check: '✅',
            cross: '❌',
            info: '💡',
            warning: '⚡',
            question: '❓',
            loading: ['◐', '◓', '◑', '◒'],
        },
    },
    light: {
        name: 'Light',
        description: 'Light theme with warm colors',
        colors: {
            primary: chalk.blue,
            secondary: chalk.green,
            accent: chalk.red,
            success: chalk.green,
            warning: chalk.yellow,
            error: chalk.red,
            info: chalk.blue,
            muted: chalk.blackBright,
            background: chalk.bgWhite,
            border: chalk.blackBright,
            highlight: chalk.bgBlue.white,
        },
        symbols: {
            bullet: '●',
            arrow: '▶',
            check: '✓',
            cross: '✗',
            info: 'i',
            warning: '!',
            question: '?',
            loading: ['⣾', '⣽', '⣻', '⢿', '⡿', '⣟', '⣯', '⣷'],
        },
    },
    neon: {
        name: 'Neon',
        description: 'Vibrant neon theme with bright colors',
        colors: {
            primary: chalk.hex('#00FFFF'),
            secondary: chalk.hex('#FF00FF'),
            accent: chalk.hex('#FFFF00'),
            success: chalk.hex('#00FF00'),
            warning: chalk.hex('#FFA500'),
            error: chalk.hex('#FF0000'),
            info: chalk.hex('#00BFFF'),
            muted: chalk.hex('#808080'),
            background: chalk.bgBlack,
            border: chalk.hex('#FFFFFF'),
            highlight: chalk.bgHex('#FF00FF').white,
        },
        symbols: {
            bullet: '◆',
            arrow: '⟶',
            check: '✨',
            cross: '💥',
            info: '🔮',
            warning: '⚡',
            question: '🤔',
            loading: ['🌟', '✨', '💫', '⭐'],
        },
    },
    minimal: {
        name: 'Minimal',
        description: 'Clean minimal theme with subtle colors',
        colors: {
            primary: chalk.white,
            secondary: chalk.gray,
            accent: chalk.whiteBright,
            success: chalk.green,
            warning: chalk.yellow,
            error: chalk.red,
            info: chalk.blue,
            muted: chalk.gray,
            background: chalk.bgBlack,
            border: chalk.gray,
            highlight: chalk.bgWhite.black,
        },
        symbols: {
            bullet: '-',
            arrow: '>',
            check: '+',
            cross: 'x',
            info: 'i',
            warning: '!',
            question: '?',
            loading: ['-', '\\', '|', '/'],
        },
    },
    retro: {
        name: 'Retro',
        description: 'Retro computing theme with amber and green',
        colors: {
            primary: chalk.hex('#FFB000'),
            secondary: chalk.hex('#00FF00'),
            accent: chalk.hex('#FF8000'),
            success: chalk.hex('#00FF00'),
            warning: chalk.hex('#FFFF00'),
            error: chalk.hex('#FF4000'),
            info: chalk.hex('#00FFFF'),
            muted: chalk.hex('#808000'),
            background: chalk.bgBlack,
            border: chalk.hex('#FFB000'),
            highlight: chalk.bgHex('#FFB000').black,
        },
        symbols: {
            bullet: '*',
            arrow: '>>',
            check: 'OK',
            cross: 'ERR',
            info: 'INFO',
            warning: 'WARN',
            question: 'INPUT',
            loading: ['[    ]', '[=   ]', '[==  ]', '[=== ]', '[====]', '[ ===]', '[  ==]', '[   =]'],
        },
    },
};
export class ThemeManager {
    currentTheme;
    themeSelected = false;
    constructor(themeName = 'default') {
        this.currentTheme = themes[themeName] || themes.default;
        this.themeSelected = themeName !== 'default';
        // Ensure we have a valid theme
        if (!this.currentTheme) {
            console.error('Failed to initialize theme:', themeName);
            this.currentTheme = themes.default;
        }
    }
    /**
     * Gets the current theme
     */
    getCurrentTheme() {
        if (!this.currentTheme) {
            console.error('ThemeManager: currentTheme is undefined in getCurrentTheme');
            return themes.default;
        }
        return this.currentTheme;
    }
    /**
     * Sets the current theme
     */
    setTheme(themeName) {
        if (themes[themeName]) {
            this.currentTheme = themes[themeName];
            this.themeSelected = true;
        }
    }
    /**
     * Checks if a theme has been explicitly selected
     */
    isThemeSelected() {
        return this.themeSelected;
    }
    /**
     * Gets all available themes
     */
    getAvailableThemes() {
        return Object.entries(themes).map(([name, theme]) => ({ name, theme }));
    }
    /**
     * Gets theme names
     */
    getThemeNames() {
        return Object.keys(themes);
    }
    /**
     * Applies theme colors to text
     */
    primary(text) {
        if (!this.currentTheme) {
            console.error('ThemeManager: currentTheme is undefined');
            return text; // Return plain text as fallback
        }
        return this.currentTheme.colors.primary(text);
    }
    secondary(text) {
        if (!this.currentTheme)
            return text;
        return this.currentTheme.colors.secondary(text);
    }
    accent(text) {
        if (!this.currentTheme)
            return text;
        return this.currentTheme.colors.accent(text);
    }
    success(text) {
        if (!this.currentTheme)
            return text;
        return this.currentTheme.colors.success(text);
    }
    warning(text) {
        if (!this.currentTheme)
            return text;
        return this.currentTheme.colors.warning(text);
    }
    error(text) {
        if (!this.currentTheme)
            return text;
        return this.currentTheme.colors.error(text);
    }
    info(text) {
        if (!this.currentTheme)
            return text;
        return this.currentTheme.colors.info(text);
    }
    muted(text) {
        if (!this.currentTheme)
            return text;
        return this.currentTheme.colors.muted(text);
    }
    highlight(text) {
        if (!this.currentTheme)
            return text;
        return this.currentTheme.colors.highlight(text);
    }
    /**
     * Gets theme symbols
     */
    getSymbol(symbol) {
        if (!this.currentTheme)
            return '';
        return this.currentTheme.symbols[symbol];
    }
}
//# sourceMappingURL=themes.js.map