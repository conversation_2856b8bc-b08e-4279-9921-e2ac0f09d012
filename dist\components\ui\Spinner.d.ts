/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import React from 'react';
import { ThemeManager } from '../../themes/themes.js';
interface SpinnerProps {
    themeManager: ThemeManager;
    type?: 'dots' | 'line' | 'pipe' | 'star' | 'bounce' | 'toggle' | 'balloon' | 'noise' | 'runner' | 'pong';
    color?: 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'error' | 'info' | 'muted';
    speed?: 'slow' | 'normal' | 'fast';
}
export declare const Spinner: React.FC<SpinnerProps>;
export declare const LoadingSpinner: React.FC<Omit<SpinnerProps, 'type'>>;
export declare const ProcessingSpinner: React.FC<Omit<SpinnerProps, 'type'>>;
export declare const SuccessSpinner: React.FC<Omit<SpinnerProps, 'color'>>;
export declare const ErrorSpinner: React.FC<Omit<SpinnerProps, 'color'>>;
export declare const WarningSpinner: React.FC<Omit<SpinnerProps, 'color'>>;
export declare const InfoSpinner: React.FC<Omit<SpinnerProps, 'color'>>;
export {};
//# sourceMappingURL=Spinner.d.ts.map