/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React from 'react';
import { Box, Text } from 'ink';
import { ThemeManager } from '../../themes/themes.js';
import { useInput } from '../../hooks/useInput.js';

interface ChatInputProps {
  themeManager: ThemeManager;
  onSendMessage: (message: string) => void;
  onClear?: () => void;
  onHelp?: () => void;
  onExit?: () => void;
  disabled?: boolean;
  placeholder?: string;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  themeManager,
  onSendMessage,
  onClear,
  onHelp,
  onExit,
  disabled = false,
  placeholder = 'Type your message...',
}) => {
  const { input, isDisabled } = useInput({
    onSubmit: onSendMessage,
    onClear,
    onHelp,
    onExit,
    disabled,
    onPaste: (_pastedText) => {
      // Could add paste feedback here if needed
    },
  });

  const renderPrompt = () => {
    if (isDisabled) {
      return themeManager.muted('> ');
    }
    return themeManager.primary('> ');
  };

  const renderInput = () => {
    if (input) {
      return input;
    }

    if (isDisabled) {
      return themeManager.muted('Please wait...');
    }

    return themeManager.muted(placeholder);
  };

  const renderCursor = () => {
    if (!isDisabled) {
      return <Text>{themeManager.accent('▋')}</Text>;
    }
    return null;
  };

  return (
    <Box 
      borderStyle="round" 
      borderColor={isDisabled ? "gray" : "blue"} 
      padding={1}
    >
      <Text>{renderPrompt()}</Text>
      <Text>{renderInput()}</Text>
      {renderCursor()}
    </Box>
  );
};
