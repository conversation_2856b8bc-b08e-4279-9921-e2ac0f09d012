{"version": 3, "file": "credentials.d.ts", "sourceRoot": "", "sources": ["../../src/auth/credentials.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAMH,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AAEvD,MAAM,WAAW,iBAAiB;IAChC,SAAS,EAAE,MAAM,CAAC,YAAY,EAAE;QAC9B,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,QAAQ,CAAC,EAAE,MAAM,CAAC;KACnB,CAAC,CAAC;IACH,eAAe,CAAC,EAAE,YAAY,CAAC;IAC/B,WAAW,EAAE,MAAM,CAAC;CACrB;AAED,qBAAa,kBAAkB;IAC7B,OAAO,CAAC,eAAe,CAAS;IAChC,OAAO,CAAC,WAAW,CAAoB;;IAQvC;;OAEG;IACH,OAAO,CAAC,eAAe;IAsBvB;;OAEG;IACH,OAAO,CAAC,eAAe;IAiBvB;;OAEG;IACH,SAAS,CAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IAUvD;;OAEG;IACH,SAAS,CAAC,QAAQ,EAAE,YAAY,GAAG,MAAM,GAAG,SAAS;IAIrD;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI;IAUrD;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,YAAY,GAAG,MAAM,GAAG,SAAS;IAIpD;;OAEG;IACH,UAAU,CAAC,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI;IAUzD;;OAEG;IACH,UAAU,CAAC,QAAQ,EAAE,YAAY,GAAG,MAAM,GAAG,SAAS;IAItD;;OAEG;IACH,kBAAkB,CAAC,QAAQ,EAAE,YAAY,GAAG,IAAI;IAKhD;;OAEG;IACH,kBAAkB,IAAI,YAAY,GAAG,SAAS;IAI9C;;OAEG;IACH,oBAAoB,CAAC,QAAQ,EAAE,YAAY,GAAG,OAAO;IAKrD;;OAEG;IACH,sBAAsB,IAAI,YAAY,EAAE;IAKxC;;OAEG;IACH,iBAAiB,CAAC,QAAQ,EAAE,YAAY,GAAG;QACzC,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,QAAQ,CAAC,EAAE,MAAM,CAAC;KACnB;IAID;;OAEG;IACH,cAAc,CAAC,QAAQ,EAAE,YAAY,GAAG,IAAI;IAW5C;;OAEG;IACH,QAAQ,IAAI,IAAI;IAahB;;OAEG;IACH,qBAAqB,IAAI,YAAY,GAAG,SAAS;IAcjD;;OAEG;IACH,mBAAmB,IAAI,OAAO;IAK9B;;OAEG;IACH,cAAc,CAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO;CAchE"}