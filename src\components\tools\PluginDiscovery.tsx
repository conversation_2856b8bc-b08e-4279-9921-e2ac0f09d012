/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { ThemeManager } from '../../themes/themes.js';

interface PluginInfo {
  id: string;
  name: string;
  description: string;
  version: string;
  author: string;
  category: string;
  tags: string[];
  installed: boolean;
  enabled: boolean;
  downloadUrl?: string;
  homepage?: string;
  repository?: string;
  license: string;
  rating: number;
  downloads: number;
  lastUpdated: Date;
  dependencies: string[];
  compatibility: string[];
  size: string;
}

interface PluginDiscoveryProps {
  themeManager: ThemeManager;
  isOpen: boolean;
  onClose: () => void;
  onInstall?: (pluginId: string) => Promise<boolean>;
  onUninstall?: (pluginId: string) => Promise<boolean>;
  onEnable?: (pluginId: string) => Promise<boolean>;
  onDisable?: (pluginId: string) => Promise<boolean>;
}

export const PluginDiscovery: React.FC<PluginDiscoveryProps> = ({
  themeManager,
  isOpen,
  onClose,
  onInstall,
  onUninstall,
  onEnable,
  onDisable,
}) => {
  const [selectedPluginIndex, setSelectedPluginIndex] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'installed' | 'available'>('all');
  const [mode, setMode] = useState<'list' | 'details' | 'installing'>('list');
  const [searchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'rating' | 'downloads' | 'updated'>('rating');
  const [isLoading, setIsLoading] = useState(false);
  const [operationStatus, setOperationStatus] = useState<string>('');

  // Mock plugin data - in real implementation, this would come from a plugin registry
  const [plugins, setPlugins] = useState<PluginInfo[]>([
    {
      id: 'filesystem-tools',
      name: 'Filesystem Tools',
      description: 'Advanced filesystem operations and file management',
      version: '2.1.0',
      author: 'Arien Team',
      category: 'File Management',
      tags: ['filesystem', 'files', 'directories'],
      installed: true,
      enabled: true,
      homepage: 'https://github.com/arien/filesystem-tools',
      repository: 'https://github.com/arien/filesystem-tools',
      license: 'MIT',
      rating: 4.8,
      downloads: 15420,
      lastUpdated: new Date('2024-12-01'),
      dependencies: [],
      compatibility: ['mcp-1.0', 'arien-2.0'],
      size: '2.3 MB',
    },
    {
      id: 'git-integration',
      name: 'Git Integration',
      description: 'Complete Git version control integration',
      version: '1.5.2',
      author: 'DevTools Inc',
      category: 'Version Control',
      tags: ['git', 'vcs', 'repository'],
      installed: false,
      enabled: false,
      downloadUrl: 'https://registry.arien.ai/plugins/git-integration',
      homepage: 'https://github.com/devtools/git-integration',
      repository: 'https://github.com/devtools/git-integration',
      license: 'Apache-2.0',
      rating: 4.6,
      downloads: 8930,
      lastUpdated: new Date('2024-11-28'),
      dependencies: ['git'],
      compatibility: ['mcp-1.0', 'arien-2.0'],
      size: '1.8 MB',
    },
    {
      id: 'web-scraper',
      name: 'Web Scraper Pro',
      description: 'Advanced web scraping and data extraction tools',
      version: '3.0.1',
      author: 'WebTools LLC',
      category: 'Web',
      tags: ['web', 'scraping', 'data', 'extraction'],
      installed: true,
      enabled: false,
      homepage: 'https://webtools.com/scraper-pro',
      repository: 'https://github.com/webtools/scraper-pro',
      license: 'Commercial',
      rating: 4.9,
      downloads: 23450,
      lastUpdated: new Date('2024-12-05'),
      dependencies: ['puppeteer', 'cheerio'],
      compatibility: ['mcp-1.0', 'arien-2.0'],
      size: '5.2 MB',
    },
    {
      id: 'database-connector',
      name: 'Database Connector',
      description: 'Connect and query various database systems',
      version: '2.3.0',
      author: 'DataBase Co',
      category: 'Database',
      tags: ['database', 'sql', 'nosql', 'query'],
      installed: false,
      enabled: false,
      downloadUrl: 'https://registry.arien.ai/plugins/database-connector',
      homepage: 'https://database.co/connector',
      license: 'MIT',
      rating: 4.4,
      downloads: 12100,
      lastUpdated: new Date('2024-11-20'),
      dependencies: ['mysql2', 'mongodb', 'redis'],
      compatibility: ['mcp-1.0'],
      size: '3.7 MB',
    },
  ]);

  useInput((input, key) => {
    if (!isOpen) return;

    if (key.escape) {
      if (mode === 'details' || mode === 'installing') {
        setMode('list');
        return;
      }
      onClose();
      return;
    }

    if (mode === 'list') {
      const filteredPlugins = getFilteredPlugins();
      
      if (key.upArrow && selectedPluginIndex > 0) {
        setSelectedPluginIndex(selectedPluginIndex - 1);
        return;
      }
      if (key.downArrow && selectedPluginIndex < filteredPlugins.length - 1) {
        setSelectedPluginIndex(selectedPluginIndex + 1);
        return;
      }
      if (key.return) {
        setMode('details');
        return;
      }
      if (key.tab) {
        cycleCategoryFilter();
        return;
      }
      if (input === 's') {
        cycleSortOrder();
        return;
      }
      if (input === 'i') {
        const plugin = filteredPlugins[selectedPluginIndex];
        if (!plugin.installed) {
          handleInstall(plugin.id);
        }
        return;
      }
      if (input === 'u') {
        const plugin = filteredPlugins[selectedPluginIndex];
        if (plugin.installed) {
          handleUninstall(plugin.id);
        }
        return;
      }
      if (input === ' ') {
        const plugin = filteredPlugins[selectedPluginIndex];
        if (plugin.installed) {
          plugin.enabled ? handleDisable(plugin.id) : handleEnable(plugin.id);
        }
        return;
      }
    }

    if (mode === 'details') {
      const filteredPlugins = getFilteredPlugins();
      const plugin = filteredPlugins[selectedPluginIndex];
      
      if (input === 'i' && !plugin.installed) {
        handleInstall(plugin.id);
        return;
      }
      if (input === 'u' && plugin.installed) {
        handleUninstall(plugin.id);
        return;
      }
      if (input === ' ' && plugin.installed) {
        plugin.enabled ? handleDisable(plugin.id) : handleEnable(plugin.id);
        return;
      }
    }
  });

  const getFilteredPlugins = () => {
    let filtered = plugins;
    
    if (selectedCategory === 'installed') {
      filtered = filtered.filter(plugin => plugin.installed);
    } else if (selectedCategory === 'available') {
      filtered = filtered.filter(plugin => !plugin.installed);
    }
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(plugin => 
        plugin.name.toLowerCase().includes(query) ||
        plugin.description.toLowerCase().includes(query) ||
        plugin.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }
    
    // Sort plugins
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'rating':
          return b.rating - a.rating;
        case 'downloads':
          return b.downloads - a.downloads;
        case 'updated':
          return b.lastUpdated.getTime() - a.lastUpdated.getTime();
        default:
          return 0;
      }
    });
    
    return filtered;
  };

  const cycleCategoryFilter = () => {
    const categories: Array<'all' | 'installed' | 'available'> = ['all', 'installed', 'available'];
    const currentIndex = categories.indexOf(selectedCategory);
    const nextIndex = (currentIndex + 1) % categories.length;
    setSelectedCategory(categories[nextIndex]);
    setSelectedPluginIndex(0);
  };

  const cycleSortOrder = () => {
    const sortOptions: Array<'name' | 'rating' | 'downloads' | 'updated'> = ['name', 'rating', 'downloads', 'updated'];
    const currentIndex = sortOptions.indexOf(sortBy);
    const nextIndex = (currentIndex + 1) % sortOptions.length;
    setSortBy(sortOptions[nextIndex]);
  };

  const handleInstall = async (pluginId: string) => {
    setMode('installing');
    setOperationStatus('Installing plugin...');
    setIsLoading(true);

    try {
      const success = await onInstall?.(pluginId);
      if (success) {
        setPlugins(prev => prev.map(plugin => 
          plugin.id === pluginId 
            ? { ...plugin, installed: true, enabled: true }
            : plugin
        ));
        setOperationStatus('Plugin installed successfully!');
      } else {
        setOperationStatus('Installation failed');
      }
    } catch (error) {
      setOperationStatus(`Installation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
      setTimeout(() => setMode('list'), 2000);
    }
  };

  const handleUninstall = async (pluginId: string) => {
    setMode('installing');
    setOperationStatus('Uninstalling plugin...');
    setIsLoading(true);

    try {
      const success = await onUninstall?.(pluginId);
      if (success) {
        setPlugins(prev => prev.map(plugin => 
          plugin.id === pluginId 
            ? { ...plugin, installed: false, enabled: false }
            : plugin
        ));
        setOperationStatus('Plugin uninstalled successfully!');
      } else {
        setOperationStatus('Uninstallation failed');
      }
    } catch (error) {
      setOperationStatus(`Uninstallation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
      setTimeout(() => setMode('list'), 2000);
    }
  };

  const handleEnable = async (pluginId: string) => {
    try {
      const success = await onEnable?.(pluginId);
      if (success) {
        setPlugins(prev => prev.map(plugin => 
          plugin.id === pluginId ? { ...plugin, enabled: true } : plugin
        ));
      }
    } catch (error) {
      console.error('Failed to enable plugin:', error);
    }
  };

  const handleDisable = async (pluginId: string) => {
    try {
      const success = await onDisable?.(pluginId);
      if (success) {
        setPlugins(prev => prev.map(plugin => 
          plugin.id === pluginId ? { ...plugin, enabled: false } : plugin
        ));
      }
    } catch (error) {
      console.error('Failed to disable plugin:', error);
    }
  };

  if (!isOpen) return null;

  const renderPluginsList = () => {
    const filteredPlugins = getFilteredPlugins();
    
    return (
      <Box flexDirection="column">
        <Box justifyContent="space-between">
          <Text>{themeManager.primary('Plugin Discovery')}</Text>
          <Text>{themeManager.muted(`${selectedCategory} | Sort: ${sortBy} (${filteredPlugins.length})`)}</Text>
        </Box>
        
        <Text></Text>
        {filteredPlugins.map((plugin, index) => {
          const isSelected = index === selectedPluginIndex;
          
          return (
            <Box key={plugin.id} marginLeft={2} marginBottom={1}>
              <Box flexDirection="column">
                <Box>
                  <Text>
                    {isSelected ? themeManager.highlight(' ▶ ') : '   '}
                    {plugin.installed ? (
                      plugin.enabled ? themeManager.success('●') : themeManager.warning('●')
                    ) : themeManager.muted('○')}
                    {' '}
                    {themeManager.primary(plugin.name)}
                    {' '}
                    {themeManager.accent(`v${plugin.version}`)}
                    {' '}
                    {themeManager.muted(`[${plugin.category}]`)}
                  </Text>
                </Box>
                <Box marginLeft={3}>
                  <Text>{themeManager.muted(plugin.description)}</Text>
                </Box>
                <Box marginLeft={3}>
                  <Text>
                    {themeManager.info(`★${plugin.rating.toFixed(1)}`)}
                    {' '}
                    {themeManager.muted(`${plugin.downloads.toLocaleString()} downloads`)}
                    {' '}
                    {themeManager.secondary(`by ${plugin.author}`)}
                  </Text>
                </Box>
              </Box>
            </Box>
          );
        })}
        
        {filteredPlugins.length === 0 && (
          <Box justifyContent="center" marginTop={2}>
            <Text>{themeManager.muted('No plugins found matching current filter')}</Text>
          </Box>
        )}
      </Box>
    );
  };

  const renderPluginDetails = () => {
    const filteredPlugins = getFilteredPlugins();
    const plugin = filteredPlugins[selectedPluginIndex];
    if (!plugin) return null;

    return (
      <Box flexDirection="column">
        <Text>{themeManager.primary(`Plugin: ${plugin.name}`)}</Text>
        <Text></Text>
        
        <Box marginLeft={2} flexDirection="column">
          <Text>{themeManager.secondary('Version: ')}{themeManager.info(plugin.version)}</Text>
          <Text>{themeManager.secondary('Author: ')}{themeManager.accent(plugin.author)}</Text>
          <Text>{themeManager.secondary('Category: ')}{themeManager.accent(plugin.category)}</Text>
          <Text>{themeManager.secondary('License: ')}{themeManager.muted(plugin.license)}</Text>
          <Text>{themeManager.secondary('Size: ')}{themeManager.muted(plugin.size)}</Text>
          <Text>{themeManager.secondary('Rating: ')}{themeManager.info(`★${plugin.rating.toFixed(1)}`)}</Text>
          <Text>{themeManager.secondary('Downloads: ')}{themeManager.muted(plugin.downloads.toLocaleString())}</Text>
          <Text>{themeManager.secondary('Updated: ')}{themeManager.muted(plugin.lastUpdated.toLocaleDateString())}</Text>
          
          <Text></Text>
          <Text>{themeManager.secondary('Status:')}</Text>
          <Text>
            {plugin.installed ? themeManager.success('Installed') : themeManager.muted('Not installed')}
            {plugin.installed && (plugin.enabled ? themeManager.success(' (Enabled)') : themeManager.warning(' (Disabled)'))}
          </Text>
          
          <Text></Text>
          <Text>{themeManager.secondary('Description:')}</Text>
          <Text>{themeManager.muted(plugin.description)}</Text>
          
          <Text></Text>
          <Text>{themeManager.secondary('Tags:')}</Text>
          <Text>{themeManager.accent(plugin.tags.join(', '))}</Text>
          
          {plugin.dependencies.length > 0 && (
            <>
              <Text></Text>
              <Text>{themeManager.secondary('Dependencies:')}</Text>
              <Text>{themeManager.muted(plugin.dependencies.join(', '))}</Text>
            </>
          )}
          
          <Text></Text>
          <Text>{themeManager.secondary('Compatibility:')}</Text>
          <Text>{themeManager.muted(plugin.compatibility.join(', '))}</Text>
        </Box>
      </Box>
    );
  };

  const renderInstallationProgress = () => (
    <Box flexDirection="column">
      <Text>{themeManager.primary('Plugin Operation')}</Text>
      <Text></Text>
      <Box marginLeft={2}>
        <Text>{isLoading ? themeManager.info('⟳ ') : ''}{operationStatus}</Text>
      </Box>
    </Box>
  );

  const renderControls = () => (
    <Box marginTop={1} borderStyle="single" borderColor="gray" padding={1}>
      <Box flexDirection="column">
        <Text>{themeManager.muted('Controls:')}</Text>
        {mode === 'list' && (
          <>
            <Text>{themeManager.muted('  ↑↓ Navigate • Enter Details • Tab Filter • S Sort')}</Text>
            <Text>{themeManager.muted('  I Install • U Uninstall • Space Toggle • Esc Close')}</Text>
          </>
        )}
        {mode === 'details' && (
          <Text>{themeManager.muted('  I Install • U Uninstall • Space Toggle • Esc Back')}</Text>
        )}
        {mode === 'installing' && (
          <Text>{themeManager.muted('  Please wait...')}</Text>
        )}
      </Box>
    </Box>
  );

  return (
    <Box flexDirection="column" padding={1}>
      <Box borderStyle="round" borderColor="blue" padding={1}>
        <Box flexDirection="column" width="100%">
          <Text>{themeManager.primary('🔌 Plugin Discovery')}</Text>
          <Text></Text>
          
          {mode === 'list' && renderPluginsList()}
          {mode === 'details' && renderPluginDetails()}
          {mode === 'installing' && renderInstallationProgress()}
          
          {renderControls()}
        </Box>
      </Box>
    </Box>
  );
};
