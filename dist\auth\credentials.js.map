{"version": 3, "file": "credentials.js", "sourceRoot": "", "sources": ["../../src/auth/credentials.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAchD,MAAM,OAAO,kBAAkB;IACrB,eAAe,CAAS;IACxB,WAAW,CAAoB;IAEvC;QACE,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;QAChE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IAC5C,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,IAAI,CAAC;YACH,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;gBACxC,MAAM,IAAI,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;gBAC3D,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC;QAED,2BAA2B;QAC3B,OAAO;YACL,SAAS,EAAE;gBACT,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,SAAS,EAAE,EAAE;aACd;YACD,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACrD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAC/C,CAAC;YAED,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC1C,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QACpF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,UAAU,CAClB,+BAA+B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EACvF,wBAAwB,CACzB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,QAAsB,EAAE,MAAc;QAC9C,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC;QACrD,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC3D,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,QAAsB;QAC9B,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,QAAsB,EAAE,KAAa;QAC5C,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;QACnD,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC3D,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,QAAsB;QAC7B,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,QAAsB,EAAE,OAAe;QAChD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC;QACvD,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC3D,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,QAAsB;QAC/B,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,QAAsB;QACvC,IAAI,CAAC,WAAW,CAAC,eAAe,GAAG,QAAQ,CAAC;QAC5C,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,QAAsB;QACzC,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC5D,OAAO,CAAC,CAAC,CAAC,cAAc,EAAE,MAAM,IAAI,cAAc,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAQ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAoB;aAC/D,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,QAAsB;QAMtC,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,QAAsB;QACnC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;QAE1C,6CAA6C;QAC7C,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,KAAK,QAAQ,EAAE,CAAC;YAClD,IAAI,CAAC,WAAW,CAAC,eAAe,GAAG,SAAS,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,WAAW,GAAG;YACjB,SAAS,EAAE;gBACT,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,QAAQ,EAAE,EAAE;gBACZ,SAAS,EAAE,EAAE;aACd;YACD,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;QACF,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,IAAI,UAAoC,CAAC;QACzC,IAAI,cAAc,GAAG,CAAC,CAAC;QAEvB,KAAK,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5E,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,GAAG,cAAc,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAwB,CAAC,EAAE,CAAC;gBAC/G,UAAU,GAAG,QAAwB,CAAC;gBACtC,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC;YACnC,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,MAAM,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC1D,OAAO,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,QAAsB,EAAE,MAAc;QACnD,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,QAAQ;gBACX,OAAO,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,CAAC;YAC3D,KAAK,QAAQ;gBACX,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC;YACzD,KAAK,UAAU;gBACb,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC;YACzD,KAAK,WAAW;gBACd,OAAO,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC;YAC7D;gBACE,OAAO,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;CACF"}