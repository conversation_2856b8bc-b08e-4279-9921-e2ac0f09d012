/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { Config } from '../config/config.js';
import { BaseTool, ToolResult } from './tools.js';
export interface GitStatusParams {
    directory?: string;
    porcelain?: boolean;
    show_ignored?: boolean;
    [key: string]: unknown;
}
export interface GitStatusInfo {
    branch: string;
    ahead?: number;
    behind?: number;
    staged: Array<{
        status: string;
        file: string;
    }>;
    unstaged: Array<{
        status: string;
        file: string;
    }>;
    untracked: string[];
    ignored?: string[];
    clean: boolean;
}
export declare class GitStatusTool extends BaseTool<GitStatusParams, ToolResult> {
    private readonly config;
    static Name: string;
    constructor(config: Config);
    validateToolParams(params: GitStatusParams): string | null;
    private executeGitCommand;
    private parseGitStatus;
    private formatStatusOutput;
    private getStatusIcon;
    execute(params: GitStatusParams, abortSignal: AbortSignal): Promise<ToolResult>;
}
//# sourceMappingURL=git-status.d.ts.map