/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect, useRef } from 'react';
import { Box, useStdout } from 'ink';
import { ThemeManager } from '../../themes/themes.js';

export interface MaxBoxSizedProps {
  children: React.ReactNode;
  themeManager?: ThemeManager;
  maxWidth?: number | string;
  maxHeight?: number | string;
  minWidth?: number | string;
  minHeight?: number | string;
  width?: number | string;
  height?: number | string;
  overflowX?: 'visible' | 'hidden';
  overflowY?: 'visible' | 'hidden';
  overflow?: 'visible' | 'hidden';
  responsive?: boolean;
  responsiveBreakpoints?: {
    small?: number;
    medium?: number;
    large?: number;
  };
  autoResize?: boolean;
  maintainAspectRatio?: boolean;
  aspectRatio?: number;
  padding?: number;
  margin?: number;
  marginX?: number;
  marginY?: number;
  marginTop?: number;
  marginBottom?: number;
  marginLeft?: number;
  marginRight?: number;
  paddingX?: number;
  paddingY?: number;
  paddingTop?: number;
  paddingBottom?: number;
  paddingLeft?: number;
  paddingRight?: number;
  borderStyle?: 'single' | 'double' | 'round' | 'bold' | 'singleDouble' | 'doubleSingle' | 'classic';
  borderColor?: string;
  flexDirection?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  flexWrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
  flexGrow?: number;
  flexShrink?: number;
  flexBasis?: number | string;
  alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
  alignSelf?: 'auto' | 'flex-start' | 'center' | 'flex-end';
  justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  position?: 'relative' | 'absolute';
  top?: number;
  right?: number;
  bottom?: number;
  left?: number;
  onResize?: (dimensions: { width: number; height: number }) => void;
  onOverflow?: (overflow: { x: boolean; y: boolean }) => void;
  debug?: boolean;
}

export const MaxBoxSized: React.FC<MaxBoxSizedProps> = ({
  children,
  themeManager,
  maxWidth,
  maxHeight,
  minWidth,
  minHeight,
  width,
  height,
  overflowX,
  overflowY,
  overflow,
  responsive = true,
  responsiveBreakpoints = {
    small: 40,
    medium: 80,
    large: 120,
  },
  autoResize = true,
  maintainAspectRatio = false,
  aspectRatio,
  onResize,
  onOverflow,
  debug = false,
  ...boxProps
}) => {
  const { stdout } = useStdout();
  const [computedDimensions, setComputedDimensions] = useState<{
    width?: number | string;
    height?: number | string;
  }>({});
  const [isOverflowing, setIsOverflowing] = useState({ x: false, y: false });
  const containerRef = useRef<any>(null);

  // Calculate responsive dimensions based on terminal size
  const calculateResponsiveDimensions = () => {
    if (!responsive || !stdout) return { width, height };

    const terminalWidth = stdout.columns || 80;
    const terminalHeight = stdout.rows || 24;

    let calculatedWidth = width;
    let calculatedHeight = height;

    // Apply responsive breakpoints
    if (typeof maxWidth === 'string' && maxWidth.includes('%')) {
      const percentage = parseInt(maxWidth.replace('%', '')) / 100;
      calculatedWidth = Math.floor(terminalWidth * percentage);
    } else if (typeof maxWidth === 'number') {
      calculatedWidth = Math.min(maxWidth, terminalWidth);
    }

    if (typeof maxHeight === 'string' && maxHeight.includes('%')) {
      const percentage = parseInt(maxHeight.replace('%', '')) / 100;
      calculatedHeight = Math.floor(terminalHeight * percentage);
    } else if (typeof maxHeight === 'number') {
      calculatedHeight = Math.min(maxHeight, terminalHeight);
    }

    // Apply minimum constraints
    if (typeof minWidth === 'number' && typeof calculatedWidth === 'number') {
      calculatedWidth = Math.max(minWidth, calculatedWidth);
    }

    if (typeof minHeight === 'number' && typeof calculatedHeight === 'number') {
      calculatedHeight = Math.max(minHeight, calculatedHeight);
    }

    // Maintain aspect ratio if specified
    if (maintainAspectRatio && aspectRatio && calculatedWidth && calculatedHeight) {
      const currentRatio = (calculatedWidth as number) / (calculatedHeight as number);
      if (currentRatio > aspectRatio) {
        calculatedWidth = Math.floor((calculatedHeight as number) * aspectRatio);
      } else {
        calculatedHeight = Math.floor((calculatedWidth as number) / aspectRatio);
      }
    }

    return {
      width: calculatedWidth,
      height: calculatedHeight,
    };
  };

  // Update dimensions when terminal size changes or props change
  useEffect(() => {
    if (autoResize) {
      const newDimensions = calculateResponsiveDimensions();
      setComputedDimensions(newDimensions);

      if (onResize && newDimensions.width && newDimensions.height) {
        onResize({
          width: typeof newDimensions.width === 'number' ? newDimensions.width : 0,
          height: typeof newDimensions.height === 'number' ? newDimensions.height : 0,
        });
      }
    }
  }, [
    stdout?.columns,
    stdout?.rows,
    width,
    height,
    maxWidth,
    maxHeight,
    minWidth,
    minHeight,
    responsive,
    autoResize,
    maintainAspectRatio,
    aspectRatio,
  ]);

  // Check for overflow conditions
  useEffect(() => {
    const checkOverflow = () => {
      const terminalWidth = stdout?.columns || 80;
      const terminalHeight = stdout?.rows || 24;

      const effectiveWidth = computedDimensions.width || width;
      const effectiveHeight = computedDimensions.height || height;

      const xOverflow = typeof effectiveWidth === 'number' && effectiveWidth > terminalWidth;
      const yOverflow = typeof effectiveHeight === 'number' && effectiveHeight > terminalHeight;

      const newOverflow = { x: xOverflow, y: yOverflow };
      
      if (newOverflow.x !== isOverflowing.x || newOverflow.y !== isOverflowing.y) {
        setIsOverflowing(newOverflow);
        if (onOverflow) {
          onOverflow(newOverflow);
        }
      }
    };

    checkOverflow();
  }, [computedDimensions, width, height, stdout?.columns, stdout?.rows, isOverflowing, onOverflow]);

  // Determine final overflow behavior
  const finalOverflowX = overflowX || overflow || (isOverflowing.x ? 'hidden' : 'visible');
  const finalOverflowY = overflowY || overflow || (isOverflowing.y ? 'hidden' : 'visible');

  // Prepare final box props
  const finalBoxProps = {
    ...boxProps,
    ref: containerRef,
    width: computedDimensions.width || width,
    height: computedDimensions.height || height,
    overflowX: finalOverflowX,
    overflowY: finalOverflowY,
  };

  // Apply max constraints directly to box props
  if (maxWidth && !computedDimensions.width) {
    finalBoxProps.width = maxWidth;
  }
  if (maxHeight && !computedDimensions.height) {
    finalBoxProps.height = maxHeight;
  }

  // Debug information
  const renderDebugInfo = () => {
    if (!debug || !themeManager) return null;

    const terminalSize = `${stdout?.columns || 'unknown'}x${stdout?.rows || 'unknown'}`;
    const computedSize = `${computedDimensions.width || 'auto'}x${computedDimensions.height || 'auto'}`;
    const overflowStatus = `X:${isOverflowing.x ? 'overflow' : 'ok'} Y:${isOverflowing.y ? 'overflow' : 'ok'}`;

    return (
      <Box
        borderStyle="single"
        borderColor="yellow"
        padding={1}
      >
        <Box flexDirection="column">
          <Box>{themeManager.muted(`Terminal: ${terminalSize}`)}</Box>
          <Box>{themeManager.muted(`Computed: ${computedSize}`)}</Box>
          <Box>{themeManager.muted(`Overflow: ${overflowStatus}`)}</Box>
        </Box>
      </Box>
    );
  };

  return (
    <Box {...finalBoxProps}>
      {children}
      {renderDebugInfo()}
    </Box>
  );
};

// Convenience components for common use cases
export const ResponsiveMaxBox: React.FC<Omit<MaxBoxSizedProps, 'responsive'>> = (props) => (
  <MaxBoxSized {...props} responsive={true} />
);

export const FixedMaxBox: React.FC<Omit<MaxBoxSizedProps, 'responsive' | 'autoResize'>> = (props) => (
  <MaxBoxSized {...props} responsive={false} autoResize={false} />
);

export const AspectRatioBox: React.FC<Omit<MaxBoxSizedProps, 'maintainAspectRatio'> & { aspectRatio: number }> = (props) => (
  <MaxBoxSized {...props} maintainAspectRatio={true} />
);
