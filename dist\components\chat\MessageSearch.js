import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
export const MessageSearch = ({ themeManager, messages, isOpen, onClose, onSelectMessage, }) => {
    const [searchQuery, setSearchQuery] = useState('');
    const [searchResults, setSearchResults] = useState([]);
    const [selectedResultIndex, setSelectedResultIndex] = useState(0);
    const [searchMode, setSearchMode] = useState('content');
    useInput((input, key) => {
        if (!isOpen)
            return;
        if (key.escape) {
            onClose();
            return;
        }
        if (key.return && searchResults.length > 0) {
            const selectedResult = searchResults[selectedResultIndex];
            onSelectMessage?.(selectedResult.messageIndex);
            onClose();
            return;
        }
        if (key.upArrow && selectedResultIndex > 0) {
            setSelectedResultIndex(selectedResultIndex - 1);
            return;
        }
        if (key.downArrow && selectedResultIndex < searchResults.length - 1) {
            setSelectedResultIndex(selectedResultIndex + 1);
            return;
        }
        if (key.tab) {
            // Cycle through search modes
            const modes = ['content', 'role', 'date'];
            const currentIndex = modes.indexOf(searchMode);
            const nextIndex = (currentIndex + 1) % modes.length;
            setSearchMode(modes[nextIndex]);
            return;
        }
        if (key.backspace || key.delete) {
            setSearchQuery(prev => prev.slice(0, -1));
            return;
        }
        if (input && input.length === 1 && !key.ctrl && !key.meta) {
            setSearchQuery(prev => prev + input);
        }
    });
    useEffect(() => {
        if (!searchQuery.trim()) {
            setSearchResults([]);
            setSelectedResultIndex(0);
            return;
        }
        const results = [];
        const query = searchQuery.toLowerCase();
        messages.forEach((message, index) => {
            let isMatch = false;
            let matchedText = '';
            let contextBefore = '';
            let contextAfter = '';
            switch (searchMode) {
                case 'content':
                    const content = message.content.toLowerCase();
                    const matchIndex = content.indexOf(query);
                    if (matchIndex !== -1) {
                        isMatch = true;
                        const start = Math.max(0, matchIndex - 20);
                        const end = Math.min(content.length, matchIndex + query.length + 20);
                        contextBefore = message.content.substring(start, matchIndex);
                        matchedText = message.content.substring(matchIndex, matchIndex + query.length);
                        contextAfter = message.content.substring(matchIndex + query.length, end);
                    }
                    break;
                case 'role':
                    if (message.role.toLowerCase().includes(query)) {
                        isMatch = true;
                        matchedText = message.role;
                        contextBefore = '';
                        contextAfter = message.content.substring(0, 50) + (message.content.length > 50 ? '...' : '');
                    }
                    break;
                case 'date':
                    const messageDate = new Date(message.timestamp).toLocaleDateString().toLowerCase();
                    const messageTime = new Date(message.timestamp).toLocaleTimeString().toLowerCase();
                    if (messageDate.includes(query) || messageTime.includes(query)) {
                        isMatch = true;
                        matchedText = new Date(message.timestamp).toLocaleString();
                        contextBefore = '';
                        contextAfter = message.content.substring(0, 50) + (message.content.length > 50 ? '...' : '');
                    }
                    break;
            }
            if (isMatch) {
                results.push({
                    messageIndex: index,
                    message,
                    matchedText,
                    contextBefore,
                    contextAfter,
                });
            }
        });
        setSearchResults(results);
        setSelectedResultIndex(0);
    }, [searchQuery, messages, searchMode]);
    if (!isOpen)
        return null;
    const renderSearchHeader = () => (_jsx(Box, { borderStyle: "round", borderColor: "blue", padding: 1, children: _jsxs(Box, { flexDirection: "column", width: "100%", children: [_jsx(Text, { children: themeManager.primary('🔍 Message Search') }), _jsxs(Box, { marginTop: 1, children: [_jsx(Text, { children: themeManager.secondary('Mode: ') }), _jsx(Text, { children: themeManager.accent(searchMode) }), _jsx(Text, { children: themeManager.muted(' (Tab to cycle)') })] }), _jsxs(Box, { marginTop: 1, children: [_jsx(Text, { children: themeManager.secondary('Query: ') }), _jsx(Text, { children: themeManager.primary(searchQuery || '...') })] }), _jsxs(Box, { marginTop: 1, children: [_jsx(Text, { children: themeManager.muted('Results: ') }), _jsx(Text, { children: themeManager.info(searchResults.length.toString()) }), searchResults.length > 0 && (_jsxs(_Fragment, { children: [_jsx(Text, { children: themeManager.muted(' | Selected: ') }), _jsx(Text, { children: themeManager.accent(`${selectedResultIndex + 1}/${searchResults.length}`) })] }))] })] }) }));
    const renderSearchResults = () => {
        if (searchResults.length === 0) {
            return (_jsx(Box, { marginTop: 1, justifyContent: "center", children: _jsx(Text, { children: themeManager.muted('No results found') }) }));
        }
        return (_jsxs(Box, { flexDirection: "column", marginTop: 1, children: [searchResults.slice(0, 5).map((result, index) => {
                    const isSelected = index === selectedResultIndex;
                    const messageDate = new Date(result.message.timestamp).toLocaleString();
                    return (_jsx(Box, { borderStyle: isSelected ? "single" : undefined, borderColor: isSelected ? "yellow" : undefined, padding: 1, marginBottom: 1, children: _jsxs(Box, { flexDirection: "column", width: "100%", children: [_jsxs(Box, { children: [_jsx(Text, { children: isSelected ? themeManager.highlight(' ▶ ') : '   ' }), _jsx(Text, { children: themeManager.secondary(`${result.message.role}: `) }), _jsx(Text, { children: themeManager.muted(`[${messageDate}]`) })] }), _jsx(Box, { marginTop: 1, marginLeft: 3, children: searchMode === 'content' ? (_jsxs(Text, { children: [themeManager.muted(result.contextBefore), themeManager.highlight(result.matchedText), themeManager.muted(result.contextAfter)] })) : (_jsxs(Text, { children: [themeManager.highlight(result.matchedText), result.contextAfter && (_jsxs(_Fragment, { children: [themeManager.muted(' - '), themeManager.muted(result.contextAfter)] }))] })) })] }) }, index));
                }), searchResults.length > 5 && (_jsx(Box, { justifyContent: "center", children: _jsx(Text, { children: themeManager.muted(`... and ${searchResults.length - 5} more results`) }) }))] }));
    };
    const renderControls = () => (_jsx(Box, { marginTop: 1, borderStyle: "single", borderColor: "gray", padding: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.muted('Controls:') }), _jsx(Text, { children: themeManager.muted('  Type to search • ↑↓ Navigate • Enter Select • Tab Change mode • Esc Close') })] }) }));
    return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [renderSearchHeader(), renderSearchResults(), renderControls()] }));
};
//# sourceMappingURL=MessageSearch.js.map