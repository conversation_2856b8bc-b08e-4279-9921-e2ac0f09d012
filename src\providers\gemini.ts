/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { FunctionDeclaration, GenerateContentResponse } from '@google/genai';
import { BaseLLMProvider, LLMMessage, LLMResponse, LLMRequestOptions, ProviderConfig } from './base.js';
import { GeminiClient } from '../core/client.js';
import { makeGeminiRequest, makeGeminiStreamRequest } from '../core/geminiRequest.js';
import { getResponseText, getFunctionCalls } from '../utils/generateContentResponseUtilities.js';
import { Config, ApprovalMode } from '../config/config.js';

export class GeminiProvider extends BaseLLMProvider {
  private client: GeminiClient;

  constructor(config: ProviderConfig) {
    super('Google Gemini', config);
    this.validateConfig();
    
    // Create a temporary config for the client
    const clientConfig = new Config({
      cwd: process.cwd(),
      model: config.model,
      embeddingModel: 'text-embedding-004',
      sandbox: false,
      targetDir: process.cwd(),
      debugMode: false,
      userMemory: '',
      arienMdFileCount: 50,
      approvalMode: ApprovalMode.DEFAULT,
      sessionId: 'cli-session',
      apiKey: config.apiKey,
      provider: 'google',
      temperature: config.temperature,
      maxTokens: config.maxTokens,
    });

    this.client = new GeminiClient(clientConfig);
  }

  async generateResponse(
    message: string,
    options: LLMRequestOptions = {}
  ): Promise<LLMResponse> {
    const response = await makeGeminiRequest(this.client, message, {
      systemPrompt: options.systemPrompt,
      tools: options.tools,
      temperature: options.temperature || this.config.temperature,
      maxTokens: options.maxTokens || this.config.maxTokens,
    });

    return {
      text: response.text,
      functionCalls: response.functionCalls,
      usage: response.usage,
    };
  }

  async generateConversationResponse(
    messages: LLMMessage[],
    options: LLMRequestOptions = {}
  ): Promise<LLMResponse> {
    // For conversation, we'll use the last message as the main prompt
    // and include previous messages as context
    const lastMessage = messages[messages.length - 1];
    const context = messages.slice(0, -1)
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');

    const fullMessage = context ? `${context}\n\n${lastMessage.content}` : lastMessage.content;

    return this.generateResponse(fullMessage, options);
  }

  async* generateStreamingResponse(
    message: string,
    options: LLMRequestOptions = {}
  ): AsyncGenerator<string, LLMResponse, unknown> {
    const generator = makeGeminiStreamRequest(this.client, message, {
      systemPrompt: options.systemPrompt,
      tools: options.tools,
      temperature: options.temperature || this.config.temperature,
      maxTokens: options.maxTokens || this.config.maxTokens,
    });

    let finalResponse: LLMResponse | undefined;

    for await (const chunk of generator) {
      if (typeof chunk === 'string') {
        yield chunk;
      } else {
        const response = chunk as GenerateContentResponse;
        const text = getResponseText(response);
        const functionCalls = getFunctionCalls(response);

        finalResponse = {
          text,
          functionCalls,
          usage: response.usageMetadata ? {
            promptTokens: response.usageMetadata.promptTokenCount || 0,
            completionTokens: response.usageMetadata.candidatesTokenCount || 0,
            totalTokens: response.usageMetadata.totalTokenCount || 0,
          } : undefined,
        };
      }
    }

    return finalResponse || {
      text: '',
      functionCalls: [],
    };
  }

  getAvailableModels(): string[] {
    return [
      'gemini-1.5-pro',
      'gemini-1.5-flash',
      'gemini-1.0-pro',
      'gemini-pro-vision',
    ];
  }

  protected convertTools(tools: FunctionDeclaration[]): FunctionDeclaration[] {
    // Gemini uses the same format as our base format
    return tools;
  }

  protected convertResponse(response: unknown): LLMResponse {
    // This is handled by the makeGeminiRequest function
    return response as LLMResponse;
  }
}
