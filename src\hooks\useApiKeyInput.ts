/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { useCallback } from 'react';
import { useInput, UseInputOptions, UseInputReturn } from './useInput.js';
import { ProviderType } from '../providers/manager.js';

export interface UseApiKeyInputOptions extends Omit<UseInputOptions, 'validator' | 'characterFilter' | 'masked'> {
  provider: ProviderType;
  onValidationChange?: (isValid: boolean) => void;
}

export interface UseApiKeyInputReturn extends UseInputReturn {
  isValidFormat: boolean;
  validationMessage: string;
}

/**
 * Validates API key format for different providers
 */
const validateApiKeyFormat = (provider: ProviderType, apiKey: string): boolean => {
  switch (provider) {
    case 'google':
      return apiKey.startsWith('AIza') && apiKey.length === 39;
    case 'openai':
      return apiKey.startsWith('sk-') && apiKey.length >= 48;
    case 'deepseek':
      return apiKey.startsWith('sk-') && apiKey.length >= 48;
    case 'anthropic':
      return apiKey.startsWith('sk-ant-') && apiKey.length >= 50;
    default:
      return apiKey.length > 0;
  }
};

/**
 * Gets validation message for API key format
 */
const getValidationMessage = (provider: ProviderType, apiKey: string): string => {
  if (!apiKey) {
    return '';
  }

  switch (provider) {
    case 'google':
      if (!apiKey.startsWith('AIza')) {
        return 'Google API keys should start with "AIza"';
      }
      if (apiKey.length !== 39) {
        return `Google API keys should be 39 characters long (current: ${apiKey.length})`;
      }
      return '';
    case 'openai':
      if (!apiKey.startsWith('sk-')) {
        return 'OpenAI API keys should start with "sk-"';
      }
      if (apiKey.length < 48) {
        return `OpenAI API keys should be at least 48 characters long (current: ${apiKey.length})`;
      }
      return '';
    case 'deepseek':
      if (!apiKey.startsWith('sk-')) {
        return 'DeepSeek API keys should start with "sk-"';
      }
      if (apiKey.length < 48) {
        return `DeepSeek API keys should be at least 48 characters long (current: ${apiKey.length})`;
      }
      return '';
    case 'anthropic':
      if (!apiKey.startsWith('sk-ant-')) {
        return 'Anthropic API keys should start with "sk-ant-"';
      }
      if (apiKey.length < 50) {
        return `Anthropic API keys should be at least 50 characters long (current: ${apiKey.length})`;
      }
      return '';
    default:
      return '';
  }
};

/**
 * Character filter for API keys (alphanumeric, hyphens, underscores)
 */
const apiKeyCharacterFilter = (char: string): boolean => {
  const charCode = char.charCodeAt(0);
  return (
    (charCode >= 48 && charCode <= 57) || // 0-9
    (charCode >= 65 && charCode <= 90) || // A-Z
    (charCode >= 97 && charCode <= 122) || // a-z
    charCode === 45 || // -
    charCode === 95    // _
  );
};

/**
 * Hook for API key input with validation and masking
 */
export const useApiKeyInput = ({
  provider,
  onValidationChange,
  onPaste,
  ...options
}: UseApiKeyInputOptions): UseApiKeyInputReturn => {
  const handlePaste = useCallback((pastedText: string) => {
    // Call the original paste handler if provided
    if (onPaste) {
      onPaste(pastedText);
    }
  }, [onPaste]);

  const inputResult = useInput({
    ...options,
    masked: true,
    maskChar: '*',
    characterFilter: apiKeyCharacterFilter,
    maxLength: 100, // Reasonable max length for API keys
    onPaste: handlePaste,
  });

  const isValidFormat = validateApiKeyFormat(provider, inputResult.input);
  const validationMessage = getValidationMessage(provider, inputResult.input);

  // Call validation change callback when validation state changes
  if (onValidationChange) {
    onValidationChange(isValidFormat);
  }

  return {
    ...inputResult,
    isValidFormat,
    validationMessage,
  };
};
