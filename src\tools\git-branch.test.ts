/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { GitBranchTool, GitBranchParams } from './git-branch.js';
import { Config, ApprovalMode, ConfigParameters } from '../config/config.js';
import fs from 'fs';
import { spawn } from 'child_process';

// Mock fs
vi.mock('fs');
const mockFs = vi.mocked(fs);

// Mock child_process
vi.mock('child_process');
const mockSpawn = vi.mocked(spawn);

// Mock config
const mockConfigParams: ConfigParameters = {
  cwd: '/test/project',
  model: 'test-model',
  embeddingModel: 'test-embedding',
  sandbox: false,
  targetDir: '/test/project',
  debugMode: false,
  userMemory: '',
  arienMdFileCount: 50,
  approvalMode: ApprovalMode.DEFAULT,
  sessionId: 'test-session',
  fileFilteringRespectGitIgnore: true,
  fullContext: false,
};

describe('GitBranchTool', () => {
  let gitBranchTool: GitBranchTool;
  let mockConfig: Config;
  let mockProcess: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockConfig = new Config(mockConfigParams);
    gitBranchTool = new GitBranchTool(mockConfig);

    // Mock process object
    mockProcess = {
      stdout: {
        on: vi.fn(),
      },
      stderr: {
        on: vi.fn(),
      },
      on: vi.fn(),
    };

    mockSpawn.mockReturnValue(mockProcess as any);
    mockFs.existsSync.mockReturnValue(true);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('constructor', () => {
    it('should create GitBranchTool with correct properties', () => {
      expect(gitBranchTool.name).toBe('git_branch');
      expect(gitBranchTool.displayName).toBe('Git Branch');
      expect(gitBranchTool.description).toContain('Manages git branches');
      expect(gitBranchTool.isOutputMarkdown).toBe(true);
      expect(gitBranchTool.canUpdateOutput).toBe(false);
    });

    it('should have correct schema structure', () => {
      const schema = gitBranchTool.schema;
      expect(schema.name).toBe('git_branch');
      expect(schema.parameters).toBeDefined();
      expect(schema.parameters.properties).toHaveProperty('action');
      expect(schema.parameters.properties).toHaveProperty('branch_name');
      expect(schema.parameters.required).toContain('action');
    });
  });

  describe('validateToolParams', () => {
    it('should validate list action without branch name', () => {
      const params: GitBranchParams = {
        action: 'list',
      };

      const result = gitBranchTool.validateToolParams(params);
      expect(result).toBeNull();
    });

    it('should require branch name for non-list actions', () => {
      const params: GitBranchParams = {
        action: 'create',
      };

      const result = gitBranchTool.validateToolParams(params);
      expect(result).toContain('Branch name is required for create action');
    });

    it('should validate branch name format', () => {
      const params: GitBranchParams = {
        action: 'create',
        branch_name: 'invalid..name',
      };

      const result = gitBranchTool.validateToolParams(params);
      expect(result).toContain('Invalid branch name format');
    });

    it('should reject branch names with spaces', () => {
      const params: GitBranchParams = {
        action: 'create',
        branch_name: 'invalid name',
      };

      const result = gitBranchTool.validateToolParams(params);
      expect(result).toContain('Invalid branch name format');
    });

    it('should reject branch names starting with dash', () => {
      const params: GitBranchParams = {
        action: 'create',
        branch_name: '-invalid',
      };

      const result = gitBranchTool.validateToolParams(params);
      expect(result).toContain('Invalid branch name format');
    });

    it('should accept valid branch names', () => {
      const params: GitBranchParams = {
        action: 'create',
        branch_name: 'feature/new-feature',
      };

      const result = gitBranchTool.validateToolParams(params);
      expect(result).toBeNull();
    });
  });

  describe('execute - list action', () => {
    it('should list local branches', async () => {
      const params: GitBranchParams = { action: 'list' };
      const abortSignal = new AbortController().signal;

      // Mock git commands
      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 0);
        }
      });

      let callCount = 0;
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callCount++;
          if (callCount === 1) {
            // rev-parse output
            setTimeout(() => callback(''), 0);
          } else {
            // branch list output
            const branchOutput = `* main
  feature/branch1
  feature/branch2`;
            setTimeout(() => callback(branchOutput), 0);
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback(''), 0);
        }
      });

      const result = await gitBranchTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('# Git Branches');
      expect(result.llmContent).toContain('Local Branches');
      expect(result.llmContent).toContain('**main** (current)');
      expect(result.llmContent).toContain('feature/branch1');
      expect(result.llmContent).toContain('feature/branch2');
      expect(result.returnDisplay).toContain('Listed git branches');
    });

    it('should list remote branches', async () => {
      const params: GitBranchParams = { action: 'list', remote: true };
      const abortSignal = new AbortController().signal;

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 0);
        }
      });

      let callCount = 0;
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callCount++;
          if (callCount === 1) {
            setTimeout(() => callback(''), 0);
          } else {
            const branchOutput = `  remotes/origin/main
  remotes/origin/feature/branch1`;
            setTimeout(() => callback(branchOutput), 0);
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback(''), 0);
        }
      });

      const result = await gitBranchTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('Remote Branches');
      expect(result.llmContent).toContain('origin/main');
      expect(result.llmContent).toContain('origin/feature/branch1');
    });
  });

  describe('execute - create action', () => {
    it('should create a new branch', async () => {
      const params: GitBranchParams = {
        action: 'create',
        branch_name: 'new-feature',
      };
      const abortSignal = new AbortController().signal;

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 0);
        }
      });

      let callCount = 0;
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callCount++;
          if (callCount === 1) {
            setTimeout(() => callback(''), 0);
          } else {
            setTimeout(() => callback('Switched to a new branch \'new-feature\''), 0);
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback(''), 0);
        }
      });

      const result = await gitBranchTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('Branch Created Successfully');
      expect(result.llmContent).toContain('**new-feature**');
      expect(result.returnDisplay).toContain('Created branch: new-feature');
    });

    it('should create branch from source branch', async () => {
      const params: GitBranchParams = {
        action: 'create',
        branch_name: 'new-feature',
        source_branch: 'develop',
      };
      const abortSignal = new AbortController().signal;

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 0);
        }
      });

      let callCount = 0;
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callCount++;
          if (callCount === 1) {
            setTimeout(() => callback(''), 0);
          } else {
            setTimeout(() => callback('Switched to a new branch \'new-feature\''), 0);
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback(''), 0);
        }
      });

      const result = await gitBranchTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('from **develop**');
    });
  });

  describe('execute - delete action', () => {
    it('should delete a branch', async () => {
      const params: GitBranchParams = {
        action: 'delete',
        branch_name: 'old-feature',
      };
      const abortSignal = new AbortController().signal;

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 0);
        }
      });

      let callCount = 0;
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callCount++;
          if (callCount === 1) {
            setTimeout(() => callback(''), 0);
          } else {
            setTimeout(() => callback('Deleted branch old-feature'), 0);
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback(''), 0);
        }
      });

      const result = await gitBranchTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('Branch Deleted Successfully');
      expect(result.llmContent).toContain('**old-feature**');
      expect(result.returnDisplay).toContain('Deleted branch: old-feature');
    });

    it('should handle force delete', async () => {
      const params: GitBranchParams = {
        action: 'delete',
        branch_name: 'unmerged-feature',
        force: true,
      };
      const abortSignal = new AbortController().signal;

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 0);
        }
      });

      let callCount = 0;
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callCount++;
          if (callCount === 1) {
            setTimeout(() => callback(''), 0);
          } else {
            setTimeout(() => callback('Deleted branch unmerged-feature'), 0);
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback(''), 0);
        }
      });

      const result = await gitBranchTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('Force delete was used');
    });
  });

  describe('execute - switch action', () => {
    it('should switch to existing branch', async () => {
      const params: GitBranchParams = {
        action: 'switch',
        branch_name: 'feature-branch',
      };
      const abortSignal = new AbortController().signal;

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 0);
        }
      });

      let callCount = 0;
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callCount++;
          if (callCount === 1) {
            setTimeout(() => callback(''), 0);
          } else {
            setTimeout(() => callback('Switched to branch \'feature-branch\''), 0);
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback(''), 0);
        }
      });

      const result = await gitBranchTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('Switched Branch Successfully');
      expect(result.llmContent).toContain('**feature-branch**');
      expect(result.returnDisplay).toContain('Switched to branch: feature-branch');
    });
  });

  describe('error handling', () => {
    it('should handle not a git repository', async () => {
      const params: GitBranchParams = { action: 'list' };
      const abortSignal = new AbortController().signal;

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(1), 0); // Exit code 1
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback('fatal: not a git repository'), 0);
        }
      });

      const result = await gitBranchTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('Not a git repository');
    });

    it('should handle branch operation failures', async () => {
      const params: GitBranchParams = {
        action: 'create',
        branch_name: 'existing-branch',
      };
      const abortSignal = new AbortController().signal;

      let callCount = 0;
      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          callCount++;
          if (callCount === 1) {
            setTimeout(() => callback(0), 0); // rev-parse succeeds
          } else {
            setTimeout(() => callback(1), 0); // branch operation fails
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback('fatal: A branch named \'existing-branch\' already exists'), 0);
        }
      });

      const result = await gitBranchTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('Failed to create branch');
      expect(result.returnDisplay).toContain('Branch creation error');
    });
  });
});
