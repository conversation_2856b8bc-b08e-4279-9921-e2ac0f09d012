/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import React from 'react';
import { ThemeManager } from '../../themes/themes.js';
import { ChatMessage } from '../../hooks/useChat.js';
interface EnhancedMessageDisplayProps {
    themeManager: ThemeManager;
    messages: ChatMessage[];
    maxHeight?: number;
    maxWidth?: number;
    showTimestamps?: boolean;
    showLineNumbers?: boolean;
    enableSearch?: boolean;
    scrollable?: boolean;
    title?: string;
}
export declare const EnhancedMessageDisplay: React.FC<EnhancedMessageDisplayProps>;
export declare const CompactMessageDisplay: React.FC<Omit<EnhancedMessageDisplayProps, 'showTimestamps' | 'showLineNumbers'>>;
export declare const DetailedMessageDisplay: React.FC<Omit<EnhancedMessageDisplayProps, 'showTimestamps' | 'showLineNumbers' | 'enableSearch'>>;
export declare const LogStyleMessageDisplay: React.FC<Omit<EnhancedMessageDisplayProps, 'showTimestamps' | 'title'>>;
export {};
//# sourceMappingURL=EnhancedMessageDisplay.d.ts.map