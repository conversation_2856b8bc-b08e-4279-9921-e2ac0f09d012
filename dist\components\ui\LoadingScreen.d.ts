/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import React from 'react';
import { ThemeManager } from '../../themes/themes.js';
interface LoadingScreenProps {
    themeManager: ThemeManager;
    status?: string;
    progress?: number;
    showBranding?: boolean;
    showProgress?: boolean;
    showStatus?: boolean;
    onComplete?: () => void;
}
export declare const LoadingScreen: React.FC<LoadingScreenProps>;
export {};
//# sourceMappingURL=LoadingScreen.d.ts.map