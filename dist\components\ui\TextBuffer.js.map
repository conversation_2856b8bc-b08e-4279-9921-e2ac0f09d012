{"version": 3, "file": "TextBuffer.js", "sourceRoot": "", "sources": ["../../../src/components/ui/TextBuffer.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AACpE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AA6C1C,MAAM,CAAC,MAAM,UAAU,GAA8B,CAAC,EACpD,YAAY,EACZ,KAAK,GAAG,EAAE,EACV,OAAO,EACP,QAAQ,GAAG,IAAI,EACf,QAAQ,EACR,SAAS,GAAG,EAAE,EACd,UAAU,GAAG,IAAI,EACjB,eAAe,GAAG,KAAK,EACvB,cAAc,GAAG,KAAK,EACtB,YAAY,GAAG,KAAK,EACpB,eAAe,GAAG,KAAK,EACvB,UAAU,GAAG,IAAI,EACjB,cAAc,GAAG,KAAK,EACtB,gBAAgB,EAChB,aAAa,EACb,iBAAiB,EACjB,QAAQ,EACR,WAAW,EACX,WAAW,EACX,OAAO,GAAG,CAAC,EACX,MAAM,GAAG,CAAC,EACV,KAAK,EACL,MAAM,EACN,YAAY,GAAG,uBAAuB,EACtC,cAAc,GAAG,YAAY,EAC7B,SAAS,GAAG,KAAK,EACjB,QAAQ,GAAG,KAAK,GACjB,EAAE,EAAE;IACH,MAAM,CAAC,YAAY,EAAE,eAAe,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACpD,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAAc,IAAI,GAAG,EAAE,CAAC,CAAC;IAC3E,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;IACjD,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IACpD,MAAM,YAAY,GAAG,MAAM,CAAM,IAAI,CAAC,CAAC;IAEvC,8CAA8C;IAC9C,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,EAAE;QAClC,IAAI,QAAQ,GAAe,EAAE,CAAC;QAE9B,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;gBAC7D,EAAE,EAAE,WAAW,KAAK,EAAE;gBACtB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC,CAAC;YACJ,QAAQ,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;QAC/B,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,QAAQ,GAAG,CAAC,GAAG,QAAQ,EAAE,GAAG,KAAK,CAAC,CAAC;QACrC,CAAC;QAED,kBAAkB;QAClB,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,KAAK,GAAG,OAAO,aAAa,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC;YACjG,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAC/D,CAAC;QAED,yBAAyB;QACzB,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAChD,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QACrE,CAAC;QAED,0BAA0B;QAC1B,IAAI,QAAQ,CAAC,MAAM,GAAG,QAAQ,EAAE,CAAC;YAC/B,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC,CAAC;IAE1D,qDAAqD;IACrD,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,EAAE;QAChC,IAAI,CAAC,UAAU;YAAE,OAAO,cAAc,CAAC;QAEvC,MAAM,UAAU,GAAG,YAAY,CAAC;QAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,EAAE,YAAY,GAAG,SAAS,CAAC,CAAC;QAC3E,OAAO,cAAc,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACpD,CAAC,EAAE,CAAC,cAAc,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;IAE1D,kDAAkD;IAClD,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,UAAU,IAAI,cAAc,EAAE,CAAC;YACjC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC;YACjE,eAAe,CAAC,SAAS,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC,EAAE,CAAC,cAAc,CAAC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC;IAEnE,iDAAiD;IACjD,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,QAAQ;YAAE,OAAO;QAErB,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;gBACf,aAAa,CAAC,KAAK,CAAC,CAAC;gBACrB,aAAa,CAAC,EAAE,CAAC,CAAC;YACpB,CAAC;iBAAM,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;gBACtB,aAAa,CAAC,KAAK,CAAC,CAAC;YACvB,CAAC;iBAAM,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;gBACzB,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC;iBAAM,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;YACtC,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;gBAChB,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC;iBAAM,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;gBACzB,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,GAAG,SAAS,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;YACjF,CAAC;iBAAM,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;gBACtB,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC;YACzD,CAAC;iBAAM,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC;gBACxB,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,GAAG,SAAS,EAAE,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC;YACzF,CAAC;iBAAM,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;gBACzB,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY;YAClC,CAAC;iBAAM,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;gBACzB,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,eAAe;YAClF,CAAC;QACH,CAAC;QAED,IAAI,YAAY,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAClC,aAAa,CAAC,IAAI,CAAC,CAAC;YACpB,aAAa,CAAC,EAAE,CAAC,CAAC;QACpB,CAAC;QAED,wBAAwB;QACxB,IAAI,eAAe,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,EAAE,CAAC;YACpD,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACtC,IAAI,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC;gBACpC,MAAM,WAAW,GAAG,YAAY,GAAG,SAAS,CAAC;gBAC7C,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC;gBAC3C,IAAI,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;oBACjC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;gBAClC,CAAC;qBAAM,CAAC;oBACN,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAC/B,CAAC;gBACD,gBAAgB,CAAC,WAAW,CAAC,CAAC;gBAE9B,IAAI,iBAAiB,EAAE,CAAC;oBACtB,MAAM,mBAAmB,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;oBACxF,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;gBACzC,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,wBAAwB;IACxB,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC;gBACP,GAAG,EAAE,YAAY;gBACjB,MAAM,EAAE,YAAY,GAAG,YAAY,CAAC,MAAM;aAC3C,CAAC,CAAC;QACL,CAAC;IACH,CAAC,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;IAElD,MAAM,eAAe,GAAG,CAAC,SAAkB,EAAE,EAAE;QAC7C,IAAI,CAAC,SAAS,IAAI,CAAC,cAAc;YAAE,OAAO,EAAE,CAAC;QAC7C,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;QACjC,OAAO,IAAI,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC;IAC3C,CAAC,CAAC;IAEF,MAAM,gBAAgB,GAAG,CAAC,KAAa,EAAE,EAAE;QACzC,IAAI,CAAC,eAAe;YAAE,OAAO,EAAE,CAAC;QAChC,MAAM,OAAO,GAAG,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACvE,OAAO,GAAG,OAAO,IAAI,CAAC;IACxB,CAAC,CAAC;IAEF,MAAM,iBAAiB,GAAG,CAAC,OAAe,EAAE,EAAE;QAC5C,IAAI,CAAC,gBAAgB;YAAE,OAAO,OAAO,CAAC;QAEtC,2FAA2F;QAC3F,+EAA+E;QAC/E,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,CAAC,IAAc,EAAE,UAAmB,EAAE,EAAE;QAC3D,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,YAAY,CAAC,SAAS,CAAC;QAChC,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC;QAED,OAAO,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC;IAChC,CAAC,CAAC;IAEF,MAAM,UAAU,GAAG,CAAC,IAAc,EAAE,KAAa,EAAE,EAAE;QACnD,MAAM,WAAW,GAAG,YAAY,GAAG,KAAK,CAAC;QACzC,MAAM,UAAU,GAAG,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAClD,MAAM,aAAa,GAAG,YAAY,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAErD,MAAM,SAAS,GAAG,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAClD,MAAM,UAAU,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC3C,MAAM,kBAAkB,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE3D,IAAI,aAAa,GAAG,kBAAkB,CAAC;QACvC,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;YAC1B,aAAa,GAAG,kBAAkB,CAAC,CAAC,yCAAyC;QAC/E,CAAC;QAED,MAAM,WAAW,GAAG,GAAG,SAAS,GAAG,UAAU,GAAG,aAAa,EAAE,CAAC;QAEhE,uFAAuF;QACvF,OAAO,CACL,KAAC,GAAG,cACF,KAAC,IAAI,cAAE,aAAa,CAAC,WAAW,CAAC,GAAQ,IADjC,IAAI,CAAC,EAAE,CAEX,CACP,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,GAAG,EAAE;QACvB,IAAI,CAAC,KAAK;YAAE,OAAO,IAAI,CAAC;QACxB,OAAO,CACL,KAAC,GAAG,IAAC,YAAY,EAAE,CAAC,YAClB,KAAC,IAAI,cAAE,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,GAAQ,GACtC,CACP,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,GAAG,EAAE;QACxB,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU;YAAE,OAAO,IAAI,CAAC;QAExC,MAAM,UAAU,GAAG,UAAU,CAAC,CAAC;YAC7B,KAAK,YAAY,GAAG,CAAC,IAAI,YAAY,GAAG,YAAY,CAAC,MAAM,OAAO,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAClG,MAAM,UAAU,GAAG,MAAM,IAAI,QAAQ,UAAU,EAAE,CAAC;QAElD,OAAO,CACL,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,cAAE,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC,GAAQ,GACzC,CACP,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,IAAI,CAAC,UAAU;YAAE,OAAO,IAAI,CAAC;QAE7B,OAAO,CACL,MAAC,GAAG,IAAC,YAAY,EAAE,CAAC,aAClB,KAAC,IAAI,cAAE,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,GAAQ,EAC/C,KAAC,IAAI,cAAE,UAAU,GAAQ,EACzB,KAAC,IAAI,cAAE,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,GAAQ,IACnC,CACP,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM,sBAAsB,GAAG,GAAG,EAAE;QAClC,IAAI,CAAC,UAAU,IAAI,cAAc,CAAC,MAAM,IAAI,SAAS;YAAE,OAAO,IAAI,CAAC;QAEnE,MAAM,OAAO,GAAG,YAAY,GAAG,SAAS,GAAG,cAAc,CAAC,MAAM,CAAC;QACjE,MAAM,WAAW,GAAG,YAAY,GAAG,CAAC,CAAC;QAErC,OAAO,CACL,MAAC,GAAG,IAAC,cAAc,EAAC,eAAe,aACjC,KAAC,IAAI,cAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,GAAQ,EACpE,KAAC,IAAI,cAAE,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,GAAQ,IAC5D,CACP,CAAC;IACJ,CAAC,CAAC;IAEF,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,aACzD,WAAW,EAAE,EACd,KAAC,IAAI,cAAE,YAAY,CAAC,KAAK,CAAC,cAAc,CAAC,GAAQ,IAC7C,CACP,CAAC;IACJ,CAAC;IAED,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAChC,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,aACzD,WAAW,EAAE,EACd,KAAC,IAAI,cAAE,YAAY,CAAC,KAAK,CAAC,YAAY,CAAC,GAAQ,IAC3C,CACP,CAAC;IACJ,CAAC;IAED,OAAO,CACL,MAAC,GAAG,IACF,GAAG,EAAE,YAAY,EACjB,aAAa,EAAC,QAAQ,EACtB,KAAK,EAAE,QAAQ,EACf,MAAM,EAAE,SAAS,EACjB,WAAW,EAAE,WAAW,EACxB,WAAW,EAAE,WAAW,EACxB,OAAO,EAAE,OAAO,EAChB,MAAM,EAAE,MAAM,aAEb,WAAW,EAAE,EACb,eAAe,EAAE,EACjB,sBAAsB,EAAE,EACzB,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,YACpC,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,GACvD,EACL,sBAAsB,EAAE,EACxB,YAAY,EAAE,IACX,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,8CAA8C;AAC9C,MAAM,CAAC,MAAM,cAAc,GAAmE,CAAC,KAAK,EAAE,EAAE,CAAC,CACvG,KAAC,UAAU,OAAK,KAAK,EAAE,OAAO,EAAC,MAAM,EAAC,eAAe,EAAE,IAAI,GAAI,CAChE,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAkE,CAAC,KAAK,EAAE,EAAE,CAAC,CACrG,KAAC,UAAU,OAAK,KAAK,EAAE,OAAO,EAAC,UAAU,EAAC,cAAc,EAAE,IAAI,GAAI,CACnE,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAA+C,CAAC,KAAK,EAAE,EAAE,CAAC,CACtF,KAAC,UAAU,OAAK,KAAK,EAAE,OAAO,EAAC,SAAS,GAAG,CAC5C,CAAC;AAEF,MAAM,CAAC,MAAM,oBAAoB,GAAkD,CAAC,KAAK,EAAE,EAAE,CAAC,CAC5F,KAAC,UAAU,OAAK,KAAK,EAAE,UAAU,EAAE,IAAI,GAAI,CAC5C,CAAC"}