/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import fs from 'fs';
import path from 'path';
import { BaseTool } from './tools.js';
import { SchemaValidator } from '../utils/schemaValidator.js';
import { getErrorMessage } from '../utils/errors.js';
import { spawn } from 'child_process';
export class GitDiffTool extends BaseTool {
    config;
    static Name = 'git_diff';
    constructor(config) {
        super(GitDiffTool.Name, 'Git Diff', `Shows differences between commits, commit and working tree, etc. Can display:
- Working directory changes vs staged area
- Staged changes vs last commit
- Differences between specific commits
- File-specific differences
- Statistical summary of changes

Supports various diff formats including unified diff, word-level diff, and statistical summaries.`, {
            type: 'object',
            properties: {
                directory: {
                    type: 'string',
                    description: '(OPTIONAL) Directory to run git diff in. Must be relative to the project root directory. Defaults to project root.',
                },
                file_path: {
                    type: 'string',
                    description: '(OPTIONAL) Specific file path to show diff for. Can be relative to the git repository root.',
                },
                staged: {
                    type: 'boolean',
                    description: '(OPTIONAL) Show staged changes (--cached). If false, shows unstaged changes. Defaults to false.',
                },
                commit_range: {
                    type: 'string',
                    description: '(OPTIONAL) Commit range to compare (e.g., "HEAD~1", "main..feature", "abc123..def456"). If not provided, compares working directory or staged area.',
                },
                context_lines: {
                    type: 'number',
                    description: '(OPTIONAL) Number of context lines to show around changes. Defaults to 3.',
                    minimum: 0,
                    maximum: 50,
                },
                word_diff: {
                    type: 'boolean',
                    description: '(OPTIONAL) Show word-level differences instead of line-level. Useful for text files. Defaults to false.',
                },
                stat_only: {
                    type: 'boolean',
                    description: '(OPTIONAL) Show only statistics (files changed, insertions, deletions) without the actual diff. Defaults to false.',
                },
            },
            required: [],
        }, true, // output is markdown
        false);
        this.config = config;
    }
    validateToolParams(params) {
        const validation = SchemaValidator.validate(params, this.parameterSchema);
        if (!validation.isValid) {
            return `Parameters failed schema validation: ${validation.errors.join(', ')}`;
        }
        if (params.directory) {
            if (path.isAbsolute(params.directory)) {
                return 'Directory cannot be absolute. Must be relative to the project root directory.';
            }
            const directory = path.resolve(this.config.getTargetDir(), params.directory);
            if (!fs.existsSync(directory)) {
                return 'Directory must exist.';
            }
        }
        if (params.context_lines !== undefined) {
            if (params.context_lines < 0 || params.context_lines > 50) {
                return 'Context lines must be between 0 and 50.';
            }
        }
        return null;
    }
    async executeGitCommand(args, cwd) {
        return new Promise((resolve) => {
            const process = spawn('git', args, {
                cwd,
                stdio: ['ignore', 'pipe', 'pipe'],
            });
            let stdout = '';
            let stderr = '';
            process.stdout?.on('data', (data) => {
                stdout += data.toString();
            });
            process.stderr?.on('data', (data) => {
                stderr += data.toString();
            });
            process.on('close', (exitCode) => {
                resolve({ stdout, stderr, exitCode: exitCode || 0 });
            });
        });
    }
    formatDiffOutput(diffOutput, params) {
        if (!diffOutput.trim()) {
            return '✅ **No differences found**\n\nThe compared states are identical.';
        }
        if (params.stat_only) {
            return this.formatStatOutput(diffOutput);
        }
        let output = '# Git Diff\n\n';
        // Add context information
        if (params.commit_range) {
            output += `**Comparing:** ${params.commit_range}\n`;
        }
        else if (params.staged) {
            output += '**Comparing:** Staged changes vs last commit\n';
        }
        else {
            output += '**Comparing:** Working directory vs staged area\n';
        }
        if (params.file_path) {
            output += `**File:** ${params.file_path}\n`;
        }
        output += '\n';
        // Format the diff output
        if (params.word_diff) {
            output += '```diff\n' + diffOutput + '\n```\n';
        }
        else {
            // Parse and format unified diff
            output += this.formatUnifiedDiff(diffOutput);
        }
        return output;
    }
    formatStatOutput(statOutput) {
        let output = '# Git Diff Statistics\n\n';
        const lines = statOutput.split('\n').filter(line => line.trim());
        for (const line of lines) {
            if (line.includes('|')) {
                // File change line
                const parts = line.split('|');
                const fileName = parts[0].trim();
                const changes = parts[1].trim();
                output += `- **${fileName}**: ${changes}\n`;
            }
            else if (line.includes('file') && (line.includes('changed') || line.includes('insertion') || line.includes('deletion'))) {
                // Summary line
                output += `\n**Summary:** ${line}\n`;
            }
        }
        return output;
    }
    formatUnifiedDiff(diffOutput) {
        const lines = diffOutput.split('\n');
        let output = '';
        let currentFile = '';
        for (const line of lines) {
            if (line.startsWith('diff --git')) {
                // File header
                const match = line.match(/diff --git a\/(.*) b\/(.*)/);
                if (match) {
                    currentFile = match[1];
                    output += `\n## 📄 ${currentFile}\n\n`;
                }
            }
            else if (line.startsWith('@@')) {
                // Hunk header
                output += `\n**${line}**\n\n`;
                output += '```diff\n';
            }
            else if (line.startsWith('+') && !line.startsWith('+++')) {
                // Addition
                output += `+ ${line.substring(1)}\n`;
            }
            else if (line.startsWith('-') && !line.startsWith('---')) {
                // Deletion
                output += `- ${line.substring(1)}\n`;
            }
            else if (line.startsWith(' ')) {
                // Context
                output += `  ${line.substring(1)}\n`;
            }
            else if (line.startsWith('\\')) {
                // No newline indicator
                output += `${line}\n`;
            }
        }
        if (output.includes('```diff\n')) {
            output += '```\n';
        }
        return output;
    }
    async execute(params, abortSignal) {
        const validationError = this.validateToolParams(params);
        if (validationError) {
            return {
                llmContent: `Error: ${validationError}`,
                returnDisplay: `Git Diff Error: ${validationError}`,
            };
        }
        if (abortSignal.aborted) {
            return {
                llmContent: 'Git diff command was cancelled by user.',
                returnDisplay: 'Git diff cancelled.',
            };
        }
        try {
            const workingDir = params.directory
                ? path.resolve(this.config.getTargetDir(), params.directory)
                : this.config.getTargetDir();
            // Check if directory is a git repository
            const gitDirCheck = await this.executeGitCommand(['rev-parse', '--git-dir'], workingDir);
            if (gitDirCheck.exitCode !== 0) {
                return {
                    llmContent: `Error: Not a git repository (or any of the parent directories)`,
                    returnDisplay: 'Error: Not a git repository',
                };
            }
            // Build git diff command
            const args = ['diff'];
            if (params.staged) {
                args.push('--cached');
            }
            if (params.stat_only) {
                args.push('--stat');
            }
            if (params.word_diff) {
                args.push('--word-diff');
            }
            if (params.context_lines !== undefined) {
                args.push(`--unified=${params.context_lines}`);
            }
            if (params.commit_range) {
                args.push(params.commit_range);
            }
            if (params.file_path) {
                args.push('--', params.file_path);
            }
            const result = await this.executeGitCommand(args, workingDir);
            if (result.exitCode !== 0) {
                return {
                    llmContent: `Git diff failed: ${result.stderr}`,
                    returnDisplay: `Git diff error: ${result.stderr}`,
                };
            }
            const formattedOutput = this.formatDiffOutput(result.stdout, params);
            let displayMessage = 'Git diff';
            if (params.file_path) {
                displayMessage += ` for ${params.file_path}`;
            }
            if (params.staged) {
                displayMessage += ' (staged)';
            }
            if (params.commit_range) {
                displayMessage += ` (${params.commit_range})`;
            }
            return {
                llmContent: formattedOutput,
                returnDisplay: displayMessage,
            };
        }
        catch (error) {
            const errorMessage = `Error executing git diff: ${getErrorMessage(error)}`;
            return {
                llmContent: errorMessage,
                returnDisplay: errorMessage,
            };
        }
    }
}
//# sourceMappingURL=git-diff.js.map