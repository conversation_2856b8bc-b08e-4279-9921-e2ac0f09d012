/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React from 'react';
import { Box, Text } from 'ink';
import { ThemeManager } from '../../themes/themes.js';
import { ProviderManager } from '../../providers/manager.js';
import { MaxBoxSized } from './MaxBoxSized.js';

interface HeaderProps {
  themeManager: ThemeManager;
  providerManager?: ProviderManager;
  title?: string;
  subtitle?: string;
  showProviderInfo?: boolean;
  showControls?: boolean;
}

export const Header: React.FC<HeaderProps> = ({
  themeManager,
  providerManager,
  title = '🤖 Arien AI CLI',
  subtitle,
  showProviderInfo = false,
  showControls = false,
}) => {
  const renderProviderInfo = () => {
    if (!showProviderInfo || !providerManager) return null;

    try {
      const provider = providerManager.getCurrentProvider();
      return (
        <Text>
          {themeManager.muted(`Provider: ${provider.getName()} | Model: ${provider.getModel()}`)}
        </Text>
      );
    } catch {
      return (
        <Text>{themeManager.error('No provider configured')}</Text>
      );
    }
  };

  const renderControls = () => {
    if (!showControls) return null;

    return (
      <Text>
        {themeManager.muted('Type your message and press Enter. Ctrl+H for help, Ctrl+L to clear, Ctrl+C to exit')}
      </Text>
    );
  };

  return (
    <MaxBoxSized
      themeManager={themeManager}
      maxWidth="100%"
      responsive={true}
      autoResize={true}
    >
      <Box
        flexDirection="column"
        borderStyle="round"
        borderColor="gray"
        padding={1}
        marginBottom={1}
        width="100%"
      >
        <Text>{themeManager.primary(title)}</Text>
        {subtitle && <Text>{themeManager.muted(subtitle)}</Text>}
        {renderProviderInfo()}
        {renderControls()}
      </Box>
    </MaxBoxSized>
  );
};
