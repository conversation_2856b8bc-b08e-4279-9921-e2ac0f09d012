/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useCallback } from 'react';
import { useInput as useInkInput } from 'ink';
export const useInput = ({ onSubmit, onClear, onHelp, onExit, onPaste, disabled = false, masked = false, maskChar = '*', validator, characterFilter, maxLength, }) => {
    const [input, setInputState] = useState('');
    const [isPasteDetected, setIsPasteDetected] = useState(false);
    const setInput = useCallback((newInput) => {
        if (!disabled) {
            // Apply max length limit
            const limitedInput = maxLength ? newInput.slice(0, maxLength) : newInput;
            // Apply validator if provided
            if (validator && !validator(limitedInput)) {
                return;
            }
            setInputState(limitedInput);
        }
    }, [disabled, maxLength, validator]);
    const clearInput = useCallback(() => {
        setInputState('');
        setIsPasteDetected(false);
    }, []);
    // Default character filter for printable ASCII characters
    const defaultCharacterFilter = useCallback((char) => {
        const charCode = char.charCodeAt(0);
        return charCode >= 32 && charCode <= 126;
    }, []);
    const activeCharacterFilter = characterFilter || defaultCharacterFilter;
    useInkInput((inputChar, key) => {
        if (disabled)
            return;
        // Handle special key combinations
        if (key.ctrl) {
            switch (inputChar.toLowerCase()) {
                case 'c':
                    if (onExit) {
                        onExit();
                    }
                    else {
                        process.exit(0);
                    }
                    return;
                case 'l':
                    if (onClear) {
                        onClear();
                    }
                    return;
                case 'h':
                    if (onHelp) {
                        onHelp();
                    }
                    return;
                case 'v':
                    // Ctrl+V paste detection (though Ink handles this automatically)
                    return;
                default:
                    return;
            }
        }
        // Handle Enter key
        if (key.return) {
            if (input.trim()) {
                onSubmit(input.trim());
                clearInput();
            }
            return;
        }
        // Handle Backspace/Delete
        if (key.backspace || key.delete) {
            setInputState(prev => prev.slice(0, -1));
            setIsPasteDetected(false);
            return;
        }
        // Handle paste operations (multiple characters at once)
        if (inputChar && inputChar.length > 1) {
            // This is a paste operation
            setIsPasteDetected(true);
            // Filter the pasted content
            const filteredContent = inputChar
                .split('')
                .filter(activeCharacterFilter)
                .join('');
            if (filteredContent) {
                // Apply max length limit
                const limitedContent = maxLength
                    ? filteredContent.slice(0, maxLength - input.length)
                    : filteredContent;
                // Apply validator if provided
                const newInput = input + limitedContent;
                if (validator && !validator(newInput)) {
                    return;
                }
                setInputState(prev => prev + limitedContent);
                // Call paste callback if provided
                if (onPaste) {
                    onPaste(limitedContent);
                }
            }
            return;
        }
        // Handle regular character input (single character)
        if (inputChar && inputChar.length === 1 && !key.ctrl && !key.meta) {
            if (activeCharacterFilter(inputChar)) {
                // Check max length
                if (maxLength && input.length >= maxLength) {
                    return;
                }
                // Apply validator if provided
                const newInput = input + inputChar;
                if (validator && !validator(newInput)) {
                    return;
                }
                setInputState(prev => prev + inputChar);
                setIsPasteDetected(false);
            }
        }
    });
    // Generate display value (masked or normal)
    const displayValue = masked
        ? maskChar.repeat(input.length)
        : input;
    return {
        input,
        displayValue,
        setInput,
        clearInput,
        isDisabled: disabled,
        isPasteDetected,
    };
};
//# sourceMappingURL=useInput.js.map