{"version": 3, "file": "HelpScreen.js", "sourceRoot": "", "sources": ["../../../src/components/ui/HelpScreen.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAE/B,OAAO,EAAE,UAAU,EAAY,MAAM,iBAAiB,CAAC;AACvD,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAO/C,MAAM,CAAC,MAAM,UAAU,GAA8B,CAAC,EACpD,YAAY,EACZ,OAAO,GACR,EAAE,EAAE;IACH,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAC/D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,6DAA6D;IAC7D,MAAM,SAAS,GAAe;QAC5B,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,sBAAsB,EAAE,KAAK,EAAE,SAAS,EAAE;QAC9D,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE;QACxC,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,qBAAqB,EAAE,KAAK,EAAE,WAAW,EAAE;QAC/D,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,8BAA8B,EAAE,KAAK,EAAE,OAAO,EAAE;QACpE,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,gCAAgC,EAAE,KAAK,EAAE,OAAO,EAAE;QACtE,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,oCAAoC,EAAE,KAAK,EAAE,OAAO,EAAE;QAC1E,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,kCAAkC,EAAE,KAAK,EAAE,OAAO,EAAE;QACxE,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE;QACxC,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE;QACrD,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,qEAAqE,EAAE,KAAK,EAAE,OAAO,EAAE;QAC5G,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,0CAA0C,EAAE,KAAK,EAAE,OAAO,EAAE;QACjF,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,mCAAmC,EAAE,KAAK,EAAE,OAAO,EAAE;QAC1E,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,wCAAwC,EAAE,KAAK,EAAE,OAAO,EAAE;QAC/E,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,0CAA0C,EAAE,KAAK,EAAE,OAAO,EAAE;QACjF,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE;QACzC,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,kBAAkB,EAAE,KAAK,EAAE,WAAW,EAAE;QAC7D,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,yCAAyC,EAAE,KAAK,EAAE,OAAO,EAAE;QAChF,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,6BAA6B,EAAE,KAAK,EAAE,OAAO,EAAE;QACpE,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,0BAA0B,EAAE,KAAK,EAAE,OAAO,EAAE;QACjE,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,mCAAmC,EAAE,KAAK,EAAE,OAAO,EAAE;QAC1E,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,oCAAoC,EAAE,KAAK,EAAE,OAAO,EAAE;QAC3E,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE;QACzC,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE;QACtD,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,gCAAgC,EAAE,KAAK,EAAE,OAAO,EAAE;QACvE,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,yCAAyC,EAAE,KAAK,EAAE,OAAO,EAAE;QAChF,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,+CAA+C,EAAE,KAAK,EAAE,OAAO,EAAE;QACtF,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,yCAAyC,EAAE,KAAK,EAAE,OAAO,EAAE;QAChF,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE;QACzC,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE;QACxD,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,yCAAyC,EAAE,KAAK,EAAE,MAAM,EAAE;QAC/E,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,sCAAsC,EAAE,KAAK,EAAE,MAAM,EAAE;QAC5E,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,0CAA0C,EAAE,KAAK,EAAE,SAAS,EAAE;KACpF,CAAC;IAEF,OAAO,CACL,KAAC,WAAW,IACV,YAAY,EAAE,YAAY,EAC1B,QAAQ,EAAC,MAAM,EACf,SAAS,EAAC,MAAM,EAChB,UAAU,EAAE,IAAI,EAChB,OAAO,EAAE,CAAC,YAEV,KAAC,UAAU,IACT,YAAY,EAAE,YAAY,EAC1B,KAAK,EAAE,SAAS,EAChB,SAAS,EAAE,EAAE,EACb,UAAU,EAAE,IAAI,EAChB,YAAY,EAAE,IAAI,EAClB,eAAe,EAAE,KAAK,EACtB,cAAc,EAAE,KAAK,EACrB,UAAU,EAAE,KAAK,EACjB,KAAK,EAAC,gCAAsB,EAC5B,WAAW,EAAC,OAAO,EACnB,WAAW,EAAC,MAAM,EAClB,OAAO,EAAE,CAAC,EACV,YAAY,EAAC,2BAA2B,EACxC,OAAO,EAAC,UAAU,GAClB,GACU,CACf,CAAC;AACJ,CAAC,CAAC"}