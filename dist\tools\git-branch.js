/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import fs from 'fs';
import path from 'path';
import { BaseTool } from './tools.js';
import { SchemaValidator } from '../utils/schemaValidator.js';
import { getErrorMessage } from '../utils/errors.js';
import { spawn } from 'child_process';
export class GitBranchTool extends BaseTool {
    config;
    static Name = 'git_branch';
    constructor(config) {
        super(GitBranchTool.Name, 'Git Branch', `Manages git branches with various operations:
- **list**: Show all branches (local, remote, or both)
- **create**: Create a new branch from current or specified branch
- **delete**: Delete a local branch (with safety checks)
- **switch**: Switch to an existing branch
- **merge**: Merge a branch into the current branch

Provides comprehensive branch management with safety validations and detailed feedback.`, {
            type: 'object',
            properties: {
                directory: {
                    type: 'string',
                    description: '(OPTIONAL) Directory to run git branch operations in. Must be relative to the project root directory. Defaults to project root.',
                },
                action: {
                    type: 'string',
                    enum: ['list', 'create', 'delete', 'switch', 'merge'],
                    description: 'The branch operation to perform: list (show branches), create (new branch), delete (remove branch), switch (checkout branch), merge (merge branch).',
                },
                branch_name: {
                    type: 'string',
                    description: 'Name of the branch for create, delete, switch, or merge operations. Required for all actions except list.',
                },
                source_branch: {
                    type: 'string',
                    description: '(OPTIONAL) Source branch to create from or merge from. Defaults to current branch for create, specified branch for merge.',
                },
                force: {
                    type: 'boolean',
                    description: '(OPTIONAL) Force the operation (use with caution). For delete: force delete even if not merged. For create: reset if branch exists. Defaults to false.',
                },
                remote: {
                    type: 'boolean',
                    description: '(OPTIONAL) For list action: show remote branches. For other actions: work with remote branches. Defaults to false.',
                },
                all: {
                    type: 'boolean',
                    description: '(OPTIONAL) For list action: show both local and remote branches. Defaults to false (local only).',
                },
            },
            required: ['action'],
        }, true, // output is markdown
        false);
        this.config = config;
    }
    validateToolParams(params) {
        const validation = SchemaValidator.validate(params, this.parameterSchema);
        if (!validation.isValid) {
            return `Parameters failed schema validation: ${validation.errors.join(', ')}`;
        }
        if (params.directory) {
            if (path.isAbsolute(params.directory)) {
                return 'Directory cannot be absolute. Must be relative to the project root directory.';
            }
            const directory = path.resolve(this.config.getTargetDir(), params.directory);
            if (!fs.existsSync(directory)) {
                return 'Directory must exist.';
            }
        }
        // Validate required parameters for specific actions
        if (params.action !== 'list' && !params.branch_name) {
            return `Branch name is required for ${params.action} action.`;
        }
        // Validate branch name format
        if (params.branch_name) {
            if (params.branch_name.includes('..') || params.branch_name.includes(' ') || params.branch_name.startsWith('-')) {
                return 'Invalid branch name format. Branch names cannot contain "..", spaces, or start with "-".';
            }
        }
        return null;
    }
    async executeGitCommand(args, cwd) {
        return new Promise((resolve) => {
            const process = spawn('git', args, {
                cwd,
                stdio: ['ignore', 'pipe', 'pipe'],
            });
            let stdout = '';
            let stderr = '';
            process.stdout?.on('data', (data) => {
                stdout += data.toString();
            });
            process.stderr?.on('data', (data) => {
                stderr += data.toString();
            });
            process.on('close', (exitCode) => {
                resolve({ stdout, stderr, exitCode: exitCode || 0 });
            });
        });
    }
    async getCurrentBranch(workingDir) {
        const result = await this.executeGitCommand(['branch', '--show-current'], workingDir);
        return result.stdout.trim() || 'HEAD';
    }
    formatBranchList(output, showRemote, showAll) {
        const lines = output.split('\n').filter(line => line.trim());
        let result = '# Git Branches\n\n';
        const localBranches = [];
        const remoteBranches = [];
        let currentBranch = '';
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed.startsWith('* ')) {
                currentBranch = trimmed.substring(2);
                localBranches.push(`**${currentBranch}** (current)`);
            }
            else if (trimmed.startsWith('remotes/')) {
                const remoteBranch = trimmed.substring(8); // Remove 'remotes/'
                remoteBranches.push(remoteBranch);
            }
            else if (trimmed) {
                localBranches.push(trimmed);
            }
        }
        if (!showRemote && !showAll) {
            result += '## 🌿 Local Branches\n\n';
            for (const branch of localBranches) {
                result += `- ${branch}\n`;
            }
        }
        else if (showRemote && !showAll) {
            result += '## 🌐 Remote Branches\n\n';
            for (const branch of remoteBranches) {
                result += `- ${branch}\n`;
            }
        }
        else if (showAll) {
            result += '## 🌿 Local Branches\n\n';
            for (const branch of localBranches) {
                result += `- ${branch}\n`;
            }
            result += '\n## 🌐 Remote Branches\n\n';
            for (const branch of remoteBranches) {
                result += `- ${branch}\n`;
            }
        }
        if (currentBranch) {
            result += `\n**Current branch:** ${currentBranch}\n`;
        }
        return result;
    }
    async execute(params, abortSignal) {
        const validationError = this.validateToolParams(params);
        if (validationError) {
            return {
                llmContent: `Error: ${validationError}`,
                returnDisplay: `Git Branch Error: ${validationError}`,
            };
        }
        if (abortSignal.aborted) {
            return {
                llmContent: 'Git branch command was cancelled by user.',
                returnDisplay: 'Git branch cancelled.',
            };
        }
        try {
            const workingDir = params.directory
                ? path.resolve(this.config.getTargetDir(), params.directory)
                : this.config.getTargetDir();
            // Check if directory is a git repository
            const gitDirCheck = await this.executeGitCommand(['rev-parse', '--git-dir'], workingDir);
            if (gitDirCheck.exitCode !== 0) {
                return {
                    llmContent: `Error: Not a git repository (or any of the parent directories)`,
                    returnDisplay: 'Error: Not a git repository',
                };
            }
            let result;
            let output = '';
            let displayMessage = '';
            switch (params.action) {
                case 'list':
                    const args = ['branch'];
                    if (params.all) {
                        args.push('-a');
                    }
                    else if (params.remote) {
                        args.push('-r');
                    }
                    result = await this.executeGitCommand(args, workingDir);
                    if (result.exitCode !== 0) {
                        return {
                            llmContent: `Failed to list branches: ${result.stderr}`,
                            returnDisplay: `Branch list error: ${result.stderr}`,
                        };
                    }
                    output = this.formatBranchList(result.stdout, params.remote || false, params.all || false);
                    displayMessage = 'Listed git branches';
                    break;
                case 'create':
                    const createArgs = ['checkout', '-b', params.branch_name];
                    if (params.source_branch) {
                        createArgs.push(params.source_branch);
                    }
                    if (params.force) {
                        createArgs.splice(1, 1, '-B'); // Replace -b with -B for force
                    }
                    result = await this.executeGitCommand(createArgs, workingDir);
                    if (result.exitCode !== 0) {
                        return {
                            llmContent: `Failed to create branch: ${result.stderr}`,
                            returnDisplay: `Branch creation error: ${result.stderr}`,
                        };
                    }
                    output = `# Branch Created Successfully\n\n✅ Created and switched to branch **${params.branch_name}**`;
                    if (params.source_branch) {
                        output += ` from **${params.source_branch}**`;
                    }
                    output += '\n';
                    displayMessage = `Created branch: ${params.branch_name}`;
                    break;
                case 'delete':
                    const deleteFlag = params.force ? '-D' : '-d';
                    result = await this.executeGitCommand(['branch', deleteFlag, params.branch_name], workingDir);
                    if (result.exitCode !== 0) {
                        return {
                            llmContent: `Failed to delete branch: ${result.stderr}`,
                            returnDisplay: `Branch deletion error: ${result.stderr}`,
                        };
                    }
                    output = `# Branch Deleted Successfully\n\n🗑️ Deleted branch **${params.branch_name}**\n`;
                    if (params.force) {
                        output += '\n⚠️ **Force delete was used** - branch was deleted even if not fully merged.\n';
                    }
                    displayMessage = `Deleted branch: ${params.branch_name}`;
                    break;
                case 'switch':
                    result = await this.executeGitCommand(['checkout', params.branch_name], workingDir);
                    if (result.exitCode !== 0) {
                        return {
                            llmContent: `Failed to switch branch: ${result.stderr}`,
                            returnDisplay: `Branch switch error: ${result.stderr}`,
                        };
                    }
                    output = `# Switched Branch Successfully\n\n🔄 Switched to branch **${params.branch_name}**\n`;
                    displayMessage = `Switched to branch: ${params.branch_name}`;
                    break;
                case 'merge':
                    const currentBranch = await this.getCurrentBranch(workingDir);
                    result = await this.executeGitCommand(['merge', params.branch_name], workingDir);
                    if (result.exitCode !== 0) {
                        if (result.stderr.includes('CONFLICT')) {
                            output = `# Merge Conflict Detected\n\n⚠️ **Merge conflicts** occurred while merging **${params.branch_name}** into **${currentBranch}**\n\n`;
                            output += '**Conflicts:**\n```\n' + result.stdout + '\n```\n\n';
                            output += 'Please resolve conflicts manually and commit the changes.\n';
                            displayMessage = `Merge conflicts in ${params.branch_name} → ${currentBranch}`;
                        }
                        else {
                            return {
                                llmContent: `Failed to merge branch: ${result.stderr}`,
                                returnDisplay: `Branch merge error: ${result.stderr}`,
                            };
                        }
                    }
                    else {
                        output = `# Merge Completed Successfully\n\n✅ Merged **${params.branch_name}** into **${currentBranch}**\n\n`;
                        if (result.stdout.trim()) {
                            output += '**Merge details:**\n```\n' + result.stdout + '\n```\n';
                        }
                        displayMessage = `Merged ${params.branch_name} into ${currentBranch}`;
                    }
                    break;
                default:
                    return {
                        llmContent: `Error: Unknown action "${params.action}"`,
                        returnDisplay: `Unknown branch action: ${params.action}`,
                    };
            }
            return {
                llmContent: output,
                returnDisplay: displayMessage,
            };
        }
        catch (error) {
            const errorMessage = `Error executing git branch operation: ${getErrorMessage(error)}`;
            return {
                llmContent: errorMessage,
                returnDisplay: errorMessage,
            };
        }
    }
}
//# sourceMappingURL=git-branch.js.map