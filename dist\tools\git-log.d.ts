/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { Config } from '../config/config.js';
import { BaseTool, ToolResult } from './tools.js';
export interface GitLogParams {
    directory?: string;
    max_count?: number;
    since?: string;
    until?: string;
    author?: string;
    grep?: string;
    file_path?: string;
    oneline?: boolean;
    graph?: boolean;
    stat?: boolean;
    format?: 'short' | 'medium' | 'full' | 'fuller' | 'oneline' | 'custom';
    [key: string]: unknown;
}
export interface CommitInfo {
    hash: string;
    shortHash: string;
    author: string;
    authorEmail: string;
    date: string;
    subject: string;
    body?: string;
}
export declare class GitLogTool extends BaseTool<GitLogParams, ToolResult> {
    private readonly config;
    static Name: string;
    constructor(config: Config);
    validateToolParams(params: GitLogParams): string | null;
    private executeGitCommand;
    private parseCommitLog;
    private formatLogOutput;
    execute(params: GitLogParams, abortSignal: AbortSignal): Promise<ToolResult>;
}
//# sourceMappingURL=git-log.d.ts.map