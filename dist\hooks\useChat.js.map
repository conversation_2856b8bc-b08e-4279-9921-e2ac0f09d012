{"version": 3, "file": "useChat.js", "sourceRoot": "", "sources": ["../../src/hooks/useChat.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AAI9C,OAAO,EAAE,mBAAmB,EAAE,MAAM,6BAA6B,CAAC;AAwBlE,MAAM,CAAC,MAAM,OAAO,GAAG,CAAC,EACtB,eAAe,EACf,YAAY,EACZ,YAAY,GAAG,mBAAmB,EAAE,GACrB,EAAiB,EAAE;IAClC,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAgB,EAAE,CAAC,CAAC;IAC5D,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAClD,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAgB,IAAI,CAAC,CAAC;IACxD,MAAM,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,QAAQ,CAAS,EAAE,CAAC,CAAC;IAEnE,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,OAA8C,EAAE,EAAE;QAChF,MAAM,UAAU,GAAgB;YAC9B,GAAG,OAAO;YACV,EAAE,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACpC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;QACF,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;QAC3C,OAAO,UAAU,CAAC,EAAE,CAAC;IACvB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,aAAa,GAAG,WAAW,CAAC,CAAC,EAAU,EAAE,OAA6B,EAAE,EAAE;QAC9E,WAAW,CAAC,IAAI,CAAC,EAAE,CACjB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CACb,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC,GAAG,CAC7C,CACF,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,mBAAmB,GAAG,WAAW,CAAC,KAAK,EAC3C,aAAqE,EACrE,EAAE;QACF,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;YACjC,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEpE,0CAA0C;gBAC1C,UAAU,CAAC;oBACT,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,QAAQ,IAAI,CAAC,IAAI,YAAY,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;iBAC/D,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,UAAU,CAAC;oBACT,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,QAAQ,IAAI,CAAC,IAAI,WAAW,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;oBAC/F,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;iBAChE,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC;IAE/B,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,EAAE,OAAe,EAAE,EAAE;QACxD,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,SAAS;YAAE,OAAO;QAEzC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAE5B,mBAAmB;QACnB,UAAU,CAAC;YACT,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;QAEH,YAAY,CAAC,IAAI,CAAC,CAAC;QAEnB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,eAAe,CAAC,kBAAkB,EAAE,CAAC;YACtD,MAAM,KAAK,GAAG,MAAM,YAAY,CAAC,mBAAmB,EAAE,CAAC;YAEvD,yCAAyC;YACzC,MAAM,kBAAkB,GAAG,UAAU,CAAC;gBACpC,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,EAAE;gBACX,WAAW,EAAE,IAAI;aAClB,CAAC,CAAC;YAEH,2BAA2B;YAC3B,MAAM,mBAAmB,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAC/C,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,OAAO,EAAE,GAAG,CAAC,OAAO;aACrB,CAAC,CAAC,CAAC;YAEJ,mBAAmB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;YAE7D,kBAAkB;YAClB,MAAM,SAAS,GAAG,QAAQ,CAAC,yBAAyB,CAAC,OAAO,EAAE;gBAC5D,KAAK;gBACL,YAAY;aACb,CAAC,CAAC;YAEH,IAAI,YAAY,GAAG,EAAE,CAAC;YACtB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,SAAS,EAAE,CAAC;gBACpC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;oBAC9B,YAAY,IAAI,KAAK,CAAC;oBACtB,aAAa,CAAC,kBAAkB,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC,CAAC;gBAC/D,CAAC;qBAAM,CAAC;oBACN,qCAAqC;oBACrC,MAAM,QAAQ,GAAG,KAAoB,CAAC;oBACtC,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAChE,MAAM,mBAAmB,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;oBACpD,CAAC;oBAED,mBAAmB;oBACnB,aAAa,CAAC,kBAAkB,EAAE;wBAChC,OAAO,EAAE,QAAQ,CAAC,IAAI;wBACtB,WAAW,EAAE,KAAK;qBACnB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC;YACvF,QAAQ,CAAC,YAAY,CAAC,CAAC;YAEvB,UAAU,CAAC;gBACT,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,UAAU,YAAY,EAAE;gBACjC,KAAK,EAAE,YAAY;aACpB,CAAC,CAAC;QACL,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,EAAE;QACD,SAAS;QACT,QAAQ;QACR,eAAe;QACf,YAAY;QACZ,YAAY;QACZ,UAAU;QACV,aAAa;QACb,mBAAmB;KACpB,CAAC,CAAC;IAEH,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE;QACrC,WAAW,CAAC,EAAE,CAAC,CAAC;QAChB,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,gBAAgB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QAC9C,IAAI,eAAe,EAAE,CAAC;YACpB,uDAAuD;YACvD,WAAW,CAAC,IAAI,CAAC,EAAE;gBACjB,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC1C,IAAI,WAAW,EAAE,IAAI,KAAK,WAAW,IAAI,WAAW,CAAC,KAAK,EAAE,CAAC;oBAC3D,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC3B,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,CAAC,eAAe,CAAC,CAAC;QACrC,CAAC;IACH,CAAC,EAAE,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC;IAEnC,OAAO;QACL,QAAQ;QACR,SAAS;QACT,KAAK;QACL,WAAW;QACX,aAAa;QACb,gBAAgB;KACjB,CAAC;AACJ,CAAC,CAAC"}