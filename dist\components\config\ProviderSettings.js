import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState } from 'react';
import { Box, Text, useInput } from 'ink';
export const ProviderSettings = ({ themeManager, isOpen, onClose, onSave, }) => {
    const [selectedProviderIndex, setSelectedProviderIndex] = useState(0);
    const [selectedFieldIndex, setSelectedFieldIndex] = useState(0);
    const [editingValue, setEditingValue] = useState('');
    const [mode, setMode] = useState('providers');
    const [hasChanges, setHasChanges] = useState(false);
    const [providers, setProviders] = useState([
        {
            id: 'openai',
            name: 'OpenAI',
            description: 'OpenAI GPT models',
            enabled: true,
            apiKey: '',
            baseUrl: 'https://api.openai.com/v1',
            models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
            defaultModel: 'gpt-4',
            maxTokens: 4096,
            temperature: 0.7,
            settings: {
                organization: '',
                project: '',
            },
        },
        {
            id: 'anthropic',
            name: 'Anthropic',
            description: 'Claude models by Anthropic',
            enabled: false,
            apiKey: '',
            baseUrl: 'https://api.anthropic.com',
            models: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
            defaultModel: 'claude-3-sonnet',
            maxTokens: 4096,
            temperature: 0.7,
            settings: {
                version: '2023-06-01',
            },
        },
        {
            id: 'google',
            name: 'Google AI',
            description: 'Google Gemini models',
            enabled: false,
            apiKey: '',
            baseUrl: 'https://generativelanguage.googleapis.com/v1',
            models: ['gemini-pro', 'gemini-pro-vision'],
            defaultModel: 'gemini-pro',
            maxTokens: 2048,
            temperature: 0.7,
            settings: {},
        },
        {
            id: 'deepseek',
            name: 'DeepSeek',
            description: 'DeepSeek AI models',
            enabled: false,
            apiKey: '',
            baseUrl: 'https://api.deepseek.com/v1',
            models: ['deepseek-chat', 'deepseek-coder'],
            defaultModel: 'deepseek-chat',
            maxTokens: 4096,
            temperature: 0.7,
            settings: {},
        },
    ]);
    useInput((input, key) => {
        if (!isOpen)
            return;
        if (key.escape) {
            if (mode === 'editing') {
                setMode('fields');
                setEditingValue('');
                return;
            }
            if (mode === 'fields') {
                setMode('providers');
                return;
            }
            onClose();
            return;
        }
        if (mode === 'providers') {
            if (key.upArrow && selectedProviderIndex > 0) {
                setSelectedProviderIndex(selectedProviderIndex - 1);
                return;
            }
            if (key.downArrow && selectedProviderIndex < providers.length - 1) {
                setSelectedProviderIndex(selectedProviderIndex + 1);
                return;
            }
            if (key.return) {
                setMode('fields');
                setSelectedFieldIndex(0);
                return;
            }
            if (input === ' ') {
                toggleProviderEnabled(selectedProviderIndex);
                return;
            }
        }
        if (mode === 'fields') {
            const fieldCount = getFieldCount();
            if (key.upArrow && selectedFieldIndex > 0) {
                setSelectedFieldIndex(selectedFieldIndex - 1);
                return;
            }
            if (key.downArrow && selectedFieldIndex < fieldCount - 1) {
                setSelectedFieldIndex(selectedFieldIndex + 1);
                return;
            }
            if (key.return) {
                const fieldName = getFieldName(selectedFieldIndex);
                if (fieldName === 'enabled') {
                    toggleProviderEnabled(selectedProviderIndex);
                }
                else {
                    setMode('editing');
                    setEditingValue(getFieldValue(selectedFieldIndex)?.toString() || '');
                }
                return;
            }
        }
        if (mode === 'editing') {
            if (key.return) {
                updateFieldValue(selectedFieldIndex, editingValue);
                setMode('fields');
                setEditingValue('');
                return;
            }
            if (key.backspace || key.delete) {
                setEditingValue(prev => prev.slice(0, -1));
                return;
            }
            if (input && input.length === 1 && !key.ctrl && !key.meta) {
                setEditingValue(prev => prev + input);
            }
        }
        // Global shortcuts
        if (key.ctrl && input === 's') {
            handleSave();
            return;
        }
    });
    const getFieldCount = () => {
        return 8; // enabled, apiKey, baseUrl, defaultModel, maxTokens, temperature, test, save
    };
    const getFieldName = (index) => {
        const fields = ['enabled', 'apiKey', 'baseUrl', 'defaultModel', 'maxTokens', 'temperature', 'test', 'save'];
        return fields[index] || '';
    };
    const getFieldValue = (index) => {
        const provider = providers[selectedProviderIndex];
        const fieldName = getFieldName(index);
        switch (fieldName) {
            case 'enabled':
                return provider.enabled;
            case 'apiKey':
                return provider.apiKey;
            case 'baseUrl':
                return provider.baseUrl;
            case 'defaultModel':
                return provider.defaultModel;
            case 'maxTokens':
                return provider.maxTokens;
            case 'temperature':
                return provider.temperature;
            default:
                return '';
        }
    };
    const updateFieldValue = (fieldIndex, value) => {
        const fieldName = getFieldName(fieldIndex);
        setProviders(prev => prev.map((provider, index) => {
            if (index === selectedProviderIndex) {
                const updatedProvider = { ...provider };
                switch (fieldName) {
                    case 'apiKey':
                        updatedProvider.apiKey = value;
                        break;
                    case 'baseUrl':
                        updatedProvider.baseUrl = value;
                        break;
                    case 'defaultModel':
                        updatedProvider.defaultModel = value;
                        break;
                    case 'maxTokens':
                        updatedProvider.maxTokens = parseInt(value) || 0;
                        break;
                    case 'temperature':
                        updatedProvider.temperature = parseFloat(value) || 0;
                        break;
                }
                return updatedProvider;
            }
            return provider;
        }));
        setHasChanges(true);
    };
    const toggleProviderEnabled = (providerIndex) => {
        setProviders(prev => prev.map((provider, index) => index === providerIndex
            ? { ...provider, enabled: !provider.enabled }
            : provider));
        setHasChanges(true);
    };
    const handleSave = () => {
        onSave?.(providers);
        setHasChanges(false);
    };
    if (!isOpen)
        return null;
    const renderProvidersList = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary('AI Providers:') }), _jsx(Text, {}), providers.map((provider, index) => (_jsx(Box, { marginLeft: 2, children: _jsxs(Text, { children: [selectedProviderIndex === index ? themeManager.highlight(' ▶ ') : '   ', provider.enabled ? themeManager.success('●') : themeManager.muted('○'), ' ', themeManager.primary(provider.name), ' - ', themeManager.muted(provider.description)] }) }, provider.id))), _jsx(Text, {}), _jsx(Box, { marginLeft: 2, children: _jsx(Text, { children: themeManager.muted('Space: Toggle • Enter: Configure') }) })] }));
    const renderProviderFields = () => {
        const provider = providers[selectedProviderIndex];
        return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary(`Configure: ${provider.name}`) }), _jsx(Text, {}), [
                    { name: 'enabled', label: 'Enabled', value: provider.enabled ? 'Yes' : 'No', type: 'boolean' },
                    { name: 'apiKey', label: 'API Key', value: provider.apiKey ? '*'.repeat(provider.apiKey.length) : 'Not set', type: 'password' },
                    { name: 'baseUrl', label: 'Base URL', value: provider.baseUrl || 'Not set', type: 'string' },
                    { name: 'defaultModel', label: 'Default Model', value: provider.defaultModel, type: 'select' },
                    { name: 'maxTokens', label: 'Max Tokens', value: provider.maxTokens.toString(), type: 'number' },
                    { name: 'temperature', label: 'Temperature', value: provider.temperature.toString(), type: 'number' },
                    { name: 'test', label: 'Test Connection', value: 'Click to test', type: 'action' },
                    { name: 'save', label: 'Save Changes', value: hasChanges ? 'Unsaved changes' : 'No changes', type: 'action' },
                ].map((field, index) => (_jsxs(Box, { marginLeft: 2, marginBottom: 1, children: [_jsx(Box, { children: _jsxs(Text, { children: [selectedFieldIndex === index ? themeManager.highlight(' ▶ ') : '   ', themeManager.primary(field.label), ":"] }) }), _jsx(Box, { marginLeft: 3, children: _jsx(Text, { children: field.type === 'boolean' && provider.enabled ? themeManager.success(field.value) :
                                    field.type === 'boolean' && !provider.enabled ? themeManager.muted(field.value) :
                                        field.type === 'password' ? themeManager.accent(field.value) :
                                            field.type === 'action' && field.name === 'save' && hasChanges ? themeManager.warning(field.value) :
                                                field.type === 'action' ? themeManager.info(field.value) :
                                                    themeManager.accent(field.value) }) })] }, field.name))), _jsx(Text, {}), _jsxs(Box, { marginLeft: 2, children: [_jsx(Text, { children: themeManager.secondary('Available Models: ') }), _jsx(Text, { children: themeManager.muted(provider.models.join(', ')) })] })] }));
    };
    const renderEditingInterface = () => {
        const provider = providers[selectedProviderIndex];
        const fieldName = getFieldName(selectedFieldIndex);
        return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary(`Editing: ${fieldName}`) }), _jsx(Text, {}), _jsxs(Box, { marginLeft: 2, children: [_jsx(Text, { children: themeManager.secondary('Provider: ') }), _jsx(Text, { children: themeManager.info(provider.name) })] }), _jsx(Text, {}), _jsxs(Box, { marginLeft: 2, children: [_jsx(Text, { children: themeManager.secondary('Current Value: ') }), _jsx(Text, { children: themeManager.primary(editingValue || '...') })] }), fieldName === 'defaultModel' && (_jsxs(Box, { marginLeft: 2, marginTop: 1, children: [_jsx(Text, { children: themeManager.secondary('Available Models: ') }), _jsx(Text, { children: themeManager.muted(provider.models.join(', ')) })] })), fieldName === 'temperature' && (_jsx(Box, { marginLeft: 2, marginTop: 1, children: _jsx(Text, { children: themeManager.muted('Range: 0.0 to 2.0 (0.7 recommended)') }) }))] }));
    };
    const renderControls = () => (_jsx(Box, { marginTop: 1, borderStyle: "single", borderColor: "gray", padding: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.muted('Controls:') }), mode === 'providers' && (_jsx(Text, { children: themeManager.muted('  ↑↓ Navigate • Enter Configure • Space Toggle • Esc Close') })), mode === 'fields' && (_jsx(Text, { children: themeManager.muted('  ↑↓ Navigate • Enter Edit • Esc Back • Ctrl+S Save') })), mode === 'editing' && (_jsx(Text, { children: themeManager.muted('  Type value • Enter Save • Esc Cancel') })), hasChanges && (_jsx(Text, { children: themeManager.warning('  Unsaved changes!') }))] }) }));
    return (_jsx(Box, { flexDirection: "column", padding: 1, children: _jsx(Box, { borderStyle: "round", borderColor: "blue", padding: 1, children: _jsxs(Box, { flexDirection: "column", width: "100%", children: [_jsx(Text, { children: themeManager.primary('🤖 Provider Settings') }), _jsx(Text, {}), mode === 'providers' && renderProvidersList(), mode === 'fields' && renderProviderFields(), mode === 'editing' && renderEditingInterface(), renderControls()] }) }) }));
};
//# sourceMappingURL=ProviderSettings.js.map