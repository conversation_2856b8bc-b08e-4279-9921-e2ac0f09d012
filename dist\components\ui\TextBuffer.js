import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useEffect, useRef, useMemo } from 'react';
import { Box, Text, useInput } from 'ink';
export const TextBuffer = ({ themeManager, lines = [], content, maxLines = 1000, maxWidth, maxHeight = 20, scrollable = true, showLineNumbers = false, showTimestamps = false, enableSearch = false, enableSelection = false, autoScroll = true, scrollToBottom = false, highlightPattern, filterPattern, onSelectionChange, onScroll, borderStyle, borderColor, padding = 0, margin = 0, title, footer, emptyMessage = 'No content to display', loadingMessage = 'Loading...', isLoading = false, disabled = false, }) => {
    const [scrollOffset, setScrollOffset] = useState(0);
    const [selectedLines, setSelectedLines] = useState(new Set());
    const [searchTerm, setSearchTerm] = useState('');
    const [searchMode, setSearchMode] = useState(false);
    const containerRef = useRef(null);
    // Convert content string to lines if provided
    const processedLines = useMemo(() => {
        let allLines = [];
        if (content) {
            const contentLines = content.split('\n').map((line, index) => ({
                id: `content-${index}`,
                content: line,
                timestamp: Date.now(),
            }));
            allLines = [...contentLines];
        }
        if (lines.length > 0) {
            allLines = [...allLines, ...lines];
        }
        // Apply filtering
        if (filterPattern) {
            const regex = typeof filterPattern === 'string' ? new RegExp(filterPattern, 'i') : filterPattern;
            allLines = allLines.filter(line => regex.test(line.content));
        }
        // Apply search filtering
        if (searchTerm) {
            const searchRegex = new RegExp(searchTerm, 'i');
            allLines = allLines.filter(line => searchRegex.test(line.content));
        }
        // Limit lines to maxLines
        if (allLines.length > maxLines) {
            allLines = allLines.slice(-maxLines);
        }
        return allLines;
    }, [content, lines, maxLines, filterPattern, searchTerm]);
    // Calculate visible lines based on scroll and height
    const visibleLines = useMemo(() => {
        if (!scrollable)
            return processedLines;
        const startIndex = scrollOffset;
        const endIndex = Math.min(processedLines.length, scrollOffset + maxHeight);
        return processedLines.slice(startIndex, endIndex);
    }, [processedLines, scrollOffset, maxHeight, scrollable]);
    // Auto-scroll to bottom when new content is added
    useEffect(() => {
        if (autoScroll || scrollToBottom) {
            const maxScroll = Math.max(0, processedLines.length - maxHeight);
            setScrollOffset(maxScroll);
        }
    }, [processedLines.length, autoScroll, scrollToBottom, maxHeight]);
    // Handle keyboard input for scrolling and search
    useInput((input, key) => {
        if (disabled)
            return;
        if (searchMode) {
            if (key.escape) {
                setSearchMode(false);
                setSearchTerm('');
            }
            else if (key.return) {
                setSearchMode(false);
            }
            else if (key.backspace) {
                setSearchTerm(prev => prev.slice(0, -1));
            }
            else if (input && input.length === 1) {
                setSearchTerm(prev => prev + input);
            }
            return;
        }
        if (scrollable) {
            if (key.upArrow) {
                setScrollOffset(prev => Math.max(0, prev - 1));
            }
            else if (key.downArrow) {
                setScrollOffset(prev => Math.min(processedLines.length - maxHeight, prev + 1));
            }
            else if (key.pageUp) {
                setScrollOffset(prev => Math.max(0, prev - maxHeight));
            }
            else if (key.pageDown) {
                setScrollOffset(prev => Math.min(processedLines.length - maxHeight, prev + maxHeight));
            }
            else if (input === 'g') {
                setScrollOffset(0); // Go to top
            }
            else if (input === 'G') {
                setScrollOffset(Math.max(0, processedLines.length - maxHeight)); // Go to bottom
            }
        }
        if (enableSearch && input === '/') {
            setSearchMode(true);
            setSearchTerm('');
        }
        // Handle line selection
        if (enableSelection && input >= '1' && input <= '9') {
            const lineIndex = parseInt(input) - 1;
            if (lineIndex < visibleLines.length) {
                const actualIndex = scrollOffset + lineIndex;
                const newSelected = new Set(selectedLines);
                if (newSelected.has(actualIndex)) {
                    newSelected.delete(actualIndex);
                }
                else {
                    newSelected.add(actualIndex);
                }
                setSelectedLines(newSelected);
                if (onSelectionChange) {
                    const selectedLineObjects = Array.from(newSelected).map(index => processedLines[index]);
                    onSelectionChange(selectedLineObjects);
                }
            }
        }
    });
    // Notify scroll changes
    useEffect(() => {
        if (onScroll) {
            onScroll({
                top: scrollOffset,
                bottom: scrollOffset + visibleLines.length,
            });
        }
    }, [scrollOffset, visibleLines.length, onScroll]);
    const formatTimestamp = (timestamp) => {
        if (!timestamp || !showTimestamps)
            return '';
        const date = new Date(timestamp);
        return `[${date.toLocaleTimeString()}] `;
    };
    const formatLineNumber = (index) => {
        if (!showLineNumbers)
            return '';
        const lineNum = (scrollOffset + index + 1).toString().padStart(3, ' ');
        return `${lineNum}: `;
    };
    const applyHighlighting = (content) => {
        if (!highlightPattern)
            return content;
        // For terminal, we'll just return the content as-is since we can't do complex highlighting
        // In a real implementation, you might want to use different colors for matches
        return content;
    };
    const getLineStyle = (line, isSelected) => {
        if (isSelected) {
            return themeManager.highlight;
        }
        if (line.color) {
            return themeManager[line.color];
        }
        return (text) => text;
    };
    const renderLine = (line, index) => {
        const actualIndex = scrollOffset + index;
        const isSelected = selectedLines.has(actualIndex);
        const styleFunction = getLineStyle(line, isSelected);
        const timestamp = formatTimestamp(line.timestamp);
        const lineNumber = formatLineNumber(index);
        const highlightedContent = applyHighlighting(line.content);
        let styledContent = highlightedContent;
        if (line.style === 'bold') {
            styledContent = highlightedContent; // Terminal styling would be applied here
        }
        const fullContent = `${timestamp}${lineNumber}${styledContent}`;
        // Note: Ink Box doesn't support onClick, so we'll handle clicks through keyboard input
        return (_jsx(Box, { children: _jsx(Text, { children: styleFunction(fullContent) }) }, line.id));
    };
    const renderTitle = () => {
        if (!title)
            return null;
        return (_jsx(Box, { marginBottom: 1, children: _jsx(Text, { children: themeManager.primary(title) }) }));
    };
    const renderFooter = () => {
        if (!footer && !scrollable)
            return null;
        const scrollInfo = scrollable ?
            ` (${scrollOffset + 1}-${scrollOffset + visibleLines.length} of ${processedLines.length})` : '';
        const footerText = footer || `Lines${scrollInfo}`;
        return (_jsx(Box, { marginTop: 1, children: _jsx(Text, { children: themeManager.muted(footerText) }) }));
    };
    const renderSearchBar = () => {
        if (!searchMode)
            return null;
        return (_jsxs(Box, { marginBottom: 1, children: [_jsx(Text, { children: themeManager.primary('Search: ') }), _jsx(Text, { children: searchTerm }), _jsx(Text, { children: themeManager.accent('▋') })] }));
    };
    const renderScrollIndicators = () => {
        if (!scrollable || processedLines.length <= maxHeight)
            return null;
        const hasMore = scrollOffset + maxHeight < processedLines.length;
        const hasPrevious = scrollOffset > 0;
        return (_jsxs(Box, { justifyContent: "space-between", children: [_jsx(Text, { children: hasPrevious ? themeManager.muted('↑ More above') : '' }), _jsx(Text, { children: hasMore ? themeManager.muted('↓ More below') : '' })] }));
    };
    if (isLoading) {
        return (_jsxs(Box, { flexDirection: "column", padding: padding, margin: margin, children: [renderTitle(), _jsx(Text, { children: themeManager.muted(loadingMessage) })] }));
    }
    if (processedLines.length === 0) {
        return (_jsxs(Box, { flexDirection: "column", padding: padding, margin: margin, children: [renderTitle(), _jsx(Text, { children: themeManager.muted(emptyMessage) })] }));
    }
    return (_jsxs(Box, { ref: containerRef, flexDirection: "column", width: maxWidth, height: maxHeight, borderStyle: borderStyle, borderColor: borderColor, padding: padding, margin: margin, children: [renderTitle(), renderSearchBar(), renderScrollIndicators(), _jsx(Box, { flexDirection: "column", flexGrow: 1, children: visibleLines.map((line, index) => renderLine(line, index)) }), renderScrollIndicators(), renderFooter()] }));
};
// Convenience components for common use cases
export const CodeTextBuffer = (props) => (_jsx(TextBuffer, { ...props, variant: "code", showLineNumbers: true }));
export const LogTextBuffer = (props) => (_jsx(TextBuffer, { ...props, variant: "detailed", showTimestamps: true }));
export const CompactTextBuffer = (props) => (_jsx(TextBuffer, { ...props, variant: "compact" }));
export const ScrollableTextBuffer = (props) => (_jsx(TextBuffer, { ...props, scrollable: true }));
//# sourceMappingURL=TextBuffer.js.map