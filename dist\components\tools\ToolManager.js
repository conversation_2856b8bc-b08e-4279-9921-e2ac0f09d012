import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState } from 'react';
import { Box, Text, useInput } from 'ink';
export const ToolManager = ({ themeManager, isOpen, onClose, onToolToggle, onToolRefresh, onToolTest, }) => {
    const [selectedToolIndex, setSelectedToolIndex] = useState(0);
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [mode, setMode] = useState('list');
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [searchQuery] = useState('');
    // Mock tool data - in real implementation, this would come from ToolRegistry
    const [tools, setTools] = useState([
        {
            name: 'file_read',
            displayName: 'File Reader',
            description: 'Read and analyze file contents',
            type: 'built-in',
            enabled: true,
            parameterSchema: { type: 'object', properties: { path: { type: 'string' } } },
            isOutputMarkdown: true,
            canUpdateOutput: false,
            lastUsed: new Date(),
            usageCount: 45,
            status: 'active',
        },
        {
            name: 'web_search',
            displayName: 'Web Search',
            description: 'Search the web for information',
            type: 'built-in',
            enabled: true,
            parameterSchema: { type: 'object', properties: { query: { type: 'string' } } },
            isOutputMarkdown: true,
            canUpdateOutput: false,
            lastUsed: new Date(Date.now() - 86400000),
            usageCount: 23,
            status: 'active',
        },
        {
            name: 'code_analyzer',
            displayName: 'Code Analyzer',
            description: 'Analyze code structure and patterns',
            type: 'discovered',
            enabled: false,
            parameterSchema: { type: 'object', properties: { code: { type: 'string' }, language: { type: 'string' } } },
            isOutputMarkdown: true,
            canUpdateOutput: true,
            usageCount: 0,
            status: 'inactive',
        },
        {
            name: 'filesystem_browser',
            displayName: 'Filesystem Browser',
            description: 'Browse and navigate filesystem',
            type: 'mcp',
            enabled: true,
            serverName: 'filesystem',
            parameterSchema: { type: 'object', properties: { path: { type: 'string' } } },
            isOutputMarkdown: false,
            canUpdateOutput: false,
            lastUsed: new Date(Date.now() - 3600000),
            usageCount: 12,
            status: 'active',
        },
        {
            name: 'git_operations',
            displayName: 'Git Operations',
            description: 'Perform Git version control operations',
            type: 'mcp',
            enabled: false,
            serverName: 'git',
            parameterSchema: { type: 'object', properties: { command: { type: 'string' } } },
            isOutputMarkdown: true,
            canUpdateOutput: false,
            usageCount: 0,
            status: 'error',
            errorMessage: 'MCP server not responding',
        },
    ]);
    useInput((input, key) => {
        if (!isOpen)
            return;
        if (key.escape) {
            if (mode === 'details' || mode === 'settings') {
                setMode('list');
                return;
            }
            onClose();
            return;
        }
        if (mode === 'list') {
            const filteredTools = getFilteredTools();
            if (key.upArrow && selectedToolIndex > 0) {
                setSelectedToolIndex(selectedToolIndex - 1);
                return;
            }
            if (key.downArrow && selectedToolIndex < filteredTools.length - 1) {
                setSelectedToolIndex(selectedToolIndex + 1);
                return;
            }
            if (key.return) {
                setMode('details');
                return;
            }
            if (input === ' ') {
                const tool = filteredTools[selectedToolIndex];
                toggleTool(tool.name);
                return;
            }
            if (key.tab) {
                cycleCategoryFilter();
                return;
            }
            if (key.ctrl && input === 'r') {
                handleRefresh();
                return;
            }
            if (key.ctrl && input === 't') {
                const tool = filteredTools[selectedToolIndex];
                handleTestTool(tool.name);
                return;
            }
        }
        if (mode === 'details') {
            if (input === 's') {
                setMode('settings');
                return;
            }
            if (input === 't') {
                const filteredTools = getFilteredTools();
                const tool = filteredTools[selectedToolIndex];
                handleTestTool(tool.name);
                return;
            }
        }
    });
    const getFilteredTools = () => {
        let filtered = tools;
        if (selectedCategory !== 'all') {
            filtered = filtered.filter(tool => tool.type === selectedCategory);
        }
        if (searchQuery) {
            const query = searchQuery.toLowerCase();
            filtered = filtered.filter(tool => tool.name.toLowerCase().includes(query) ||
                tool.displayName.toLowerCase().includes(query) ||
                tool.description.toLowerCase().includes(query));
        }
        return filtered;
    };
    const cycleCategoryFilter = () => {
        const categories = ['all', 'built-in', 'discovered', 'mcp'];
        const currentIndex = categories.indexOf(selectedCategory);
        const nextIndex = (currentIndex + 1) % categories.length;
        setSelectedCategory(categories[nextIndex]);
        setSelectedToolIndex(0);
    };
    const toggleTool = (toolName) => {
        setTools(prev => prev.map(tool => tool.name === toolName
            ? { ...tool, enabled: !tool.enabled, status: !tool.enabled ? 'active' : 'inactive' }
            : tool));
        const tool = tools.find(t => t.name === toolName);
        if (tool) {
            onToolToggle?.(toolName, !tool.enabled);
        }
    };
    const handleRefresh = async () => {
        setIsRefreshing(true);
        try {
            await onToolRefresh?.();
            // In real implementation, would reload tools from ToolRegistry
        }
        catch (error) {
            console.error('Failed to refresh tools:', error);
        }
        finally {
            setIsRefreshing(false);
        }
    };
    const handleTestTool = async (toolName) => {
        const toolIndex = tools.findIndex(t => t.name === toolName);
        if (toolIndex === -1)
            return;
        setTools(prev => prev.map((tool, index) => index === toolIndex
            ? { ...tool, status: 'loading' }
            : tool));
        try {
            const success = await onToolTest?.(toolName);
            setTools(prev => prev.map((tool, index) => index === toolIndex
                ? { ...tool, status: success ? 'active' : 'error', errorMessage: success ? undefined : 'Test failed' }
                : tool));
        }
        catch (error) {
            setTools(prev => prev.map((tool, index) => index === toolIndex
                ? { ...tool, status: 'error', errorMessage: error instanceof Error ? error.message : 'Unknown error' }
                : tool));
        }
    };
    if (!isOpen)
        return null;
    const renderToolsList = () => {
        const filteredTools = getFilteredTools();
        return (_jsxs(Box, { flexDirection: "column", children: [_jsxs(Box, { justifyContent: "space-between", children: [_jsx(Text, { children: themeManager.primary('Available Tools') }), _jsx(Text, { children: themeManager.muted(`Filter: ${selectedCategory} (${filteredTools.length})`) })] }), isRefreshing && (_jsx(Box, { marginTop: 1, children: _jsx(Text, { children: themeManager.info('🔄 Refreshing tools...') }) })), _jsx(Text, {}), filteredTools.map((tool, index) => {
                    const isSelected = index === selectedToolIndex;
                    const statusIcon = getStatusIcon(tool.status);
                    const statusColor = getStatusColor(tool.status);
                    return (_jsx(Box, { marginLeft: 2, children: _jsxs(Text, { children: [isSelected ? themeManager.highlight(' ▶ ') : '   ', tool.enabled ? themeManager.success('●') : themeManager.muted('○'), ' ', statusColor(statusIcon), ' ', themeManager.primary(tool.displayName), ' ', themeManager.muted(`[${tool.type}]`), tool.serverName && themeManager.accent(` (${tool.serverName})`)] }) }, tool.name));
                }), filteredTools.length === 0 && (_jsx(Box, { justifyContent: "center", marginTop: 2, children: _jsx(Text, { children: themeManager.muted('No tools found matching current filter') }) }))] }));
    };
    const renderToolDetails = () => {
        const filteredTools = getFilteredTools();
        const tool = filteredTools[selectedToolIndex];
        if (!tool)
            return null;
        return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary(`Tool Details: ${tool.displayName}`) }), _jsx(Text, {}), _jsxs(Box, { marginLeft: 2, flexDirection: "column", children: [_jsxs(Text, { children: [themeManager.secondary('Name: '), themeManager.info(tool.name)] }), _jsxs(Text, { children: [themeManager.secondary('Type: '), themeManager.accent(tool.type)] }), _jsxs(Text, { children: [themeManager.secondary('Status: '), getStatusColor(tool.status)(tool.status)] }), _jsxs(Text, { children: [themeManager.secondary('Enabled: '), tool.enabled ? themeManager.success('Yes') : themeManager.muted('No')] }), tool.serverName && (_jsxs(Text, { children: [themeManager.secondary('Server: '), themeManager.accent(tool.serverName)] })), _jsx(Text, {}), _jsx(Text, { children: themeManager.secondary('Description:') }), _jsx(Text, { children: themeManager.muted(tool.description) }), _jsx(Text, {}), _jsx(Text, { children: themeManager.secondary('Usage Statistics:') }), _jsx(Text, { children: themeManager.muted(`Used ${tool.usageCount} times`) }), tool.lastUsed && (_jsx(Text, { children: themeManager.muted(`Last used: ${tool.lastUsed.toLocaleString()}`) })), _jsx(Text, {}), _jsx(Text, { children: themeManager.secondary('Capabilities:') }), _jsx(Text, { children: themeManager.muted(`Markdown output: ${tool.isOutputMarkdown ? 'Yes' : 'No'}`) }), _jsx(Text, { children: themeManager.muted(`Live updates: ${tool.canUpdateOutput ? 'Yes' : 'No'}`) }), tool.errorMessage && (_jsxs(_Fragment, { children: [_jsx(Text, {}), _jsx(Text, { children: themeManager.error(`Error: ${tool.errorMessage}`) })] }))] })] }));
    };
    const getStatusIcon = (status) => {
        switch (status) {
            case 'active': return '✓';
            case 'inactive': return '○';
            case 'error': return '✗';
            case 'loading': return '⟳';
            default: return '?';
        }
    };
    const getStatusColor = (status) => {
        switch (status) {
            case 'active': return themeManager.success;
            case 'inactive': return themeManager.muted;
            case 'error': return themeManager.error;
            case 'loading': return themeManager.warning;
            default: return themeManager.muted;
        }
    };
    const renderControls = () => (_jsx(Box, { marginTop: 1, borderStyle: "single", borderColor: "gray", padding: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.muted('Controls:') }), mode === 'list' && (_jsxs(_Fragment, { children: [_jsx(Text, { children: themeManager.muted('  ↑↓ Navigate • Enter Details • Space Toggle • Tab Filter') }), _jsx(Text, { children: themeManager.muted('  Ctrl+R Refresh • Ctrl+T Test • Esc Close') })] })), mode === 'details' && (_jsx(Text, { children: themeManager.muted('  S Settings • T Test • Esc Back') }))] }) }));
    return (_jsx(Box, { flexDirection: "column", padding: 1, children: _jsx(Box, { borderStyle: "round", borderColor: "blue", padding: 1, children: _jsxs(Box, { flexDirection: "column", width: "100%", children: [_jsx(Text, { children: themeManager.primary('🔧 Tool Manager') }), _jsx(Text, {}), mode === 'list' && renderToolsList(), mode === 'details' && renderToolDetails(), renderControls()] }) }) }));
};
//# sourceMappingURL=ToolManager.js.map