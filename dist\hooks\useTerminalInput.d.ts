/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { TerminalInputManager, TerminalInputOptions, TerminalInputState } from '../terminal/TerminalInputManager.js';
export interface UseTerminalInputOptions extends TerminalInputOptions {
    onSubmit?: (value: string) => void;
    onEscape?: () => void;
    onPasteAccepted?: (data: {
        originalText: string;
        filteredText: string;
        newValue: string;
    }) => void;
    onPasteRejected?: (data: {
        text: string;
        reason: string;
    }) => void;
    onValidationChanged?: (data: {
        isValid: boolean;
        value: string;
    }) => void;
    disabled?: boolean;
}
export interface UseTerminalInputReturn {
    state: TerminalInputState;
    manager: TerminalInputManager;
    clear: () => void;
    setValue: (value: string) => void;
    setDisabled: (disabled: boolean) => void;
    updateOptions: (options: Partial<TerminalInputOptions>) => void;
}
/**
 * React hook that provides advanced terminal input functionality using TerminalInputManager
 */
export declare const useTerminalInput: (options?: UseTerminalInputOptions) => UseTerminalInputReturn;
/**
 * Specialized hook for API key input with built-in validation
 */
export declare const useApiKeyTerminalInput: (provider: string, options?: Omit<UseTerminalInputOptions, "masked" | "validator">) => UseTerminalInputReturn;
/**
 * Specialized hook for chat input with enhanced paste handling
 */
export declare const useChatTerminalInput: (options?: UseTerminalInputOptions) => UseTerminalInputReturn;
//# sourceMappingURL=useTerminalInput.d.ts.map