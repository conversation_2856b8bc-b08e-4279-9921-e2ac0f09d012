/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import fs from 'fs';
import path from 'path';
import os from 'os';
import { ArienError } from '../utils/errors.js';
import { ProviderType } from '../providers/manager.js';

export interface StoredCredentials {
  providers: Record<ProviderType, {
    apiKey?: string;
    model?: string;
    baseUrl?: string;
    lastUsed?: number;
  }>;
  defaultProvider?: ProviderType;
  lastUpdated: number;
}

export class CredentialsManager {
  private credentialsPath: string;
  private credentials: StoredCredentials;

  constructor() {
    const configDir = path.join(os.homedir(), '.arien');
    this.credentialsPath = path.join(configDir, 'credentials.json');
    this.credentials = this.loadCredentials();
  }

  /**
   * Loads credentials from disk
   */
  private loadCredentials(): StoredCredentials {
    try {
      if (fs.existsSync(this.credentialsPath)) {
        const data = fs.readFileSync(this.credentialsPath, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
      console.warn('Failed to load credentials:', error);
    }

    // Return default structure
    return {
      providers: {
        google: {},
        openai: {},
        deepseek: {},
        anthropic: {},
      },
      lastUpdated: Date.now(),
    };
  }

  /**
   * Saves credentials to disk
   */
  private saveCredentials(): void {
    try {
      const configDir = path.dirname(this.credentialsPath);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }

      this.credentials.lastUpdated = Date.now();
      fs.writeFileSync(this.credentialsPath, JSON.stringify(this.credentials, null, 2));
    } catch (error) {
      throw new ArienError(
        `Failed to save credentials: ${error instanceof Error ? error.message : String(error)}`,
        'CREDENTIALS_SAVE_ERROR'
      );
    }
  }

  /**
   * Sets API key for a provider
   */
  setApiKey(provider: ProviderType, apiKey: string): void {
    if (!this.credentials.providers[provider]) {
      this.credentials.providers[provider] = {};
    }
    
    this.credentials.providers[provider].apiKey = apiKey;
    this.credentials.providers[provider].lastUsed = Date.now();
    this.saveCredentials();
  }

  /**
   * Gets API key for a provider
   */
  getApiKey(provider: ProviderType): string | undefined {
    return this.credentials.providers[provider]?.apiKey;
  }

  /**
   * Sets model for a provider
   */
  setModel(provider: ProviderType, model: string): void {
    if (!this.credentials.providers[provider]) {
      this.credentials.providers[provider] = {};
    }
    
    this.credentials.providers[provider].model = model;
    this.credentials.providers[provider].lastUsed = Date.now();
    this.saveCredentials();
  }

  /**
   * Gets model for a provider
   */
  getModel(provider: ProviderType): string | undefined {
    return this.credentials.providers[provider]?.model;
  }

  /**
   * Sets base URL for a provider
   */
  setBaseUrl(provider: ProviderType, baseUrl: string): void {
    if (!this.credentials.providers[provider]) {
      this.credentials.providers[provider] = {};
    }
    
    this.credentials.providers[provider].baseUrl = baseUrl;
    this.credentials.providers[provider].lastUsed = Date.now();
    this.saveCredentials();
  }

  /**
   * Gets base URL for a provider
   */
  getBaseUrl(provider: ProviderType): string | undefined {
    return this.credentials.providers[provider]?.baseUrl;
  }

  /**
   * Sets default provider
   */
  setDefaultProvider(provider: ProviderType): void {
    this.credentials.defaultProvider = provider;
    this.saveCredentials();
  }

  /**
   * Gets default provider
   */
  getDefaultProvider(): ProviderType | undefined {
    return this.credentials.defaultProvider;
  }

  /**
   * Checks if a provider is configured
   */
  isProviderConfigured(provider: ProviderType): boolean {
    const providerConfig = this.credentials.providers[provider];
    return !!(providerConfig?.apiKey && providerConfig?.model);
  }

  /**
   * Gets all configured providers
   */
  getConfiguredProviders(): ProviderType[] {
    return (Object.keys(this.credentials.providers) as ProviderType[])
      .filter(provider => this.isProviderConfigured(provider));
  }

  /**
   * Gets provider configuration
   */
  getProviderConfig(provider: ProviderType): {
    apiKey?: string;
    model?: string;
    baseUrl?: string;
    lastUsed?: number;
  } {
    return this.credentials.providers[provider] || {};
  }

  /**
   * Removes provider configuration
   */
  removeProvider(provider: ProviderType): void {
    this.credentials.providers[provider] = {};
    
    // If this was the default provider, clear it
    if (this.credentials.defaultProvider === provider) {
      this.credentials.defaultProvider = undefined;
    }
    
    this.saveCredentials();
  }

  /**
   * Clears all credentials
   */
  clearAll(): void {
    this.credentials = {
      providers: {
        google: {},
        openai: {},
        deepseek: {},
        anthropic: {},
      },
      lastUpdated: Date.now(),
    };
    this.saveCredentials();
  }

  /**
   * Gets the most recently used provider
   */
  getMostRecentProvider(): ProviderType | undefined {
    let mostRecent: ProviderType | undefined;
    let mostRecentTime = 0;

    for (const [provider, config] of Object.entries(this.credentials.providers)) {
      if (config.lastUsed && config.lastUsed > mostRecentTime && this.isProviderConfigured(provider as ProviderType)) {
        mostRecent = provider as ProviderType;
        mostRecentTime = config.lastUsed;
      }
    }

    return mostRecent;
  }

  /**
   * Checks if there are any valid credentials configured
   */
  hasValidCredentials(): boolean {
    const configuredProviders = this.getConfiguredProviders();
    return configuredProviders.length > 0;
  }

  /**
   * Validates API key format for a provider
   */
  validateApiKey(provider: ProviderType, apiKey: string): boolean {
    switch (provider) {
      case 'google':
        return apiKey.startsWith('AIza') && apiKey.length === 39;
      case 'openai':
        return apiKey.startsWith('sk-') && apiKey.length >= 48;
      case 'deepseek':
        return apiKey.startsWith('sk-') && apiKey.length >= 48;
      case 'anthropic':
        return apiKey.startsWith('sk-ant-') && apiKey.length >= 50;
      default:
        return apiKey.length > 0;
    }
  }
}
