/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
export interface UseInputOptions {
    onSubmit: (input: string) => void;
    onClear?: () => void;
    onHelp?: () => void;
    onExit?: () => void;
    onPaste?: (pastedText: string) => void;
    disabled?: boolean;
    multiline?: boolean;
    placeholder?: string;
    masked?: boolean;
    maskChar?: string;
    validator?: (input: string) => boolean;
    characterFilter?: (char: string) => boolean;
    maxLength?: number;
}
export interface UseInputReturn {
    input: string;
    displayValue: string;
    setInput: (input: string) => void;
    clearInput: () => void;
    isDisabled: boolean;
    isPasteDetected: boolean;
}
export declare const useInput: ({ onSubmit, onClear, onHelp, onExit, onPaste, disabled, masked, maskChar, validator, characterFilter, maxLength, }: UseInputOptions) => UseInputReturn;
//# sourceMappingURL=useInput.d.ts.map