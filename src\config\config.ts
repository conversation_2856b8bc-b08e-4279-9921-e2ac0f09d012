/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { FileDiscoveryService } from '../services/fileDiscoveryService.js';
import { GeminiClient } from '../core/client.js';

export enum ApprovalMode {
  DEFAULT = 'default',
  ALWAYS = 'always',
  NEVER = 'never',
  AUTO_EDIT = 'auto_edit',
}

export interface MCPServerConfig {
  command?: string;
  args?: string[];
  url?: string;
  httpUrl?: string;
  cwd?: string;
  env?: Record<string, string>;
  headers?: Record<string, string>;
  timeout?: number;
  trust?: boolean;
}

export interface ConfigParameters {
  cwd: string;
  model: string;
  embeddingModel: string;
  sandbox: boolean | undefined;
  targetDir: string;
  debugMode: boolean;
  userMemory: string;
  arienMdFileCount: number;
  approvalMode: ApprovalMode;
  sessionId: string;
  apiKey?: string;
  provider?: string;
  temperature?: number;
  maxTokens?: number;
  toolDiscoveryCommand?: string;
  toolCallCommand?: string;
  mcpServerCommand?: string;
  mcpServers?: Record<string, MCPServerConfig>;
  fileFilteringRespectGitIgnore?: boolean;
  fullContext?: boolean;
  question?: string;
}

export class Config {
  private params: ConfigParameters;
  private fileService: FileDiscoveryService;
  private geminiClient: GeminiClient | null = null;

  constructor(params: ConfigParameters) {
    this.params = params;
    this.fileService = new FileDiscoveryService(params.targetDir);
  }

  // Core getters
  getCwd(): string {
    return this.params.cwd;
  }

  getModel(): string {
    return this.params.model;
  }

  getEmbeddingModel(): string {
    return this.params.embeddingModel;
  }

  getSandbox(): boolean | undefined {
    return this.params.sandbox;
  }

  getTargetDir(): string {
    return this.params.targetDir;
  }

  getDebugMode(): boolean {
    return this.params.debugMode;
  }

  getUserMemory(): string {
    return this.params.userMemory;
  }

  getArienMdFileCount(): number {
    return this.params.arienMdFileCount;
  }

  getApprovalMode(): ApprovalMode {
    return this.params.approvalMode;
  }

  setApprovalMode(mode: ApprovalMode): void {
    this.params.approvalMode = mode;
  }

  getSessionId(): string {
    return this.params.sessionId;
  }

  getApiKey(): string | undefined {
    return this.params.apiKey;
  }

  getProvider(): string | undefined {
    return this.params.provider;
  }

  getTemperature(): number | undefined {
    return this.params.temperature;
  }

  getMaxTokens(): number | undefined {
    return this.params.maxTokens;
  }

  // Tool discovery
  getToolDiscoveryCommand(): string | undefined {
    return this.params.toolDiscoveryCommand;
  }

  getToolCallCommand(): string | undefined {
    return this.params.toolCallCommand;
  }

  // MCP configuration
  getMcpServerCommand(): string | undefined {
    return this.params.mcpServerCommand;
  }

  getMcpServers(): Record<string, MCPServerConfig> | undefined {
    return this.params.mcpServers;
  }

  // File filtering
  getFileFilteringRespectGitIgnore(): boolean {
    return this.params.fileFilteringRespectGitIgnore ?? true;
  }

  getFullContext(): boolean {
    return this.params.fullContext ?? false;
  }

  getQuestion(): string | undefined {
    return this.params.question;
  }

  // Services
  getFileService(): FileDiscoveryService {
    return this.fileService;
  }

  getGeminiClient(): GeminiClient {
    if (!this.geminiClient) {
      this.geminiClient = new GeminiClient(this);
    }
    return this.geminiClient;
  }

  // Update methods
  updateParams(updates: Partial<ConfigParameters>): void {
    this.params = { ...this.params, ...updates };
  }
}
