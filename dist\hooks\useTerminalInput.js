/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useEffect, useCallback, useRef } from 'react';
import { useInput as useInkInput } from 'ink';
import { TerminalInputManager } from '../terminal/TerminalInputManager.js';
/**
 * React hook that provides advanced terminal input functionality using TerminalInputManager
 */
export const useTerminalInput = (options = {}) => {
    const managerRef = useRef(null);
    const [state, setState] = useState({
        value: '',
        displayValue: '',
        isValid: true,
        validationMessage: '',
        isPasteDetected: false,
        cursorPosition: 0,
        isDisabled: options.disabled || false,
    });
    // Initialize manager
    useEffect(() => {
        const { onSubmit, onEscape, onPasteAccepted, onPasteRejected, onValidationChanged, disabled, ...managerOptions } = options;
        managerRef.current = new TerminalInputManager(managerOptions);
        // Set initial disabled state
        if (disabled) {
            managerRef.current.setDisabled(disabled);
        }
        // Set up event listeners
        const manager = managerRef.current;
        const handleValueChanged = () => {
            setState(manager.getState());
        };
        const handleSubmit = (data) => {
            if (onSubmit) {
                onSubmit(data.value);
            }
        };
        const handleEscape = () => {
            if (onEscape) {
                onEscape();
            }
        };
        const handlePasteAccepted = (data) => {
            if (onPasteAccepted) {
                onPasteAccepted(data);
            }
        };
        const handlePasteRejected = (data) => {
            if (onPasteRejected) {
                onPasteRejected(data);
            }
        };
        const handleValidationChanged = (data) => {
            if (onValidationChanged) {
                onValidationChanged(data);
            }
        };
        const handlePasteDetectionChanged = () => {
            setState(manager.getState());
        };
        const handleDisabledChanged = () => {
            setState(manager.getState());
        };
        // Register event listeners
        manager.on('valueChanged', handleValueChanged);
        manager.on('submit', handleSubmit);
        manager.on('escape', handleEscape);
        manager.on('pasteAccepted', handlePasteAccepted);
        manager.on('pasteRejected', handlePasteRejected);
        manager.on('validationChanged', handleValidationChanged);
        manager.on('pasteDetectionChanged', handlePasteDetectionChanged);
        manager.on('disabledChanged', handleDisabledChanged);
        // Initial state update
        setState(manager.getState());
        // Cleanup
        return () => {
            manager.destroy();
        };
    }, []); // Empty dependency array - manager is created once
    // Update options when they change
    useEffect(() => {
        if (managerRef.current) {
            const { onSubmit, onEscape, onPasteAccepted, onPasteRejected, onValidationChanged, disabled, ...managerOptions } = options;
            managerRef.current.updateOptions(managerOptions);
            if (disabled !== undefined) {
                managerRef.current.setDisabled(disabled);
            }
        }
    }, [options]);
    // Integrate with Ink's useInput
    useInkInput((inputChar, key) => {
        if (managerRef.current) {
            managerRef.current.processInput(inputChar, key);
        }
    });
    // Memoized functions
    const clear = useCallback(() => {
        if (managerRef.current) {
            managerRef.current.clear();
        }
    }, []);
    const setValue = useCallback((value) => {
        if (managerRef.current) {
            managerRef.current.setValue(value);
        }
    }, []);
    const setDisabled = useCallback((disabled) => {
        if (managerRef.current) {
            managerRef.current.setDisabled(disabled);
        }
    }, []);
    const updateOptions = useCallback((newOptions) => {
        if (managerRef.current) {
            managerRef.current.updateOptions(newOptions);
        }
    }, []);
    return {
        state,
        manager: managerRef.current,
        clear,
        setValue,
        setDisabled,
        updateOptions,
    };
};
/**
 * Specialized hook for API key input with built-in validation
 */
export const useApiKeyTerminalInput = (provider, options = {}) => {
    const apiKeyValidators = {
        google: (key) => key.startsWith('AIza') && key.length === 39,
        openai: (key) => key.startsWith('sk-') && key.length >= 48,
        deepseek: (key) => key.startsWith('sk-') && key.length >= 48,
        anthropic: (key) => key.startsWith('sk-ant-') && key.length >= 50,
    };
    const validator = apiKeyValidators[provider] || ((key) => key.length > 0);
    const characterFilter = (char) => {
        const charCode = char.charCodeAt(0);
        return ((charCode >= 48 && charCode <= 57) || // 0-9
            (charCode >= 65 && charCode <= 90) || // A-Z
            (charCode >= 97 && charCode <= 122) || // a-z
            charCode === 45 || // -
            charCode === 95 // _
        );
    };
    return useTerminalInput({
        ...options,
        masked: true,
        maskChar: '*',
        validator,
        characterFilter,
        maxLength: 100,
    });
};
/**
 * Specialized hook for chat input with enhanced paste handling
 */
export const useChatTerminalInput = (options = {}) => {
    return useTerminalInput({
        ...options,
        maxLength: 10000, // Allow long messages
        realTimeValidation: false, // Don't validate chat messages in real-time
    });
};
//# sourceMappingURL=useTerminalInput.js.map