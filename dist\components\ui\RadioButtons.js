import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput } from 'ink';
export const RadioButtons = ({ themeManager, options, selectedValue, onSelectionChange, onSubmit, disabled = false, showDescriptions = true, showIndicators = true, allowDeselect = false, variant = 'default', title, helpText, width, maxHeight, scrollable = true, }) => {
    const [selectedIndex, setSelectedIndex] = useState(() => {
        if (selectedValue) {
            const index = options.findIndex(option => option.value === selectedValue);
            return index >= 0 ? index : -1;
        }
        return -1;
    });
    const [scrollOffset, setScrollOffset] = useState(0);
    // Update selected index when selectedValue prop changes
    useEffect(() => {
        if (selectedValue) {
            const index = options.findIndex(option => option.value === selectedValue);
            setSelectedIndex(index >= 0 ? index : -1);
        }
        else {
            setSelectedIndex(-1);
        }
    }, [selectedValue, options]);
    // Handle scrolling for large lists
    const visibleOptions = maxHeight && scrollable ?
        options.slice(scrollOffset, scrollOffset + maxHeight) :
        options;
    const handleSelectionChange = useCallback((index) => {
        if (index >= 0 && index < options.length) {
            const option = options[index];
            if (!option.disabled) {
                const newIndex = allowDeselect && selectedIndex === index ? -1 : index;
                setSelectedIndex(newIndex);
                if (newIndex >= 0 && onSelectionChange) {
                    onSelectionChange(option.value, option);
                }
                else if (newIndex === -1 && onSelectionChange && allowDeselect) {
                    onSelectionChange('', {});
                }
            }
        }
    }, [selectedIndex, options, onSelectionChange, allowDeselect]);
    const handleSubmit = useCallback(() => {
        if (selectedIndex >= 0 && selectedIndex < options.length) {
            const option = options[selectedIndex];
            if (!option.disabled && onSubmit) {
                onSubmit(option.value, option);
            }
        }
    }, [selectedIndex, options, onSubmit]);
    useInput((input, key) => {
        if (disabled || options.length === 0)
            return;
        if (key.upArrow) {
            const newIndex = selectedIndex > 0 ? selectedIndex - 1 : options.length - 1;
            handleSelectionChange(newIndex);
            // Handle scrolling
            if (scrollable && maxHeight && newIndex < scrollOffset) {
                setScrollOffset(Math.max(0, newIndex));
            }
        }
        else if (key.downArrow) {
            const newIndex = selectedIndex < options.length - 1 ? selectedIndex + 1 : 0;
            handleSelectionChange(newIndex);
            // Handle scrolling
            if (scrollable && maxHeight && newIndex >= scrollOffset + maxHeight) {
                setScrollOffset(Math.min(options.length - maxHeight, newIndex - maxHeight + 1));
            }
        }
        else if (key.return) {
            handleSubmit();
        }
        else if (input === ' ') {
            // Space bar toggles selection
            handleSelectionChange(selectedIndex);
        }
        // Handle number key selection (1-9)
        const numKey = parseInt(input);
        if (!isNaN(numKey) && numKey >= 1 && numKey <= Math.min(9, options.length)) {
            handleSelectionChange(numKey - 1);
        }
    });
    const getIndicator = (isSelected, option) => {
        if (!showIndicators)
            return '';
        if (option.disabled) {
            return themeManager.muted('○ ');
        }
        if (isSelected) {
            return themeManager.primary('● ');
        }
        return themeManager.muted('○ ');
    };
    const getOptionText = (option, isSelected, isFocused) => {
        let text = option.label;
        if (option.disabled) {
            return themeManager.muted(text);
        }
        if (isSelected) {
            return themeManager.primary(text);
        }
        if (isFocused) {
            return themeManager.highlight(text);
        }
        return text;
    };
    const renderOption = (option, actualIndex) => {
        const isSelected = selectedIndex === actualIndex;
        const isFocused = selectedIndex === actualIndex;
        const indicator = getIndicator(isSelected, option);
        const optionText = getOptionText(option, isSelected, isFocused);
        const optionContent = (_jsxs(Box, { flexDirection: "column", children: [_jsxs(Box, { children: [_jsx(Text, { children: indicator }), _jsx(Text, { children: optionText }), variant !== 'compact' && showDescriptions && option.description && (_jsxs(Text, { children: [" - ", themeManager.muted(option.description)] }))] }), variant === 'detailed' && showDescriptions && option.description && (_jsx(Box, { marginLeft: 4, children: _jsx(Text, { children: themeManager.muted(option.description) }) }))] }, actualIndex));
        return optionContent;
    };
    const renderTitle = () => {
        if (!title)
            return null;
        return (_jsx(Box, { marginBottom: 1, children: _jsx(Text, { children: themeManager.primary(title) }) }));
    };
    const renderHelpText = () => {
        if (!helpText)
            return null;
        return (_jsx(Box, { marginTop: 1, children: _jsx(Text, { children: themeManager.muted(helpText) }) }));
    };
    const renderScrollIndicators = () => {
        if (!scrollable || !maxHeight || options.length <= maxHeight)
            return null;
        const hasMore = scrollOffset + maxHeight < options.length;
        const hasPrevious = scrollOffset > 0;
        return (_jsxs(Box, { justifyContent: "space-between", children: [_jsx(Text, { children: hasPrevious ? themeManager.muted('↑ More above') : '' }), _jsx(Text, { children: hasMore ? themeManager.muted('↓ More below') : '' })] }));
    };
    if (options.length === 0) {
        return (_jsxs(Box, { flexDirection: "column", children: [renderTitle(), _jsx(Text, { children: themeManager.muted('No options available') }), renderHelpText()] }));
    }
    return (_jsxs(Box, { flexDirection: "column", width: width, children: [renderTitle(), renderScrollIndicators(), _jsx(Box, { flexDirection: "column", children: visibleOptions.map((option, index) => {
                    const actualIndex = scrollable && maxHeight ? scrollOffset + index : index;
                    return renderOption(option, actualIndex);
                }) }), renderScrollIndicators(), renderHelpText()] }));
};
// Convenience components for common use cases
export const CompactRadioButtons = (props) => (_jsx(RadioButtons, { ...props, variant: "compact" }));
export const DetailedRadioButtons = (props) => (_jsx(RadioButtons, { ...props, variant: "detailed" }));
//# sourceMappingURL=RadioButtons.js.map