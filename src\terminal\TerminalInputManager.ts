/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { EventEmitter } from 'events';

export interface TerminalInputEvent {
  type: 'character' | 'paste' | 'special';
  data: string;
  key?: {
    ctrl?: boolean;
    meta?: boolean;
    shift?: boolean;
    return?: boolean;
    backspace?: boolean;
    delete?: boolean;
    escape?: boolean;
    upArrow?: boolean;
    downArrow?: boolean;
    leftArrow?: boolean;
    rightArrow?: boolean;
  };
  timestamp: number;
}

export interface TerminalInputOptions {
  masked?: boolean;
  maskChar?: string;
  maxLength?: number;
  validator?: (input: string) => boolean;
  characterFilter?: (char: string) => boolean;
  pasteValidator?: (pastedText: string) => boolean;
  realTimeValidation?: boolean;
}

export interface TerminalInputState {
  value: string;
  displayValue: string;
  isValid: boolean;
  validationMessage: string;
  isPasteDetected: boolean;
  cursorPosition: number;
  isDisabled: boolean;
}

/**
 * Centralized terminal input manager for consistent input handling across the application
 */
export class TerminalInputManager extends EventEmitter {
  private state: TerminalInputState;
  private options: TerminalInputOptions;
  private pasteDetectionTimeout: NodeJS.Timeout | null = null;

  constructor(options: TerminalInputOptions = {}) {
    super();
    this.options = {
      masked: false,
      maskChar: '*',
      maxLength: undefined,
      validator: undefined,
      characterFilter: this.defaultCharacterFilter,
      pasteValidator: undefined,
      realTimeValidation: true,
      ...options,
    };

    this.state = {
      value: '',
      displayValue: '',
      isValid: true,
      validationMessage: '',
      isPasteDetected: false,
      cursorPosition: 0,
      isDisabled: false,
    };
  }

  /**
   * Default character filter for printable ASCII characters
   */
  private defaultCharacterFilter = (char: string): boolean => {
    const charCode = char.charCodeAt(0);
    return charCode >= 32 && charCode <= 126;
  };

  /**
   * Process input event from terminal
   */
  processInput(inputChar: string, key: any): boolean {
    if (this.state.isDisabled) {
      return false;
    }

    const event: TerminalInputEvent = {
      type: this.determineInputType(inputChar, key),
      data: inputChar,
      key,
      timestamp: Date.now(),
    };

    this.emit('input', event);

    switch (event.type) {
      case 'paste':
        return this.handlePaste(inputChar);
      case 'character':
        return this.handleCharacter(inputChar);
      case 'special':
        return this.handleSpecialKey(key);
      default:
        return false;
    }
  }

  /**
   * Determine the type of input event
   */
  private determineInputType(inputChar: string, key: any): 'character' | 'paste' | 'special' {
    if (key.ctrl || key.meta || key.return || key.backspace || key.delete || key.escape || 
        key.upArrow || key.downArrow || key.leftArrow || key.rightArrow) {
      return 'special';
    }

    if (inputChar && inputChar.length > 1) {
      return 'paste';
    }

    if (inputChar && inputChar.length === 1) {
      return 'character';
    }

    return 'special';
  }

  /**
   * Handle paste operations
   */
  private handlePaste(pastedText: string): boolean {
    // Apply paste validator if provided
    if (this.options.pasteValidator && !this.options.pasteValidator(pastedText)) {
      this.emit('pasteRejected', { text: pastedText, reason: 'validation_failed' });
      return false;
    }

    // Filter the pasted content
    const filteredContent = pastedText
      .split('')
      .filter(this.options.characterFilter || this.defaultCharacterFilter)
      .join('');

    if (!filteredContent) {
      this.emit('pasteRejected', { text: pastedText, reason: 'no_valid_characters' });
      return false;
    }

    // Apply max length limit
    const availableSpace = this.options.maxLength 
      ? this.options.maxLength - this.state.value.length
      : filteredContent.length;
    
    const limitedContent = filteredContent.slice(0, availableSpace);

    // Apply validator if provided
    const newValue = this.state.value + limitedContent;
    if (this.options.validator && !this.options.validator(newValue)) {
      this.emit('pasteRejected', { text: pastedText, reason: 'validation_failed' });
      return false;
    }

    // Update state
    this.updateValue(newValue);
    this.setPasteDetected(true);

    this.emit('pasteAccepted', { 
      originalText: pastedText, 
      filteredText: limitedContent,
      newValue: newValue 
    });

    return true;
  }

  /**
   * Handle single character input
   */
  private handleCharacter(char: string): boolean {
    // Apply character filter
    if (this.options.characterFilter && !this.options.characterFilter(char)) {
      this.emit('characterRejected', { char, reason: 'filter_failed' });
      return false;
    }

    // Check max length
    if (this.options.maxLength && this.state.value.length >= this.options.maxLength) {
      this.emit('characterRejected', { char, reason: 'max_length_exceeded' });
      return false;
    }

    // Apply validator if provided
    const newValue = this.state.value + char;
    if (this.options.validator && !this.options.validator(newValue)) {
      this.emit('characterRejected', { char, reason: 'validation_failed' });
      return false;
    }

    // Update state
    this.updateValue(newValue);
    this.setPasteDetected(false);

    this.emit('characterAccepted', { char, newValue });

    return true;
  }

  /**
   * Handle special key operations
   */
  private handleSpecialKey(key: any): boolean {
    if (key.backspace || key.delete) {
      const newValue = this.state.value.slice(0, -1);
      this.updateValue(newValue);
      this.setPasteDetected(false);
      this.emit('backspace', { newValue });
      return true;
    }

    if (key.return) {
      this.emit('submit', { value: this.state.value });
      return true;
    }

    if (key.escape) {
      this.emit('escape');
      return true;
    }

    // Handle other special keys
    this.emit('specialKey', { key });
    return false;
  }

  /**
   * Update the input value and display value
   */
  private updateValue(newValue: string): void {
    this.state.value = newValue;
    this.state.displayValue = this.options.masked 
      ? (this.options.maskChar || '*').repeat(newValue.length)
      : newValue;
    
    this.state.cursorPosition = newValue.length;

    // Perform real-time validation if enabled
    if (this.options.realTimeValidation) {
      this.validateInput();
    }

    this.emit('valueChanged', { 
      value: this.state.value, 
      displayValue: this.state.displayValue 
    });
  }

  /**
   * Set paste detection state
   */
  private setPasteDetected(detected: boolean): void {
    this.state.isPasteDetected = detected;

    // Clear paste detection after a delay
    if (detected) {
      if (this.pasteDetectionTimeout) {
        clearTimeout(this.pasteDetectionTimeout);
      }
      this.pasteDetectionTimeout = setTimeout(() => {
        this.state.isPasteDetected = false;
        this.emit('pasteDetectionCleared');
      }, 2000);
    }

    this.emit('pasteDetectionChanged', { isPasteDetected: detected });
  }

  /**
   * Validate the current input
   */
  private validateInput(): void {
    const isValid = this.options.validator 
      ? this.options.validator(this.state.value)
      : true;

    this.state.isValid = isValid;
    this.emit('validationChanged', { 
      isValid, 
      value: this.state.value 
    });
  }

  /**
   * Get current state
   */
  getState(): Readonly<TerminalInputState> {
    return { ...this.state };
  }

  /**
   * Clear the input
   */
  clear(): void {
    this.updateValue('');
    this.setPasteDetected(false);
    this.emit('cleared');
  }

  /**
   * Set the input value programmatically
   */
  setValue(value: string): void {
    this.updateValue(value);
  }

  /**
   * Enable or disable the input
   */
  setDisabled(disabled: boolean): void {
    this.state.isDisabled = disabled;
    this.emit('disabledChanged', { isDisabled: disabled });
  }

  /**
   * Update options
   */
  updateOptions(newOptions: Partial<TerminalInputOptions>): void {
    this.options = { ...this.options, ...newOptions };
    
    // Re-validate if real-time validation is enabled
    if (this.options.realTimeValidation) {
      this.validateInput();
    }

    this.emit('optionsChanged', { options: this.options });
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.pasteDetectionTimeout) {
      clearTimeout(this.pasteDetectionTimeout);
    }
    this.removeAllListeners();
  }
}
