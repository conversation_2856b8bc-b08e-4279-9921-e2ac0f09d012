/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import React from 'react';
import { ThemeManager } from '../../themes/themes.js';
interface ProgressBarProps {
    themeManager: ThemeManager;
    progress: number;
    width?: number;
    showPercentage?: boolean;
    showLabel?: boolean;
    label?: string;
    variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
    animated?: boolean;
    indeterminate?: boolean;
}
export declare const ProgressBar: React.FC<ProgressBarProps>;
export declare const SuccessProgressBar: React.FC<Omit<ProgressBarProps, 'variant'>>;
export declare const WarningProgressBar: React.FC<Omit<ProgressBarProps, 'variant'>>;
export declare const ErrorProgressBar: React.FC<Omit<ProgressBarProps, 'variant'>>;
export declare const InfoProgressBar: React.FC<Omit<ProgressBarProps, 'variant'>>;
export declare const IndeterminateProgressBar: React.FC<Omit<ProgressBarProps, 'indeterminate'>>;
export {};
//# sourceMappingURL=ProgressBar.d.ts.map