/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { ThemeManager } from '../../themes/themes.js';
import { CredentialsManager } from '../../auth/credentials.js';
import { ProviderManager, ProviderType } from '../../providers/manager.js';
import { Header } from '../ui/Header.js';
import { LoadingIndicator } from '../ui/LoadingIndicator.js';
import { RadioButtons, RadioOption } from '../ui/RadioButtons.js';
import { MaxBoxSized } from '../ui/MaxBoxSized.js';
import { useApiKeyInput } from '../../hooks/useApiKeyInput.js';

interface AuthScreenProps {
  themeManager: ThemeManager;
  credentialsManager: CredentialsManager;
  providerManager: ProviderManager;
  onAuthComplete: () => void;
}

type AuthStep = 'provider' | 'model' | 'apikey' | 'testing';

export const AuthScreen: React.FC<AuthScreenProps> = ({
  themeManager,
  credentialsManager,
  providerManager,
  onAuthComplete,
}) => {
  const [step, setStep] = useState<AuthStep>('provider');
  const [selectedProvider, setSelectedProvider] = useState<ProviderType | null>(null);
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [testResult, setTestResult] = useState<string | null>(null);
  const [isPasteDetected, setIsPasteDetected] = useState(false);

  const providers = providerManager.getAvailableProviders();
  const models = selectedProvider ? providerManager.getModelsForProvider(selectedProvider) : [];

  // Convert providers to RadioOptions
  const providerOptions: RadioOption[] = providers.map(provider => ({
    value: provider.type,
    label: provider.name,
    description: `${provider.models.length} models available`,
  }));

  // Convert models to RadioOptions
  const modelOptions: RadioOption[] = models.map(model => ({
    value: model,
    label: model,
    description: `Model: ${model}`,
  }));

  // API key input hook
  const apiKeyInput = useApiKeyInput({
    provider: selectedProvider || 'openai',
    onSubmit: (input: string) => {
      if (input.trim()) {
        setStep('testing');
        testConnection();
      }
    },
    onPaste: (_pastedText: string) => {
      setIsPasteDetected(true);
      // Reset paste detection after a short delay
      setTimeout(() => setIsPasteDetected(false), 2000);
    },
    disabled: step !== 'apikey',
  });

  // Check if we already have configured providers
  useEffect(() => {
    const configuredProviders = credentialsManager.getConfiguredProviders();
    if (configuredProviders.length > 0) {
      const defaultProvider = credentialsManager.getDefaultProvider() || configuredProviders[0];
      const config = credentialsManager.getProviderConfig(defaultProvider);
      
      if (config.apiKey && config.model) {
        // Auto-proceed if we have valid configuration
        onAuthComplete();
        return;
      }
    }
  }, [credentialsManager, onAuthComplete]);

  // Handler for provider selection
  const handleProviderSelection = (value: string, _option: RadioOption) => {
    setSelectedProvider(value as ProviderType);
  };

  const handleProviderSubmit = (value: string, _option: RadioOption) => {
    setSelectedProvider(value as ProviderType);
    setStep('model');
  };

  // Handler for model selection
  const handleModelSelection = (value: string, _option: RadioOption) => {
    setSelectedModel(value);
  };

  const handleModelSubmit = (value: string, _option: RadioOption) => {
    setSelectedModel(value);
    setStep('apikey');
    apiKeyInput.clearInput();
  };

  useInput((_inputChar, key) => {
    if (step === 'apikey') {
      if (key.escape) {
        setStep('model');
        apiKeyInput.clearInput();
      }
      // API key input is now handled by the useApiKeyTerminalInput hook
    } else if (step === 'testing') {
      if (key.return && testResult) {
        if (testResult === 'success') {
          onAuthComplete();
        } else {
          setStep('apikey');
          apiKeyInput.clearInput();
          setTestResult(null);
        }
      } else if (key.escape) {
        setStep('apikey');
        apiKeyInput.clearInput();
        setTestResult(null);
      }
    }
  });

  const testConnection = async () => {
    const currentApiKey = apiKeyInput.input;
    if (!selectedProvider || !selectedModel || !currentApiKey) return;

    setIsTestingConnection(true);
    setTestResult(null);

    try {
      // Validate API key format using the input hook's validation
      if (!apiKeyInput.isValidFormat) {
        setTestResult('invalid_format');
        setIsTestingConnection(false);
        return;
      }

      // Create provider and test connection
      const providerId = providerManager.createProvider(selectedProvider, {
        apiKey: currentApiKey,
        model: selectedModel,
      });

      const isConnected = await providerManager.testProvider(providerId);

      if (isConnected) {
        // Save credentials
        credentialsManager.setApiKey(selectedProvider, currentApiKey);
        credentialsManager.setModel(selectedProvider, selectedModel);
        credentialsManager.setDefaultProvider(selectedProvider);

        setTestResult('success');
      } else {
        setTestResult('failed');
        providerManager.removeProvider(providerId);
      }
    } catch (error) {
      setTestResult('error');
    } finally {
      setIsTestingConnection(false);
    }
  };

  const renderProviderSelection = () => (
    <MaxBoxSized
      themeManager={themeManager}
      maxWidth={80}
      maxHeight={20}
      responsive={true}
      padding={1}
    >
      <RadioButtons
        themeManager={themeManager}
        options={providerOptions}
        selectedValue={selectedProvider || ''}
        onSelectionChange={handleProviderSelection}
        onSubmit={handleProviderSubmit}
        title="Select an AI Provider:"
        showDescriptions={true}
        variant="detailed"
        helpText="Use ↑↓ to navigate, Enter to select"
      />
    </MaxBoxSized>
  );

  const renderModelSelection = () => (
    <MaxBoxSized
      themeManager={themeManager}
      maxWidth={80}
      maxHeight={20}
      responsive={true}
      padding={1}
    >
      <RadioButtons
        themeManager={themeManager}
        options={modelOptions}
        selectedValue={selectedModel}
        onSelectionChange={handleModelSelection}
        onSubmit={handleModelSubmit}
        title={`Select a model for ${providers.find(p => p.type === selectedProvider)?.name}:`}
        showDescriptions={false}
        variant="compact"
        helpText="Use ↑↓ to navigate, Enter to select, Esc to go back"
      />
    </MaxBoxSized>
  );

  const renderApiKeyInput = () => (
    <MaxBoxSized
      themeManager={themeManager}
      maxWidth={80}
      maxHeight={25}
      responsive={true}
      padding={1}
    >
      <Box flexDirection="column">
        <Text>{themeManager.secondary(`Enter API Key for ${providers.find(p => p.type === selectedProvider)?.name}:`)}</Text>
        <Text></Text>
        <Box marginLeft={2}>
          <Text>{themeManager.primary('API Key: ')}</Text>
          <Text>{themeManager.accent(apiKeyInput.displayValue || '')}</Text>
          {apiKeyInput.input && !apiKeyInput.isDisabled && (
            <Text>{themeManager.accent('▋')}</Text>
          )}
        </Box>
        <Text></Text>

        {/* Paste detection feedback */}
        {(isPasteDetected || apiKeyInput.isPasteDetected) && (
          <Box marginLeft={2}>
            <Text>{themeManager.success('✓ Paste detected')}</Text>
          </Box>
        )}

        {/* Validation feedback */}
        {apiKeyInput.input && apiKeyInput.validationMessage && (
          <Box marginLeft={2}>
            <Text>{themeManager.warning(`⚠ ${apiKeyInput.validationMessage}`)}</Text>
          </Box>
        )}

        {/* Format validation */}
        {apiKeyInput.input && apiKeyInput.isValidFormat && (
          <Box marginLeft={2}>
            <Text>{themeManager.success('✓ Valid API key format')}</Text>
          </Box>
        )}

        <Text></Text>
        <Text>{themeManager.muted('Type or paste your API key and press Enter, Esc to go back')}</Text>
        {selectedProvider === 'google' && (
          <Text>{themeManager.info('Get your API key from: https://makersuite.google.com/app/apikey')}</Text>
        )}
        {selectedProvider === 'openai' && (
          <Text>{themeManager.info('Get your API key from: https://platform.openai.com/api-keys')}</Text>
        )}
        {selectedProvider === 'deepseek' && (
          <Text>{themeManager.info('Get your API key from: https://platform.deepseek.com/api-keys')}</Text>
        )}
        {selectedProvider === 'anthropic' && (
          <Text>{themeManager.info('Get your API key from: https://console.anthropic.com/account/keys')}</Text>
        )}
      </Box>
    </MaxBoxSized>
  );

  const renderTesting = () => (
    <MaxBoxSized
      themeManager={themeManager}
      maxWidth={80}
      maxHeight={15}
      responsive={true}
      padding={1}
    >
      <Box flexDirection="column">
        <Text>{themeManager.secondary('Testing Connection...')}</Text>
        <Text></Text>
        {isTestingConnection && (
          <Box marginLeft={2}>
            <LoadingIndicator
              themeManager={themeManager}
              text={`Connecting to ${providers.find(p => p.type === selectedProvider)?.name}`}
              type="spinner"
            />
          </Box>
        )}
        {testResult === 'success' && (
          <Box flexDirection="column" marginLeft={2}>
            <Text>{themeManager.success('✅ Connection successful!')}</Text>
            <Text>{themeManager.muted('Press Enter to continue')}</Text>
          </Box>
        )}
        {testResult === 'failed' && (
          <Box flexDirection="column" marginLeft={2}>
            <Text>{themeManager.error('❌ Connection failed')}</Text>
            <Text>{themeManager.muted('Please check your API key and try again')}</Text>
            <Text>{themeManager.muted('Press Enter to retry, Esc to go back')}</Text>
          </Box>
        )}
        {testResult === 'invalid_format' && (
          <Box flexDirection="column" marginLeft={2}>
            <Text>{themeManager.error('❌ Invalid API key format')}</Text>
            <Text>{themeManager.muted('Please check your API key format and try again')}</Text>
            <Text>{themeManager.muted('Press Enter to retry, Esc to go back')}</Text>
          </Box>
        )}
        {testResult === 'error' && (
          <Box flexDirection="column" marginLeft={2}>
            <Text>{themeManager.error('❌ Connection error')}</Text>
            <Text>{themeManager.muted('An error occurred while testing the connection')}</Text>
            <Text>{themeManager.muted('Press Enter to retry, Esc to go back')}</Text>
          </Box>
        )}
      </Box>
    </MaxBoxSized>
  );

  return (
    <MaxBoxSized
      themeManager={themeManager}
      maxWidth="100%"
      maxHeight="100%"
      responsive={true}
      padding={1}
    >
      <Box flexDirection="column">
        <Header
          themeManager={themeManager}
          title="🤖 Arien AI CLI - Authentication"
          subtitle="Configure your AI provider to get started"
        />
        {step === 'provider' && renderProviderSelection()}
        {step === 'model' && renderModelSelection()}
        {step === 'apikey' && renderApiKeyInput()}
        {step === 'testing' && renderTesting()}
      </Box>
    </MaxBoxSized>
  );
};
