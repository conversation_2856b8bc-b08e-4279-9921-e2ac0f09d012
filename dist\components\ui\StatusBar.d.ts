/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import React from 'react';
import { ThemeManager } from '../../themes/themes.js';
interface StatusBarProps {
    themeManager: ThemeManager;
    leftItems?: React.ReactNode[];
    centerItems?: React.ReactNode[];
    rightItems?: React.ReactNode[];
    provider?: string;
    model?: string;
    connectionStatus?: 'connected' | 'disconnected' | 'connecting';
    messageCount?: number;
    showTime?: boolean;
    showProvider?: boolean;
    showConnection?: boolean;
    showMessageCount?: boolean;
}
export declare const StatusBar: React.FC<StatusBarProps>;
export {};
//# sourceMappingURL=StatusBar.d.ts.map