{"version": 3, "file": "useTerminalInput.d.ts", "sourceRoot": "", "sources": ["../../src/hooks/useTerminalInput.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,OAAO,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,MAAM,qCAAqC,CAAC;AAErH,MAAM,WAAW,uBAAwB,SAAQ,oBAAoB;IACnE,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC;IACnC,QAAQ,CAAC,EAAE,MAAM,IAAI,CAAC;IACtB,eAAe,CAAC,EAAE,CAAC,IAAI,EAAE;QAAE,YAAY,EAAE,MAAM,CAAC;QAAC,YAAY,EAAE,MAAM,CAAC;QAAC,QAAQ,EAAE,MAAM,CAAA;KAAE,KAAK,IAAI,CAAC;IACnG,eAAe,CAAC,EAAE,CAAC,IAAI,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE,KAAK,IAAI,CAAC;IACnE,mBAAmB,CAAC,EAAE,CAAC,IAAI,EAAE;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAE,KAAK,IAAI,CAAC;IAC1E,QAAQ,CAAC,EAAE,OAAO,CAAC;CACpB;AAED,MAAM,WAAW,sBAAsB;IACrC,KAAK,EAAE,kBAAkB,CAAC;IAC1B,OAAO,EAAE,oBAAoB,CAAC;IAC9B,KAAK,EAAE,MAAM,IAAI,CAAC;IAClB,QAAQ,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC;IAClC,WAAW,EAAE,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,CAAC;IACzC,aAAa,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,oBAAoB,CAAC,KAAK,IAAI,CAAC;CACjE;AAED;;GAEG;AACH,eAAO,MAAM,gBAAgB,GAAI,UAAS,uBAA4B,KAAG,sBA2IxE,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,sBAAsB,GAAI,UAAU,MAAM,EAAE,UAAS,IAAI,CAAC,uBAAuB,EAAE,QAAQ,GAAG,WAAW,CAAM,2BA6B3H,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,oBAAoB,GAAI,UAAS,uBAA4B,2BAMzE,CAAC"}