/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import fs from 'fs';
import path from 'path';
import { BaseTool } from './tools.js';
import { SchemaValidator } from '../utils/schemaValidator.js';
import { getErrorMessage } from '../utils/errors.js';
import { spawn } from 'child_process';
export class GitLogTool extends BaseTool {
    config;
    static Name = 'git_log';
    constructor(config) {
        super(GitLogTool.Name, 'Git Log', `Shows commit history with various filtering and formatting options:
- Filter by date range, author, or commit message content
- Limit number of commits shown
- Display commit statistics and file changes
- Show commit graph and branch relationships
- Filter commits affecting specific files
- Multiple output formats for different use cases

Provides comprehensive repository history exploration with rich formatting options.`, {
            type: 'object',
            properties: {
                directory: {
                    type: 'string',
                    description: '(OPTIONAL) Directory to show git log for. Must be relative to the project root directory. Defaults to project root.',
                },
                max_count: {
                    type: 'number',
                    description: '(OPTIONAL) Maximum number of commits to show. Defaults to 10.',
                    minimum: 1,
                    maximum: 1000,
                },
                since: {
                    type: 'string',
                    description: '(OPTIONAL) Show commits since date/time (e.g., "2023-01-01", "1 week ago", "yesterday").',
                },
                until: {
                    type: 'string',
                    description: '(OPTIONAL) Show commits until date/time (e.g., "2023-12-31", "1 day ago").',
                },
                author: {
                    type: 'string',
                    description: '(OPTIONAL) Filter commits by author name or email (partial matches supported).',
                },
                grep: {
                    type: 'string',
                    description: '(OPTIONAL) Filter commits by commit message content (case-insensitive search).',
                },
                file_path: {
                    type: 'string',
                    description: '(OPTIONAL) Show only commits that affected the specified file or directory path.',
                },
                oneline: {
                    type: 'boolean',
                    description: '(OPTIONAL) Show each commit on a single line with hash and subject. Defaults to false.',
                },
                graph: {
                    type: 'boolean',
                    description: '(OPTIONAL) Show ASCII graph of branch and merge history. Defaults to false.',
                },
                stat: {
                    type: 'boolean',
                    description: '(OPTIONAL) Show file change statistics for each commit. Defaults to false.',
                },
                format: {
                    type: 'string',
                    enum: ['short', 'medium', 'full', 'fuller', 'oneline', 'custom'],
                    description: '(OPTIONAL) Output format: short (hash+subject), medium (default), full (with committer), fuller (with dates), oneline (compact), custom (detailed markdown).',
                },
            },
            required: [],
        }, true, // output is markdown
        false);
        this.config = config;
    }
    validateToolParams(params) {
        const validation = SchemaValidator.validate(params, this.parameterSchema);
        if (!validation.isValid) {
            return `Parameters failed schema validation: ${validation.errors.join(', ')}`;
        }
        if (params.directory) {
            if (path.isAbsolute(params.directory)) {
                return 'Directory cannot be absolute. Must be relative to the project root directory.';
            }
            const directory = path.resolve(this.config.getTargetDir(), params.directory);
            if (!fs.existsSync(directory)) {
                return 'Directory must exist.';
            }
        }
        if (params.max_count !== undefined) {
            if (params.max_count < 1 || params.max_count > 1000) {
                return 'Max count must be between 1 and 1000.';
            }
        }
        return null;
    }
    async executeGitCommand(args, cwd) {
        return new Promise((resolve) => {
            const process = spawn('git', args, {
                cwd,
                stdio: ['ignore', 'pipe', 'pipe'],
            });
            let stdout = '';
            let stderr = '';
            process.stdout?.on('data', (data) => {
                stdout += data.toString();
            });
            process.stderr?.on('data', (data) => {
                stderr += data.toString();
            });
            process.on('close', (exitCode) => {
                resolve({ stdout, stderr, exitCode: exitCode || 0 });
            });
        });
    }
    parseCommitLog(output) {
        const commits = [];
        const commitBlocks = output.split('\n\ncommit ').filter(block => block.trim());
        for (let block of commitBlocks) {
            if (!block.startsWith('commit ')) {
                block = 'commit ' + block;
            }
            const lines = block.split('\n');
            const commit = {};
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                if (line.startsWith('commit ')) {
                    commit.hash = line.split(' ')[1];
                    commit.shortHash = commit.hash.substring(0, 7);
                }
                else if (line.startsWith('Author: ')) {
                    const authorMatch = line.match(/Author: (.+) <(.+)>/);
                    if (authorMatch) {
                        commit.author = authorMatch[1];
                        commit.authorEmail = authorMatch[2];
                    }
                }
                else if (line.startsWith('Date: ')) {
                    commit.date = line.substring(6).trim();
                }
                else if (line.trim() && !line.startsWith(' ') && commit.hash && !commit.subject) {
                    commit.subject = line.trim();
                }
                else if (line.startsWith('    ') && commit.subject) {
                    if (!commit.body)
                        commit.body = '';
                    commit.body += line.substring(4) + '\n';
                }
            }
            if (commit.hash && commit.author && commit.subject) {
                commits.push(commit);
            }
        }
        return commits;
    }
    formatLogOutput(output, params) {
        if (params.oneline || params.format === 'oneline') {
            return '# Git Log (One Line)\n\n```\n' + output + '\n```\n';
        }
        if (params.graph) {
            return '# Git Log (Graph)\n\n```\n' + output + '\n```\n';
        }
        if (params.stat) {
            return '# Git Log (With Statistics)\n\n```\n' + output + '\n```\n';
        }
        if (params.format && params.format !== 'custom') {
            return `# Git Log (${params.format} format)\n\n\`\`\`\n` + output + '\n```\n';
        }
        // Custom markdown format
        const commits = this.parseCommitLog(output);
        if (commits.length === 0) {
            return '# Git Log\n\n📝 No commits found matching the specified criteria.\n';
        }
        let result = '# Git Log\n\n';
        // Add filter information
        const filters = [];
        if (params.since)
            filters.push(`since: ${params.since}`);
        if (params.until)
            filters.push(`until: ${params.until}`);
        if (params.author)
            filters.push(`author: ${params.author}`);
        if (params.grep)
            filters.push(`message: ${params.grep}`);
        if (params.file_path)
            filters.push(`file: ${params.file_path}`);
        if (filters.length > 0) {
            result += `**Filters:** ${filters.join(', ')}\n\n`;
        }
        result += `**Showing ${commits.length} commit${commits.length !== 1 ? 's' : ''}**\n\n`;
        for (const commit of commits) {
            result += `## 📝 ${commit.shortHash} - ${commit.subject}\n\n`;
            result += `**Author:** ${commit.author} <${commit.authorEmail}>\n`;
            result += `**Date:** ${commit.date}\n`;
            result += `**Hash:** \`${commit.hash}\`\n\n`;
            if (commit.body && commit.body.trim()) {
                result += `**Description:**\n${commit.body.trim()}\n\n`;
            }
            result += '---\n\n';
        }
        return result;
    }
    async execute(params, abortSignal) {
        const validationError = this.validateToolParams(params);
        if (validationError) {
            return {
                llmContent: `Error: ${validationError}`,
                returnDisplay: `Git Log Error: ${validationError}`,
            };
        }
        if (abortSignal.aborted) {
            return {
                llmContent: 'Git log command was cancelled by user.',
                returnDisplay: 'Git log cancelled.',
            };
        }
        try {
            const workingDir = params.directory
                ? path.resolve(this.config.getTargetDir(), params.directory)
                : this.config.getTargetDir();
            // Check if directory is a git repository
            const gitDirCheck = await this.executeGitCommand(['rev-parse', '--git-dir'], workingDir);
            if (gitDirCheck.exitCode !== 0) {
                return {
                    llmContent: `Error: Not a git repository (or any of the parent directories)`,
                    returnDisplay: 'Error: Not a git repository',
                };
            }
            // Build git log command
            const args = ['log'];
            // Add format options
            if (params.oneline) {
                args.push('--oneline');
            }
            else if (params.format && params.format !== 'custom') {
                args.push(`--format=${params.format}`);
            }
            if (params.graph) {
                args.push('--graph');
            }
            if (params.stat) {
                args.push('--stat');
            }
            // Add filtering options
            if (params.max_count) {
                args.push(`-${params.max_count}`);
            }
            else {
                args.push('-10'); // Default to 10 commits
            }
            if (params.since) {
                args.push('--since', params.since);
            }
            if (params.until) {
                args.push('--until', params.until);
            }
            if (params.author) {
                args.push('--author', params.author);
            }
            if (params.grep) {
                args.push('--grep', params.grep);
                args.push('-i'); // Case insensitive
            }
            if (params.file_path) {
                args.push('--', params.file_path);
            }
            const result = await this.executeGitCommand(args, workingDir);
            if (result.exitCode !== 0) {
                return {
                    llmContent: `Git log failed: ${result.stderr}`,
                    returnDisplay: `Git log error: ${result.stderr}`,
                };
            }
            if (!result.stdout.trim()) {
                return {
                    llmContent: '# Git Log\n\n📝 No commits found matching the specified criteria.\n',
                    returnDisplay: 'No commits found',
                };
            }
            const formattedOutput = this.formatLogOutput(result.stdout, params);
            let displayMessage = 'Git log';
            if (params.max_count) {
                displayMessage += ` (${params.max_count} commits)`;
            }
            if (params.file_path) {
                displayMessage += ` for ${params.file_path}`;
            }
            return {
                llmContent: formattedOutput,
                returnDisplay: displayMessage,
            };
        }
        catch (error) {
            const errorMessage = `Error executing git log: ${getErrorMessage(error)}`;
            return {
                llmContent: errorMessage,
                returnDisplay: errorMessage,
            };
        }
    }
}
//# sourceMappingURL=git-log.js.map