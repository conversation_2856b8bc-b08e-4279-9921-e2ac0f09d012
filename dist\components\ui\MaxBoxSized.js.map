{"version": 3, "file": "MaxBoxSized.js", "sourceRoot": "", "sources": ["../../../src/components/ui/MaxBoxSized.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC;AAC3D,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,MAAM,KAAK,CAAC;AA0DrC,MAAM,CAAC,MAAM,WAAW,GAA+B,CAAC,EACtD,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,KAAK,EACL,MAAM,EACN,SAAS,EACT,SAAS,EACT,QAAQ,EACR,UAAU,GAAG,IAAI,EACjB,qBAAqB,GAAG;IACtB,KAAK,EAAE,EAAE;IACT,MAAM,EAAE,EAAE;IACV,KAAK,EAAE,GAAG;CACX,EACD,UAAU,GAAG,IAAI,EACjB,mBAAmB,GAAG,KAAK,EAC3B,WAAW,EACX,QAAQ,EACR,UAAU,EACV,KAAK,GAAG,KAAK,EACb,GAAG,QAAQ,EACZ,EAAE,EAAE;IACH,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC;IAC/B,MAAM,CAAC,kBAAkB,EAAE,qBAAqB,CAAC,GAAG,QAAQ,CAGzD,EAAE,CAAC,CAAC;IACP,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;IAC3E,MAAM,YAAY,GAAG,MAAM,CAAM,IAAI,CAAC,CAAC;IAEvC,yDAAyD;IACzD,MAAM,6BAA6B,GAAG,GAAG,EAAE;QACzC,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM;YAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;QAErD,MAAM,aAAa,GAAG,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC;QAC3C,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;QAEzC,IAAI,eAAe,GAAG,KAAK,CAAC;QAC5B,IAAI,gBAAgB,GAAG,MAAM,CAAC;QAE9B,+BAA+B;QAC/B,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC3D,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;YAC7D,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC,CAAC;QAC3D,CAAC;aAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACxC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7D,MAAM,UAAU,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;YAC9D,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,UAAU,CAAC,CAAC;QAC7D,CAAC;aAAM,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;YACzC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QACzD,CAAC;QAED,4BAA4B;QAC5B,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE,CAAC;YACxE,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE,CAAC;YAC1E,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;QAC3D,CAAC;QAED,qCAAqC;QACrC,IAAI,mBAAmB,IAAI,WAAW,IAAI,eAAe,IAAI,gBAAgB,EAAE,CAAC;YAC9E,MAAM,YAAY,GAAI,eAA0B,GAAI,gBAA2B,CAAC;YAChF,IAAI,YAAY,GAAG,WAAW,EAAE,CAAC;gBAC/B,eAAe,GAAG,IAAI,CAAC,KAAK,CAAE,gBAA2B,GAAG,WAAW,CAAC,CAAC;YAC3E,CAAC;iBAAM,CAAC;gBACN,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAE,eAA0B,GAAG,WAAW,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC;QAED,OAAO;YACL,KAAK,EAAE,eAAe;YACtB,MAAM,EAAE,gBAAgB;SACzB,CAAC;IACJ,CAAC,CAAC;IAEF,+DAA+D;IAC/D,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,6BAA6B,EAAE,CAAC;YACtD,qBAAqB,CAAC,aAAa,CAAC,CAAC;YAErC,IAAI,QAAQ,IAAI,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,MAAM,EAAE,CAAC;gBAC5D,QAAQ,CAAC;oBACP,KAAK,EAAE,OAAO,aAAa,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBACxE,MAAM,EAAE,OAAO,aAAa,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;iBAC5E,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC,EAAE;QACD,MAAM,EAAE,OAAO;QACf,MAAM,EAAE,IAAI;QACZ,KAAK;QACL,MAAM;QACN,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,SAAS;QACT,UAAU;QACV,UAAU;QACV,mBAAmB;QACnB,WAAW;KACZ,CAAC,CAAC;IAEH,gCAAgC;IAChC,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,aAAa,GAAG,GAAG,EAAE;YACzB,MAAM,aAAa,GAAG,MAAM,EAAE,OAAO,IAAI,EAAE,CAAC;YAC5C,MAAM,cAAc,GAAG,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC;YAE1C,MAAM,cAAc,GAAG,kBAAkB,CAAC,KAAK,IAAI,KAAK,CAAC;YACzD,MAAM,eAAe,GAAG,kBAAkB,CAAC,MAAM,IAAI,MAAM,CAAC;YAE5D,MAAM,SAAS,GAAG,OAAO,cAAc,KAAK,QAAQ,IAAI,cAAc,GAAG,aAAa,CAAC;YACvF,MAAM,SAAS,GAAG,OAAO,eAAe,KAAK,QAAQ,IAAI,eAAe,GAAG,cAAc,CAAC;YAE1F,MAAM,WAAW,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC;YAEnD,IAAI,WAAW,CAAC,CAAC,KAAK,aAAa,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,KAAK,aAAa,CAAC,CAAC,EAAE,CAAC;gBAC3E,gBAAgB,CAAC,WAAW,CAAC,CAAC;gBAC9B,IAAI,UAAU,EAAE,CAAC;oBACf,UAAU,CAAC,WAAW,CAAC,CAAC;gBAC1B,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEF,aAAa,EAAE,CAAC;IAClB,CAAC,EAAE,CAAC,kBAAkB,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC,CAAC;IAElG,oCAAoC;IACpC,MAAM,cAAc,GAAG,SAAS,IAAI,QAAQ,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IACzF,MAAM,cAAc,GAAG,SAAS,IAAI,QAAQ,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IAEzF,0BAA0B;IAC1B,MAAM,aAAa,GAAG;QACpB,GAAG,QAAQ;QACX,GAAG,EAAE,YAAY;QACjB,KAAK,EAAE,kBAAkB,CAAC,KAAK,IAAI,KAAK;QACxC,MAAM,EAAE,kBAAkB,CAAC,MAAM,IAAI,MAAM;QAC3C,SAAS,EAAE,cAAc;QACzB,SAAS,EAAE,cAAc;KAC1B,CAAC;IAEF,8CAA8C;IAC9C,IAAI,QAAQ,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAC1C,aAAa,CAAC,KAAK,GAAG,QAAQ,CAAC;IACjC,CAAC;IACD,IAAI,SAAS,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;QAC5C,aAAa,CAAC,MAAM,GAAG,SAAS,CAAC;IACnC,CAAC;IAED,oBAAoB;IACpB,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,IAAI,CAAC,KAAK,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC;QAEzC,MAAM,YAAY,GAAG,GAAG,MAAM,EAAE,OAAO,IAAI,SAAS,IAAI,MAAM,EAAE,IAAI,IAAI,SAAS,EAAE,CAAC;QACpF,MAAM,YAAY,GAAG,GAAG,kBAAkB,CAAC,KAAK,IAAI,MAAM,IAAI,kBAAkB,CAAC,MAAM,IAAI,MAAM,EAAE,CAAC;QACpG,MAAM,cAAc,GAAG,KAAK,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,MAAM,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAE3G,OAAO,CACL,KAAC,GAAG,IACF,WAAW,EAAC,QAAQ,EACpB,WAAW,EAAC,QAAQ,EACpB,OAAO,EAAE,CAAC,YAEV,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,KAAC,GAAG,cAAE,YAAY,CAAC,KAAK,CAAC,aAAa,YAAY,EAAE,CAAC,GAAO,EAC5D,KAAC,GAAG,cAAE,YAAY,CAAC,KAAK,CAAC,aAAa,YAAY,EAAE,CAAC,GAAO,EAC5D,KAAC,GAAG,cAAE,YAAY,CAAC,KAAK,CAAC,aAAa,cAAc,EAAE,CAAC,GAAO,IAC1D,GACF,CACP,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,GAAG,OAAK,aAAa,aACnB,QAAQ,EACR,eAAe,EAAE,IACd,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,8CAA8C;AAC9C,MAAM,CAAC,MAAM,gBAAgB,GAAmD,CAAC,KAAK,EAAE,EAAE,CAAC,CACzF,KAAC,WAAW,OAAK,KAAK,EAAE,UAAU,EAAE,IAAI,GAAI,CAC7C,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAkE,CAAC,KAAK,EAAE,EAAE,CAAC,CACnG,KAAC,WAAW,OAAK,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,GAAI,CACjE,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAAsF,CAAC,KAAK,EAAE,EAAE,CAAC,CAC1H,KAAC,WAAW,OAAK,KAAK,EAAE,mBAAmB,EAAE,IAAI,GAAI,CACtD,CAAC"}