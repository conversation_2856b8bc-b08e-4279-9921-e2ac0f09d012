/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import React from 'react';
import { ThemeManager } from '../../themes/themes.js';
export interface TextLine {
    id: string;
    content: string;
    timestamp?: number;
    metadata?: Record<string, any>;
    style?: 'normal' | 'bold' | 'italic' | 'underline' | 'strikethrough';
    color?: 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'error' | 'info' | 'muted';
}
export interface TextBufferProps {
    themeManager: ThemeManager;
    lines?: TextLine[];
    content?: string;
    maxLines?: number;
    maxWidth?: number;
    maxHeight?: number;
    scrollable?: boolean;
    showLineNumbers?: boolean;
    showTimestamps?: boolean;
    enableSearch?: boolean;
    enableSelection?: boolean;
    wordWrap?: boolean;
    autoScroll?: boolean;
    scrollToBottom?: boolean;
    highlightPattern?: string | RegExp;
    filterPattern?: string | RegExp;
    onLineClick?: (line: TextLine, index: number) => void;
    onSelectionChange?: (selectedLines: TextLine[]) => void;
    onScroll?: (scrollPosition: {
        top: number;
        bottom: number;
    }) => void;
    variant?: 'default' | 'compact' | 'detailed' | 'code';
    borderStyle?: 'single' | 'double' | 'round' | 'bold';
    borderColor?: string;
    padding?: number;
    margin?: number;
    title?: string;
    footer?: string;
    emptyMessage?: string;
    loadingMessage?: string;
    isLoading?: boolean;
    disabled?: boolean;
}
export declare const TextBuffer: React.FC<TextBufferProps>;
export declare const CodeTextBuffer: React.FC<Omit<TextBufferProps, 'variant' | 'showLineNumbers'>>;
export declare const LogTextBuffer: React.FC<Omit<TextBufferProps, 'variant' | 'showTimestamps'>>;
export declare const CompactTextBuffer: React.FC<Omit<TextBufferProps, 'variant'>>;
export declare const ScrollableTextBuffer: React.FC<Omit<TextBufferProps, 'scrollable'>>;
//# sourceMappingURL=TextBuffer.d.ts.map