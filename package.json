{"name": "arien-ai-cli", "version": "1.0.0", "description": "AI-Powered CLI tool with multiple LLM providers and function calling capabilities", "main": "dist/index.js", "type": "module", "bin": {"arien": "dist/index.js"}, "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "test": "vitest", "test:watch": "vitest --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["ai", "cli", "llm", "chat", "assistant", "tools", "function-calling"], "author": "Arien LLC", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.30.1", "@google/genai": "^1.8.0", "@modelcontextprotocol/sdk": "^1.0.4", "chalk": "^5.3.0", "diff": "^7.0.0", "glob": "^11.0.0", "html-to-text": "^9.0.5", "ink": "^5.0.1", "openai": "^4.73.1", "react": "^18.3.1", "shell-quote": "^1.8.1", "strip-ansi": "^7.1.0", "tsx": "^4.19.2", "yaml": "^2.6.1", "zod": "^3.24.1"}, "devDependencies": {"@types/diff": "^6.0.0", "@types/html-to-text": "^9.0.4", "@types/node": "^22.10.2", "@types/react": "^18.3.23", "@types/shell-quote": "^1.7.5", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "eslint": "^9.17.0", "rimraf": "^6.0.1", "typescript": "^5.8.3", "vitest": "^2.1.8"}, "engines": {"node": ">=20.0.0"}}