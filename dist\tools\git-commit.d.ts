/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { Config } from '../config/config.js';
import { BaseTool, ToolResult } from './tools.js';
export interface GitCommitParams {
    directory?: string;
    message: string;
    description?: string;
    add_all?: boolean;
    add_files?: string[];
    amend?: boolean;
    no_verify?: boolean;
    author?: string;
    [key: string]: unknown;
}
export declare class GitCommitTool extends BaseTool<GitCommitParams, ToolResult> {
    private readonly config;
    static Name: string;
    constructor(config: Config);
    validateToolParams(params: GitCommitParams): string | null;
    private executeGitCommand;
    private formatCommitMessage;
    private analyzeCommitMessage;
    execute(params: GitCommitParams, abortSignal: AbortSignal): Promise<ToolResult>;
}
//# sourceMappingURL=git-commit.d.ts.map