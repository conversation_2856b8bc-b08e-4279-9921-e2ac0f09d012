import { Fragment as _Fragment, jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { MaxBoxSized } from './MaxBoxSized.js';
export const StatusBar = ({ themeManager, leftItems = [], centerItems = [], rightItems = [], provider, model, connectionStatus = 'connected', messageCount = 0, showTime = true, showProvider = true, showConnection = true, showMessageCount = true, }) => {
    const getCurrentTime = () => {
        return new Date().toLocaleTimeString();
    };
    const getConnectionStatusIcon = () => {
        const theme = themeManager.getCurrentTheme();
        switch (connectionStatus) {
            case 'connected':
                return themeManager.success(theme.symbols.check);
            case 'disconnected':
                return themeManager.error(theme.symbols.cross);
            case 'connecting':
                return themeManager.warning('⟳');
            default:
                return themeManager.muted('?');
        }
    };
    const getConnectionStatusText = () => {
        switch (connectionStatus) {
            case 'connected':
                return themeManager.success('Connected');
            case 'disconnected':
                return themeManager.error('Disconnected');
            case 'connecting':
                return themeManager.warning('Connecting...');
            default:
                return themeManager.muted('Unknown');
        }
    };
    const renderLeftSection = () => {
        const items = [...leftItems];
        if (showProvider && provider) {
            items.push(_jsxs(Text, { children: [themeManager.secondary('Provider: '), themeManager.primary(provider), model && (_jsxs(_Fragment, { children: [themeManager.muted(' ('), themeManager.accent(model), themeManager.muted(')')] }))] }, "provider"));
        }
        if (showConnection) {
            items.push(_jsxs(Text, { children: [getConnectionStatusIcon(), " ", getConnectionStatusText()] }, "connection"));
        }
        return items.length > 0 ? (_jsx(Box, { gap: 2, children: items.map((item, index) => (_jsx(Box, { children: item }, index))) })) : null;
    };
    const renderCenterSection = () => {
        const items = [...centerItems];
        if (showMessageCount) {
            items.push(_jsxs(Text, { children: [themeManager.muted('Messages: '), themeManager.info(messageCount.toString())] }, "messages"));
        }
        return items.length > 0 ? (_jsx(Box, { gap: 2, justifyContent: "center", children: items.map((item, index) => (_jsx(Box, { children: item }, index))) })) : null;
    };
    const renderRightSection = () => {
        const items = [...rightItems];
        if (showTime) {
            items.push(_jsx(Text, { children: themeManager.muted(getCurrentTime()) }, "time"));
        }
        return items.length > 0 ? (_jsx(Box, { gap: 2, justifyContent: "flex-end", children: items.map((item, index) => (_jsx(Box, { children: item }, index))) })) : null;
    };
    return (_jsx(MaxBoxSized, { themeManager: themeManager, maxWidth: "100%", responsive: true, autoResize: true, children: _jsxs(Box, { borderStyle: "single", borderColor: "gray", paddingX: 1, justifyContent: "space-between", width: "100%", children: [_jsx(Box, { flexGrow: 1, children: renderLeftSection() }), _jsx(Box, { flexGrow: 1, children: renderCenterSection() }), _jsx(Box, { flexGrow: 1, children: renderRightSection() })] }) }));
};
//# sourceMappingURL=StatusBar.js.map