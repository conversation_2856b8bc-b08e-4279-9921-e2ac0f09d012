import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { Header } from '../ui/Header.js';
import { RadioButtons } from '../ui/RadioButtons.js';
export const ThemeSelector = ({ themeManager, onThemeSelected, onSkip, }) => {
    const [selectedTheme, setSelectedTheme] = useState('');
    const [previewMode, setPreviewMode] = useState(false);
    const [livePreview, setLivePreview] = useState(true);
    const [originalTheme] = useState(themeManager.getCurrentTheme().name);
    const themes = themeManager.getAvailableThemes();
    // Convert themes to RadioOptions
    const themeOptions = themes.map(themeItem => ({
        value: themeItem.name,
        label: themeItem.theme.name,
        description: themeItem.theme.description,
    }));
    // Live preview effect - automatically preview themes as user navigates
    useEffect(() => {
        if (livePreview && !previewMode && selectedTheme) {
            themeManager.setTheme(selectedTheme);
        }
    }, [selectedTheme, livePreview, previewMode, themeManager]);
    // Handle additional keyboard shortcuts
    useInput((input, key) => {
        if (input === 'p' || input === 'P') {
            setPreviewMode(true);
        }
        else if (input === 'l' || input === 'L') {
            setLivePreview(!livePreview);
            if (!livePreview && selectedTheme) {
                themeManager.setTheme(selectedTheme);
            }
            else {
                themeManager.setTheme(originalTheme);
            }
        }
        else if (input === 's' || input === 'S') {
            // Reset to original theme before skipping
            themeManager.setTheme(originalTheme);
            onSkip();
        }
        else if (previewMode && (key.escape || input === 'q' || input === 'Q')) {
            setPreviewMode(false);
            if (livePreview && selectedTheme) {
                themeManager.setTheme(selectedTheme);
            }
            else {
                themeManager.setTheme(originalTheme);
            }
        }
    });
    const handleThemeSelection = (value, _option) => {
        setSelectedTheme(value);
    };
    const handleThemeSubmit = (value, _option) => {
        onThemeSelected(value);
    };
    const renderThemeList = () => (_jsx(Box, { flexDirection: "column", children: _jsx(RadioButtons, { themeManager: themeManager, options: themeOptions, selectedValue: selectedTheme, onSelectionChange: handleThemeSelection, onSubmit: handleThemeSubmit, title: "Available Themes:", showDescriptions: true, showIndicators: true, variant: "detailed", helpText: `Controls: P Detailed Preview | L Toggle Live Preview ${livePreview ? '[ON]' : '[OFF]'} | S Skip` }) }));
    const renderDetailedPreview = () => {
        const currentThemeItem = themes.find(t => t.name === selectedTheme);
        if (!currentThemeItem)
            return null;
        const currentTheme = currentThemeItem.theme;
        return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.secondary(`🔍 Detailed Preview: ${currentTheme.name}`) }), _jsx(Text, { children: themeManager.muted(currentTheme.description) }), _jsx(Text, {}), _jsxs(Box, { flexDirection: "column", marginLeft: 1, borderStyle: "round", borderColor: "gray", padding: 1, children: [_jsx(Text, { children: themeManager.primary('🤖 AI Assistant Chat Interface Preview') }), _jsx(Text, {}), _jsx(Box, { marginLeft: 2, children: _jsxs(Text, { children: [themeManager.accent('You:'), " ", themeManager.primary('Hello, can you help me with a task?')] }) }), _jsxs(Box, { marginLeft: 2, marginTop: 1, children: [_jsxs(Text, { children: [themeManager.secondary('Assistant:'), " ", themeManager.primary('Of course! I\'d be happy to help.')] }), _jsx(Text, { children: themeManager.success(`${currentTheme.symbols.check} Task completed successfully`) }), _jsx(Text, { children: themeManager.info(`${currentTheme.symbols.info} Additional information available`) }), _jsx(Text, { children: themeManager.warning(`${currentTheme.symbols.warning} Please review the output`) })] }), _jsxs(Box, { marginTop: 1, borderStyle: "single", borderColor: "gray", padding: 1, children: [_jsx(Text, { children: themeManager.muted('Status Indicators:') }), _jsxs(Text, { children: [themeManager.success(`${currentTheme.symbols.check} Success`), " ", themeManager.error(`${currentTheme.symbols.cross} Error`), " ", themeManager.info(`${currentTheme.symbols.info} Info`), " ", themeManager.warning(`${currentTheme.symbols.warning} Warning`)] }), _jsxs(Text, { children: [themeManager.muted('Loading: '), " ", currentTheme.symbols.loading.join(' ')] }), _jsx(Text, { children: themeManager.highlight(' Highlighted important text ') })] }), _jsxs(Box, { marginTop: 1, children: [_jsx(Text, { children: themeManager.muted('Color Palette:') }), _jsxs(Text, { children: [themeManager.primary('Primary'), " ", themeManager.secondary('Secondary'), " ", themeManager.accent('Accent'), " ", themeManager.muted('Muted')] })] })] }), _jsx(Text, {}), _jsx(Text, { children: themeManager.muted('Use ↑↓ to try other themes, Enter to select, Q/Esc to go back') })] }));
    };
    // Add a mini preview component for live preview mode
    const renderMiniPreview = () => {
        if (!livePreview || previewMode || !selectedTheme)
            return null;
        const currentThemeItem = themes.find(t => t.name === selectedTheme);
        if (!currentThemeItem)
            return null;
        const currentTheme = currentThemeItem.theme;
        return (_jsxs(Box, { flexDirection: "column", marginTop: 1, borderStyle: "single", borderColor: "gray", padding: 1, children: [_jsx(Text, { children: themeManager.muted(`Preview: ${currentTheme.name}`) }), _jsxs(Box, { flexDirection: "row", gap: 1, children: [_jsx(Text, { children: themeManager.primary('Primary') }), _jsx(Text, { children: themeManager.secondary('Secondary') }), _jsx(Text, { children: themeManager.accent('Accent') }), _jsx(Text, { children: themeManager.success(currentTheme.symbols.check) }), _jsx(Text, { children: themeManager.error(currentTheme.symbols.cross) })] })] }));
    };
    return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsx(Header, { themeManager: themeManager, title: "\uD83C\uDFA8 Enhanced Theme Selection", subtitle: `Choose your preferred CLI theme • ${themes.length} themes available` }), !previewMode ? (_jsxs(Box, { flexDirection: "column", children: [renderThemeList(), renderMiniPreview()] })) : (renderDetailedPreview())] }));
};
//# sourceMappingURL=ThemeSelector.js.map