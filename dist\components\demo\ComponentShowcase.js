import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { RadioButtons } from '../ui/RadioButtons.js';
import { MaxBoxSized } from '../ui/MaxBoxSized.js';
import { TextBuffer } from '../ui/TextBuffer.js';
export const ComponentShowcase = ({ themeManager, onExit, }) => {
    const [currentDemo, setCurrentDemo] = useState('radio');
    const [selectedOption, setSelectedOption] = useState('');
    const [textContent, setTextContent] = useState('');
    // Demo options for RadioButtons
    const demoOptions = [
        { value: 'radio', label: 'Radio Buttons Demo', description: 'Interactive selection component' },
        { value: 'maxbox', label: 'Max Box Sized Demo', description: 'Responsive container with size constraints' },
        { value: 'textbuffer', label: 'Text Buffer Demo', description: 'Advanced text display with scrolling' },
        { value: 'combined', label: 'Combined Demo', description: 'All components working together' },
    ];
    // Sample options for radio button demo
    const sampleOptions = [
        { value: 'option1', label: 'First Option', description: 'This is the first choice' },
        { value: 'option2', label: 'Second Option', description: 'This is the second choice' },
        { value: 'option3', label: 'Third Option', description: 'This is the third choice' },
        { value: 'disabled', label: 'Disabled Option', description: 'This option is disabled', disabled: true },
    ];
    // Sample text lines for TextBuffer demo
    const sampleTextLines = [
        { id: '1', content: '# Welcome to the Text Buffer Demo', color: 'primary', style: 'bold' },
        { id: '2', content: 'This component can display formatted text with various features:', color: 'secondary' },
        { id: '3', content: '• Syntax highlighting and color coding', color: 'info' },
        { id: '4', content: '• Line numbers and timestamps', color: 'info' },
        { id: '5', content: '• Scrolling for large content', color: 'info' },
        { id: '6', content: '• Search functionality', color: 'info' },
        { id: '7', content: '', color: 'muted' },
        { id: '8', content: '## Code Example:', color: 'accent', style: 'bold' },
        { id: '9', content: 'const example = "Hello, World!";', color: 'accent' },
        { id: '10', content: 'console.log(example);', color: 'accent' },
        { id: '11', content: '', color: 'muted' },
        { id: '12', content: 'SUCCESS: Component loaded successfully', color: 'success' },
        { id: '13', content: 'WARNING: This is a demo environment', color: 'warning' },
        { id: '14', content: 'ERROR: This is just a sample error message', color: 'error' },
        { id: '15', content: '', color: 'muted' },
        { id: '16', content: 'Use arrow keys to scroll, / to search, ESC to exit search', color: 'muted' },
    ];
    // Handle keyboard input
    useInput((input, key) => {
        if (key.escape || input === 'q' || input === 'Q') {
            onExit();
        }
    });
    const handleDemoSelection = (value, _option) => {
        setCurrentDemo(value);
    };
    const handleOptionSelection = (value, option) => {
        setSelectedOption(value);
        setTextContent(`Selected: ${option.label} (${value})\nDescription: ${option.description}`);
    };
    const renderRadioButtonsDemo = () => (_jsx(MaxBoxSized, { themeManager: themeManager, maxWidth: 60, maxHeight: 15, borderStyle: "round", borderColor: "blue", padding: 1, responsive: true, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary('Radio Buttons Component Demo') }), _jsx(Text, {}), _jsx(RadioButtons, { themeManager: themeManager, options: sampleOptions, selectedValue: selectedOption, onSelectionChange: handleOptionSelection, title: "Choose an option:", showDescriptions: true, variant: "detailed", helpText: "Use \u2191\u2193 to navigate, Enter to select, Space to toggle" }), selectedOption && (_jsx(Box, { marginTop: 1, children: _jsx(Text, { children: themeManager.success(`✓ Selected: ${selectedOption}`) }) }))] }) }));
    const renderMaxBoxSizedDemo = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary('Max Box Sized Component Demo') }), _jsx(Text, {}), _jsx(MaxBoxSized, { themeManager: themeManager, maxWidth: 50, maxHeight: 10, borderStyle: "double", borderColor: "green", padding: 2, responsive: true, debug: true, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.secondary('Responsive Container') }), _jsx(Text, {}), _jsx(Text, { children: "This container automatically adjusts to terminal size" }), _jsx(Text, { children: "while respecting maximum width and height constraints." }), _jsx(Text, {}), _jsx(Text, { children: themeManager.muted('Resize your terminal to see the effect!') })] }) })] }));
    const renderTextBufferDemo = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary('Text Buffer Component Demo') }), _jsx(Text, {}), _jsx(TextBuffer, { themeManager: themeManager, lines: sampleTextLines, maxHeight: 12, maxWidth: 70, showTimestamps: false, showLineNumbers: true, enableSearch: true, scrollable: true, title: "Sample Text Content", borderStyle: "single", borderColor: "yellow", padding: 1, variant: "detailed" })] }));
    const renderCombinedDemo = () => (_jsxs(Box, { flexDirection: "row", gap: 2, children: [_jsx(MaxBoxSized, { themeManager: themeManager, maxWidth: 30, maxHeight: 20, borderStyle: "round", borderColor: "cyan", padding: 1, children: _jsx(RadioButtons, { themeManager: themeManager, options: sampleOptions, selectedValue: selectedOption, onSelectionChange: handleOptionSelection, title: "Options:", variant: "compact", showDescriptions: false }) }), _jsx(MaxBoxSized, { themeManager: themeManager, maxWidth: 50, maxHeight: 20, borderStyle: "round", borderColor: "magenta", padding: 1, children: _jsx(TextBuffer, { themeManager: themeManager, content: textContent || 'Select an option to see details here...', maxHeight: 18, scrollable: true, showTimestamps: false, title: "Selection Details", variant: "compact" }) })] }));
    const renderCurrentDemo = () => {
        switch (currentDemo) {
            case 'radio':
                return renderRadioButtonsDemo();
            case 'maxbox':
                return renderMaxBoxSizedDemo();
            case 'textbuffer':
                return renderTextBufferDemo();
            case 'combined':
                return renderCombinedDemo();
            default:
                return renderRadioButtonsDemo();
        }
    };
    return (_jsx(MaxBoxSized, { themeManager: themeManager, maxWidth: 100, maxHeight: 30, responsive: true, flexDirection: "column", padding: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary('🎨 Component Showcase') }), _jsx(Text, { children: themeManager.muted('Demonstrating the new reusable UI components') }), _jsx(Text, {}), _jsx(Box, { marginBottom: 2, children: _jsx(RadioButtons, { themeManager: themeManager, options: demoOptions, selectedValue: currentDemo, onSelectionChange: handleDemoSelection, title: "Select a demo:", variant: "compact", showDescriptions: true }) }), _jsx(Box, { flexGrow: 1, children: renderCurrentDemo() }), _jsx(Box, { marginTop: 1, children: _jsx(Text, { children: themeManager.muted('Press Q or ESC to exit') }) })] }) }));
};
//# sourceMappingURL=ComponentShowcase.js.map