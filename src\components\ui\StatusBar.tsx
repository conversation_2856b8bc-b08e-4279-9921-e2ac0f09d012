/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React from 'react';
import { Box, Text } from 'ink';
import { ThemeManager } from '../../themes/themes.js';
import { MaxBoxSized } from './MaxBoxSized.js';

interface StatusBarProps {
  themeManager: ThemeManager;
  leftItems?: React.ReactNode[];
  centerItems?: React.ReactNode[];
  rightItems?: React.ReactNode[];
  provider?: string;
  model?: string;
  connectionStatus?: 'connected' | 'disconnected' | 'connecting';
  messageCount?: number;
  showTime?: boolean;
  showProvider?: boolean;
  showConnection?: boolean;
  showMessageCount?: boolean;
}

export const StatusBar: React.FC<StatusBarProps> = ({
  themeManager,
  leftItems = [],
  centerItems = [],
  rightItems = [],
  provider,
  model,
  connectionStatus = 'connected',
  messageCount = 0,
  showTime = true,
  showProvider = true,
  showConnection = true,
  showMessageCount = true,
}) => {
  const getCurrentTime = () => {
    return new Date().toLocaleTimeString();
  };

  const getConnectionStatusIcon = () => {
    const theme = themeManager.getCurrentTheme();
    switch (connectionStatus) {
      case 'connected':
        return themeManager.success(theme.symbols.check);
      case 'disconnected':
        return themeManager.error(theme.symbols.cross);
      case 'connecting':
        return themeManager.warning('⟳');
      default:
        return themeManager.muted('?');
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return themeManager.success('Connected');
      case 'disconnected':
        return themeManager.error('Disconnected');
      case 'connecting':
        return themeManager.warning('Connecting...');
      default:
        return themeManager.muted('Unknown');
    }
  };

  const renderLeftSection = () => {
    const items = [...leftItems];

    if (showProvider && provider) {
      items.push(
        <Text key="provider">
          {themeManager.secondary('Provider: ')}
          {themeManager.primary(provider)}
          {model && (
            <>
              {themeManager.muted(' (')}
              {themeManager.accent(model)}
              {themeManager.muted(')')}
            </>
          )}
        </Text>
      );
    }

    if (showConnection) {
      items.push(
        <Text key="connection">
          {getConnectionStatusIcon()} {getConnectionStatusText()}
        </Text>
      );
    }

    return items.length > 0 ? (
      <Box gap={2}>
        {items.map((item, index) => (
          <Box key={index}>{item}</Box>
        ))}
      </Box>
    ) : null;
  };

  const renderCenterSection = () => {
    const items = [...centerItems];

    if (showMessageCount) {
      items.push(
        <Text key="messages">
          {themeManager.muted('Messages: ')}
          {themeManager.info(messageCount.toString())}
        </Text>
      );
    }

    return items.length > 0 ? (
      <Box gap={2} justifyContent="center">
        {items.map((item, index) => (
          <Box key={index}>{item}</Box>
        ))}
      </Box>
    ) : null;
  };

  const renderRightSection = () => {
    const items = [...rightItems];

    if (showTime) {
      items.push(
        <Text key="time">
          {themeManager.muted(getCurrentTime())}
        </Text>
      );
    }

    return items.length > 0 ? (
      <Box gap={2} justifyContent="flex-end">
        {items.map((item, index) => (
          <Box key={index}>{item}</Box>
        ))}
      </Box>
    ) : null;
  };

  return (
    <MaxBoxSized
      themeManager={themeManager}
      maxWidth="100%"
      responsive={true}
      autoResize={true}
    >
      <Box
        borderStyle="single"
        borderColor="gray"
        paddingX={1}
        justifyContent="space-between"
        width="100%"
      >
        <Box flexGrow={1}>
          {renderLeftSection()}
        </Box>

        <Box flexGrow={1}>
          {renderCenterSection()}
        </Box>

        <Box flexGrow={1}>
          {renderRightSection()}
        </Box>
      </Box>
    </MaxBoxSized>
  );
};
