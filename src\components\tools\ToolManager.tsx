/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { ThemeManager } from '../../themes/themes.js';

interface ToolInfo {
  name: string;
  displayName: string;
  description: string;
  type: 'built-in' | 'discovered' | 'mcp';
  enabled: boolean;
  serverName?: string;
  parameterSchema: Record<string, any>;
  isOutputMarkdown: boolean;
  canUpdateOutput: boolean;
  lastUsed?: Date;
  usageCount: number;
  status: 'active' | 'inactive' | 'error' | 'loading';
  errorMessage?: string;
}

interface ToolManagerProps {
  themeManager: ThemeManager;
  isOpen: boolean;
  onClose: () => void;
  onToolToggle?: (toolName: string, enabled: boolean) => void;
  onToolRefresh?: () => Promise<void>;
  onToolTest?: (toolName: string) => Promise<boolean>;
}

export const ToolManager: React.FC<ToolManagerProps> = ({
  themeManager,
  isOpen,
  onClose,
  onToolToggle,
  onToolRefresh,
  onToolTest,
}) => {
  const [selectedToolIndex, setSelectedToolIndex] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'built-in' | 'discovered' | 'mcp'>('all');
  const [mode, setMode] = useState<'list' | 'details' | 'settings'>('list');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchQuery] = useState('');

  // Mock tool data - in real implementation, this would come from ToolRegistry
  const [tools, setTools] = useState<ToolInfo[]>([
    {
      name: 'file_read',
      displayName: 'File Reader',
      description: 'Read and analyze file contents',
      type: 'built-in',
      enabled: true,
      parameterSchema: { type: 'object', properties: { path: { type: 'string' } } },
      isOutputMarkdown: true,
      canUpdateOutput: false,
      lastUsed: new Date(),
      usageCount: 45,
      status: 'active',
    },
    {
      name: 'web_search',
      displayName: 'Web Search',
      description: 'Search the web for information',
      type: 'built-in',
      enabled: true,
      parameterSchema: { type: 'object', properties: { query: { type: 'string' } } },
      isOutputMarkdown: true,
      canUpdateOutput: false,
      lastUsed: new Date(Date.now() - 86400000),
      usageCount: 23,
      status: 'active',
    },
    {
      name: 'code_analyzer',
      displayName: 'Code Analyzer',
      description: 'Analyze code structure and patterns',
      type: 'discovered',
      enabled: false,
      parameterSchema: { type: 'object', properties: { code: { type: 'string' }, language: { type: 'string' } } },
      isOutputMarkdown: true,
      canUpdateOutput: true,
      usageCount: 0,
      status: 'inactive',
    },
    {
      name: 'filesystem_browser',
      displayName: 'Filesystem Browser',
      description: 'Browse and navigate filesystem',
      type: 'mcp',
      enabled: true,
      serverName: 'filesystem',
      parameterSchema: { type: 'object', properties: { path: { type: 'string' } } },
      isOutputMarkdown: false,
      canUpdateOutput: false,
      lastUsed: new Date(Date.now() - 3600000),
      usageCount: 12,
      status: 'active',
    },
    {
      name: 'git_operations',
      displayName: 'Git Operations',
      description: 'Perform Git version control operations',
      type: 'mcp',
      enabled: false,
      serverName: 'git',
      parameterSchema: { type: 'object', properties: { command: { type: 'string' } } },
      isOutputMarkdown: true,
      canUpdateOutput: false,
      usageCount: 0,
      status: 'error',
      errorMessage: 'MCP server not responding',
    },
  ]);

  useInput((input, key) => {
    if (!isOpen) return;

    if (key.escape) {
      if (mode === 'details' || mode === 'settings') {
        setMode('list');
        return;
      }
      onClose();
      return;
    }

    if (mode === 'list') {
      const filteredTools = getFilteredTools();
      
      if (key.upArrow && selectedToolIndex > 0) {
        setSelectedToolIndex(selectedToolIndex - 1);
        return;
      }
      if (key.downArrow && selectedToolIndex < filteredTools.length - 1) {
        setSelectedToolIndex(selectedToolIndex + 1);
        return;
      }
      if (key.return) {
        setMode('details');
        return;
      }
      if (input === ' ') {
        const tool = filteredTools[selectedToolIndex];
        toggleTool(tool.name);
        return;
      }
      if (key.tab) {
        cycleCategoryFilter();
        return;
      }
      if (key.ctrl && input === 'r') {
        handleRefresh();
        return;
      }
      if (key.ctrl && input === 't') {
        const tool = filteredTools[selectedToolIndex];
        handleTestTool(tool.name);
        return;
      }
    }

    if (mode === 'details') {
      if (input === 's') {
        setMode('settings');
        return;
      }
      if (input === 't') {
        const filteredTools = getFilteredTools();
        const tool = filteredTools[selectedToolIndex];
        handleTestTool(tool.name);
        return;
      }
    }
  });

  const getFilteredTools = () => {
    let filtered = tools;
    
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(tool => tool.type === selectedCategory);
    }
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(tool => 
        tool.name.toLowerCase().includes(query) ||
        tool.displayName.toLowerCase().includes(query) ||
        tool.description.toLowerCase().includes(query)
      );
    }
    
    return filtered;
  };

  const cycleCategoryFilter = () => {
    const categories: Array<'all' | 'built-in' | 'discovered' | 'mcp'> = ['all', 'built-in', 'discovered', 'mcp'];
    const currentIndex = categories.indexOf(selectedCategory);
    const nextIndex = (currentIndex + 1) % categories.length;
    setSelectedCategory(categories[nextIndex]);
    setSelectedToolIndex(0);
  };

  const toggleTool = (toolName: string) => {
    setTools(prev => prev.map(tool => 
      tool.name === toolName 
        ? { ...tool, enabled: !tool.enabled, status: !tool.enabled ? 'active' : 'inactive' }
        : tool
    ));
    const tool = tools.find(t => t.name === toolName);
    if (tool) {
      onToolToggle?.(toolName, !tool.enabled);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await onToolRefresh?.();
      // In real implementation, would reload tools from ToolRegistry
    } catch (error) {
      console.error('Failed to refresh tools:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleTestTool = async (toolName: string) => {
    const toolIndex = tools.findIndex(t => t.name === toolName);
    if (toolIndex === -1) return;

    setTools(prev => prev.map((tool, index) => 
      index === toolIndex 
        ? { ...tool, status: 'loading' }
        : tool
    ));

    try {
      const success = await onToolTest?.(toolName);
      setTools(prev => prev.map((tool, index) => 
        index === toolIndex 
          ? { ...tool, status: success ? 'active' : 'error', errorMessage: success ? undefined : 'Test failed' }
          : tool
      ));
    } catch (error) {
      setTools(prev => prev.map((tool, index) => 
        index === toolIndex 
          ? { ...tool, status: 'error', errorMessage: error instanceof Error ? error.message : 'Unknown error' }
          : tool
      ));
    }
  };

  if (!isOpen) return null;

  const renderToolsList = () => {
    const filteredTools = getFilteredTools();
    
    return (
      <Box flexDirection="column">
        <Box justifyContent="space-between">
          <Text>{themeManager.primary('Available Tools')}</Text>
          <Text>{themeManager.muted(`Filter: ${selectedCategory} (${filteredTools.length})`)}</Text>
        </Box>
        
        {isRefreshing && (
          <Box marginTop={1}>
            <Text>{themeManager.info('🔄 Refreshing tools...')}</Text>
          </Box>
        )}
        
        <Text></Text>
        {filteredTools.map((tool, index) => {
          const isSelected = index === selectedToolIndex;
          const statusIcon = getStatusIcon(tool.status);
          const statusColor = getStatusColor(tool.status);
          
          return (
            <Box key={tool.name} marginLeft={2}>
              <Text>
                {isSelected ? themeManager.highlight(' ▶ ') : '   '}
                {tool.enabled ? themeManager.success('●') : themeManager.muted('○')}
                {' '}
                {statusColor(statusIcon)}
                {' '}
                {themeManager.primary(tool.displayName)}
                {' '}
                {themeManager.muted(`[${tool.type}]`)}
                {tool.serverName && themeManager.accent(` (${tool.serverName})`)}
              </Text>
            </Box>
          );
        })}
        
        {filteredTools.length === 0 && (
          <Box justifyContent="center" marginTop={2}>
            <Text>{themeManager.muted('No tools found matching current filter')}</Text>
          </Box>
        )}
      </Box>
    );
  };

  const renderToolDetails = () => {
    const filteredTools = getFilteredTools();
    const tool = filteredTools[selectedToolIndex];
    if (!tool) return null;

    return (
      <Box flexDirection="column">
        <Text>{themeManager.primary(`Tool Details: ${tool.displayName}`)}</Text>
        <Text></Text>
        
        <Box marginLeft={2} flexDirection="column">
          <Text>{themeManager.secondary('Name: ')}{themeManager.info(tool.name)}</Text>
          <Text>{themeManager.secondary('Type: ')}{themeManager.accent(tool.type)}</Text>
          <Text>{themeManager.secondary('Status: ')}{getStatusColor(tool.status)(tool.status)}</Text>
          <Text>{themeManager.secondary('Enabled: ')}{tool.enabled ? themeManager.success('Yes') : themeManager.muted('No')}</Text>
          
          {tool.serverName && (
            <Text>{themeManager.secondary('Server: ')}{themeManager.accent(tool.serverName)}</Text>
          )}
          
          <Text></Text>
          <Text>{themeManager.secondary('Description:')}</Text>
          <Text>{themeManager.muted(tool.description)}</Text>
          
          <Text></Text>
          <Text>{themeManager.secondary('Usage Statistics:')}</Text>
          <Text>{themeManager.muted(`Used ${tool.usageCount} times`)}</Text>
          {tool.lastUsed && (
            <Text>{themeManager.muted(`Last used: ${tool.lastUsed.toLocaleString()}`)}</Text>
          )}
          
          <Text></Text>
          <Text>{themeManager.secondary('Capabilities:')}</Text>
          <Text>{themeManager.muted(`Markdown output: ${tool.isOutputMarkdown ? 'Yes' : 'No'}`)}</Text>
          <Text>{themeManager.muted(`Live updates: ${tool.canUpdateOutput ? 'Yes' : 'No'}`)}</Text>
          
          {tool.errorMessage && (
            <>
              <Text></Text>
              <Text>{themeManager.error(`Error: ${tool.errorMessage}`)}</Text>
            </>
          )}
        </Box>
      </Box>
    );
  };

  const getStatusIcon = (status: ToolInfo['status']) => {
    switch (status) {
      case 'active': return '✓';
      case 'inactive': return '○';
      case 'error': return '✗';
      case 'loading': return '⟳';
      default: return '?';
    }
  };

  const getStatusColor = (status: ToolInfo['status']) => {
    switch (status) {
      case 'active': return themeManager.success;
      case 'inactive': return themeManager.muted;
      case 'error': return themeManager.error;
      case 'loading': return themeManager.warning;
      default: return themeManager.muted;
    }
  };

  const renderControls = () => (
    <Box marginTop={1} borderStyle="single" borderColor="gray" padding={1}>
      <Box flexDirection="column">
        <Text>{themeManager.muted('Controls:')}</Text>
        {mode === 'list' && (
          <>
            <Text>{themeManager.muted('  ↑↓ Navigate • Enter Details • Space Toggle • Tab Filter')}</Text>
            <Text>{themeManager.muted('  Ctrl+R Refresh • Ctrl+T Test • Esc Close')}</Text>
          </>
        )}
        {mode === 'details' && (
          <Text>{themeManager.muted('  S Settings • T Test • Esc Back')}</Text>
        )}
      </Box>
    </Box>
  );

  return (
    <Box flexDirection="column" padding={1}>
      <Box borderStyle="round" borderColor="blue" padding={1}>
        <Box flexDirection="column" width="100%">
          <Text>{themeManager.primary('🔧 Tool Manager')}</Text>
          <Text></Text>
          
          {mode === 'list' && renderToolsList()}
          {mode === 'details' && renderToolDetails()}
          
          {renderControls()}
        </Box>
      </Box>
    </Box>
  );
};
