/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React from 'react';
import { Box, Text, useInput } from 'ink';
import { ThemeManager } from '../../themes/themes.js';
import { MaxBoxSized } from './MaxBoxSized.js';

interface ModalAction {
  label: string;
  key: string;
  handler: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
}

interface ModalProps {
  themeManager: ThemeManager;
  title: string;
  children: React.ReactNode;
  isOpen: boolean;
  onClose?: () => void;
  actions?: ModalAction[];
  width?: number;
  height?: number;
  closable?: boolean;
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
}

export const Modal: React.FC<ModalProps> = ({
  themeManager,
  title,
  children,
  isOpen,
  onClose,
  actions = [],
  width = 60,
  height,
  closable = true,
  variant = 'default',
}) => {
  useInput((input, key) => {
    if (!isOpen) return;

    if (key.escape && closable && onClose) {
      onClose();
      return;
    }

    // Handle action keys
    const action = actions.find(a => a.key === input);
    if (action) {
      action.handler();
    }
  });

  if (!isOpen) return null;

  const getBorderColor = () => {
    switch (variant) {
      case 'success':
        return 'green';
      case 'warning':
        return 'yellow';
      case 'error':
        return 'red';
      case 'info':
        return 'blue';
      default:
        return 'white';
    }
  };

  const getVariantIcon = () => {
    const theme = themeManager.getCurrentTheme();
    switch (variant) {
      case 'success':
        return themeManager.success(theme.symbols.check);
      case 'warning':
        return themeManager.warning(theme.symbols.warning);
      case 'error':
        return themeManager.error(theme.symbols.cross);
      case 'info':
        return themeManager.info(theme.symbols.info);
      default:
        return '';
    }
  };

  const renderHeader = () => (
    <Box justifyContent="space-between" borderStyle="single" borderColor="gray" paddingX={1}>
      <Box>
        {getVariantIcon() && <Text>{getVariantIcon()} </Text>}
        <Text>{themeManager.primary(title)}</Text>
      </Box>
      {closable && onClose && (
        <Text>{themeManager.muted('[ESC to close]')}</Text>
      )}
    </Box>
  );

  const renderContent = () => (
    <Box padding={1} flexDirection="column" minHeight={height}>
      {children}
    </Box>
  );

  const renderActions = () => {
    if (actions.length === 0) return null;

    return (
      <Box 
        borderStyle="single" 
        borderColor="gray" 
        paddingX={1} 
        justifyContent="space-between"
      >
        <Box gap={2}>
          {actions.map((action, index) => {
            const getActionColor = () => {
              switch (action.variant) {
                case 'primary':
                  return themeManager.primary;
                case 'danger':
                  return themeManager.error;
                case 'secondary':
                default:
                  return themeManager.secondary;
              }
            };

            return (
              <Text key={index}>
                {themeManager.accent(`[${action.key}]`)} {getActionColor()(action.label)}
              </Text>
            );
          })}
        </Box>
      </Box>
    );
  };

  return (
    <MaxBoxSized
      themeManager={themeManager}
      maxWidth="100%"
      maxHeight="100%"
      responsive={true}
      justifyContent="center"
      alignItems="center"
    >
      <Box
        justifyContent="center"
        alignItems="center"
        height="100%"
        width="100%"
      >
        {/* Modal */}
        <MaxBoxSized
          themeManager={themeManager}
          maxWidth={typeof width === 'number' ? width : 80}
          maxHeight={typeof height === 'number' ? height : 25}
          minWidth={40}
          minHeight={10}
          responsive={true}
        >
          <Box
            flexDirection="column"
            borderStyle="double"
            borderColor={getBorderColor()}
            width="100%"
            height="100%"
            paddingX={2}
            paddingY={1}
          >
            {renderHeader()}
            {renderContent()}
            {renderActions()}
          </Box>
        </MaxBoxSized>
      </Box>
    </MaxBoxSized>
  );
};

// Specialized modal variants
interface ConfirmModalProps {
  themeManager: ThemeManager;
  title: string;
  message: string;
  isOpen: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  confirmLabel?: string;
  cancelLabel?: string;
  variant?: 'default' | 'danger';
}

export const ConfirmModal: React.FC<ConfirmModalProps> = ({
  themeManager,
  title,
  message,
  isOpen,
  onConfirm,
  onCancel,
  confirmLabel = 'Confirm',
  cancelLabel = 'Cancel',
  variant = 'default',
}) => {
  const actions: ModalAction[] = [
    {
      label: confirmLabel,
      key: 'y',
      handler: onConfirm,
      variant: variant === 'danger' ? 'danger' : 'primary',
    },
    {
      label: cancelLabel,
      key: 'n',
      handler: onCancel,
      variant: 'secondary',
    },
  ];

  return (
    <Modal
      themeManager={themeManager}
      title={title}
      isOpen={isOpen}
      onClose={onCancel}
      actions={actions}
      variant={variant === 'danger' ? 'error' : 'default'}
      width={50}
    >
      <Text>{message}</Text>
    </Modal>
  );
};

interface AlertModalProps {
  themeManager: ThemeManager;
  title: string;
  message: string;
  isOpen: boolean;
  onClose: () => void;
  variant?: 'success' | 'warning' | 'error' | 'info';
}

export const AlertModal: React.FC<AlertModalProps> = ({
  themeManager,
  title,
  message,
  isOpen,
  onClose,
  variant = 'info',
}) => {
  const actions: ModalAction[] = [
    {
      label: 'OK',
      key: 'Enter',
      handler: onClose,
      variant: 'primary',
    },
  ];

  return (
    <Modal
      themeManager={themeManager}
      title={title}
      isOpen={isOpen}
      onClose={onClose}
      actions={actions}
      variant={variant}
      width={50}
    >
      <Text>{message}</Text>
    </Modal>
  );
};
