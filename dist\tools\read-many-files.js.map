{"version": 3, "file": "read-many-files.js", "sourceRoot": "", "sources": ["../../src/tools/read-many-files.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAc,MAAM,YAAY,CAAC;AAClD,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAC9D,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,IAAI,EAAE,MAAM,MAAM,CAAC;AAC5B,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,EAAE,yBAAyB,EAAE,MAAM,iBAAiB,CAAC;AAC5D,OAAO,EACL,cAAc,EACd,wBAAwB,EACxB,gBAAgB,GACjB,MAAM,uBAAuB,CAAC;AAG/B,OAAO,EACL,yBAAyB,EACzB,aAAa,GACd,MAAM,yBAAyB,CAAC;AAgDjC;;;;;GAKG;AACH,MAAM,gBAAgB,GAAa;IACjC,oBAAoB;IACpB,YAAY;IACZ,eAAe;IACf,aAAa;IACb,YAAY;IACZ,aAAa;IACb,gBAAgB;IAChB,mBAAmB;IACnB,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,SAAS;IACT,YAAY;IACZ,YAAY;IACZ,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,SAAS;IACT,UAAU;IACV,UAAU;IACV,SAAS;IACT,UAAU;IACV,WAAW;IACX,UAAU;IACV,WAAW;IACX,UAAU;IACV,WAAW;IACX,UAAU;IACV,UAAU;IACV,UAAU;IACV,eAAe;IACf,SAAS;IACT,MAAM,yBAAyB,EAAE,EAAE;CACpC,CAAC;AAEF,MAAM,+BAA+B,GAAG,oBAAoB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,OAAO,iBAAkB,SAAQ,QAGtC;IAUY;IACD;IAVV,MAAM,CAAU,IAAI,GAAW,iBAAiB,CAAC;IAChC,oBAAoB,GAAa,EAAE,CAAC;IAErD;;;;OAIG;IACH,YACW,SAAiB,EAClB,MAAc;QAEtB,MAAM,eAAe,GAA4B;YAC/C,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,KAAK,EAAE;oBACL,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBACzB,WAAW,EACT,yIAAyI;iBAC5I;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBACzB,WAAW,EACT,oKAAoK;oBACtK,OAAO,EAAE,EAAE;iBACZ;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBACzB,WAAW,EACT,mJAAmJ;oBACrJ,OAAO,EAAE,EAAE;iBACZ;gBACD,SAAS,EAAE;oBACT,IAAI,EAAE,SAAS;oBACf,WAAW,EACT,4GAA4G;oBAC9G,OAAO,EAAE,IAAI;iBACd;gBACD,kBAAkB,EAAE;oBAClB,IAAI,EAAE,SAAS;oBACf,WAAW,EACT,6HAA6H;oBAC/H,OAAO,EAAE,IAAI;iBACd;gBACD,kBAAkB,EAAE;oBAClB,IAAI,EAAE,SAAS;oBACf,WAAW,EACT,gIAAgI;oBAClI,OAAO,EAAE,IAAI;iBACd;aACF;YACD,QAAQ,EAAE,CAAC,OAAO,CAAC;SACpB,CAAC;QAEF,KAAK,CACH,iBAAiB,CAAC,IAAI,EACtB,eAAe,EACf;;;;;;;;;qwBAS+vB,EAC/vB,eAAe,CAChB,CAAC;QA9DO,cAAS,GAAT,SAAS,CAAQ;QAClB,WAAM,GAAN,MAAM,CAAQ;QA8DtB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACzC,IAAI,CAAC,oBAAoB,GAAG,MAAM;aAC/B,cAAc,EAAE;aAChB,oBAAoB,EAAE,CAAC;IAC5B,CAAC;IAED,cAAc,CAAC,MAA2B;QACxC,IACE,CAAC,MAAM,CAAC,KAAK;YACb,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;YAC5B,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EACzB,CAAC;YACD,OAAO,2FAA2F,CAAC;QACrG,CAAC;QACD,IACE,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,CAAC,eAAe,CAAC,QAAQ,CACvB,IAAI,CAAC,MAAM,CAAC,UAAqC,EACjD,MAAM,CACP,EACD,CAAC;YACD,IACE,CAAC,MAAM,CAAC,KAAK;gBACb,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC5B,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EACzB,CAAC;gBACD,OAAO,2FAA2F,CAAC;YACrG,CAAC;YACD,OAAO,2HAA2H,CAAC;QACrI,CAAC;QACD,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC7C,OAAO,+DAA+D,CAAC;YACzE,CAAC;QACH,CAAC;QACD,IACE,MAAM,CAAC,OAAO;YACd,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;gBAC7B,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,EAC5D,CAAC;YACD,OAAO,mEAAmE,CAAC;QAC7E,CAAC;QACD,IACE,MAAM,CAAC,OAAO;YACd,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;gBAC7B,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,EAC5D,CAAC;YACD,OAAO,mEAAmE,CAAC;QAC7E,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc,CAAC,MAA2B;QACxC,MAAM,WAAW,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,CAAC;QACjE,MAAM,QAAQ,GAAG,qBAAqB,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,kCAAkC,IAAI,CAAC,SAAS,KAAK,CAAC;QAEpH,8EAA8E;QAC9E,MAAM,aAAa,GAAG,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC;QAC3C,MAAM,uBAAuB,GAAG,MAAM,CAAC,kBAAkB,KAAK,KAAK,CAAC;QAEpE,MAAM,oCAAoC,GACxC,uBAAuB;YACrB,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,GAAG,aAAa,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACvE,CAAC,CAAC,CAAC,GAAG,aAAa,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAEvD,IAAI,WAAW,GAAG,cAAc,oCAAoC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,mBAAmB,oCAAoC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,oCAAoC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAE3P,mFAAmF;QACnF,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzC,MAAM,sBAAsB,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CACpE,oCAAoC,CAAC,QAAQ,CAAC,CAAC,CAAC,CACjD,CAAC,MAAM,CAAC;YACT,IAAI,sBAAsB,GAAG,CAAC,EAAE,CAAC;gBAC/B,WAAW,IAAI,cAAc,sBAAsB,sBAAsB,CAAC;YAC5E,CAAC;QACH,CAAC;QAED,OAAO,8CAA8C,QAAQ,KAAK,WAAW,oBAAoB,gBAAgB,iBAAiB,+BAA+B,CAAC,OAAO,CAAC,YAAY,EAAE,kBAAkB,CAAC,IAAI,CAAC;IAClN,CAAC;IAED,KAAK,CAAC,OAAO,CACX,MAA2B,EAC3B,MAAmB;QAEnB,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO;gBACL,UAAU,EAAE,iCAAiC,IAAI,CAAC,WAAW,aAAa,eAAe,EAAE;gBAC3F,aAAa,EAAE,yBAAyB,eAAe,EAAE;aAC1D,CAAC;QACJ,CAAC;QAED,MAAM,EACJ,KAAK,EAAE,aAAa,EACpB,OAAO,GAAG,EAAE,EACZ,OAAO,GAAG,EAAE,EACZ,kBAAkB,GAAG,IAAI,EACzB,kBAAkB,GAAG,IAAI,GAC1B,GAAG,MAAM,CAAC;QAEX,MAAM,gBAAgB,GACpB,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,gCAAgC,EAAE,CAAC;QAEvE,yCAAyC;QACzC,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;QAEnD,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC;QACnC,MAAM,eAAe,GAAG,IAAI,GAAG,EAAU,CAAC;QAC1C,MAAM,YAAY,GAA4C,EAAE,CAAC;QACjE,MAAM,2BAA2B,GAAa,EAAE,CAAC;QACjD,MAAM,YAAY,GAAkB,EAAE,CAAC;QAEvC,MAAM,iBAAiB,GAAG,kBAAkB;YAC1C,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,GAAG,OAAO,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACjE,CAAC,CAAC,CAAC,GAAG,OAAO,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAE/C,MAAM,cAAc,GAAG,CAAC,GAAG,aAAa,EAAE,GAAG,OAAO,CAAC,CAAC;QACtD,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,OAAO;gBACL,UAAU,EAAE,+CAA+C;gBAC3D,aAAa,EAAE,uGAAuG;aACvH,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE;gBACzC,GAAG,EAAE,WAAW;gBAChB,MAAM,EAAE,iBAAiB;gBACzB,KAAK,EAAE,IAAI;gBACX,GAAG,EAAE,IAAI;gBACT,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,IAAI;gBACZ,MAAM;aACP,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,gBAAgB;gBACtC,CAAC,CAAC,aAAa;qBACV,WAAW,CACV,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EACjD;oBACE,gBAAgB;iBACjB,CACF;qBACA,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;gBAC7C,CAAC,CAAC,OAAO,CAAC;YAEZ,IAAI,eAAe,GAAG,CAAC,CAAC;YACxB,KAAK,MAAM,gBAAgB,IAAI,OAAO,EAAE,CAAC;gBACvC,qFAAqF;gBACrF,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;oBAC9C,YAAY,CAAC,IAAI,CAAC;wBAChB,IAAI,EAAE,gBAAgB;wBACtB,MAAM,EAAE,wEAAwE,WAAW,WAAW,gBAAgB,EAAE;qBACzH,CAAC,CAAC;oBACH,SAAS;gBACX,CAAC;gBAED,oDAAoD;gBACpD,IAAI,gBAAgB,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;oBACpE,eAAe,EAAE,CAAC;oBAClB,SAAS;gBACX,CAAC;gBAED,eAAe,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YACxC,CAAC;YAED,wDAAwD;YACxD,IAAI,eAAe,GAAG,CAAC,EAAE,CAAC;gBACxB,YAAY,CAAC,IAAI,CAAC;oBAChB,IAAI,EAAE,GAAG,eAAe,UAAU;oBAClC,MAAM,EAAE,SAAS;iBAClB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,UAAU,EAAE,6BAA6B,eAAe,CAAC,KAAK,CAAC,EAAE;gBACjE,aAAa,EAAE,iFAAiF,eAAe,CAAC,KAAK,CAAC,UAAU;aACjI,CAAC;QACJ,CAAC;QAED,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,EAAE,CAAC;QAEvD,KAAK,MAAM,QAAQ,IAAI,WAAW,EAAE,CAAC;YACnC,MAAM,sBAAsB,GAAG,IAAI;iBAChC,QAAQ,CAAC,WAAW,EAAE,QAAQ,CAAC;iBAC/B,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YAEvB,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;YAE1C,IAAI,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,KAAK,EAAE,CAAC;gBAC/C,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC3D,MAAM,wBAAwB,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;gBACxE,MAAM,mBAAmB,GAAG,aAAa,CAAC,IAAI,CAC5C,CAAC,OAAe,EAAE,EAAE,CAClB,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC;oBAC7C,OAAO,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAC7C,CAAC;gBAEF,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBACzB,YAAY,CAAC,IAAI,CAAC;wBAChB,IAAI,EAAE,sBAAsB;wBAC5B,MAAM,EACJ,0EAA0E;qBAC7E,CAAC,CAAC;oBACH,SAAS;gBACX,CAAC;YACH,CAAC;YAED,2CAA2C;YAC3C,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACzD,MAAM,gBAAgB,GAAG,wBAAwB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;gBAEzE,MAAM,cAAc,GAAG;oBACrB,UAAU,EAAE,gBAAgB;oBAC5B,aAAa,EAAE,cAAc,sBAAsB,EAAE;iBACtD,CAAC;gBAEF,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC7C,2CAA2C;oBAC3C,IAAI,cAAc,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,MAAM,IAAI,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,IAAK,cAAc,CAAC,UAAU,CAAC,CAAC,CAAS,CAAC,IAAI,EAAE,CAAC;wBAC9N,MAAM,SAAS,GAAG,+BAA+B,CAAC,OAAO,CACvD,YAAY,EACZ,sBAAsB,CACvB,CAAC;wBACF,YAAY,CAAC,IAAI,CAAC,GAAG,SAAS,OAAQ,cAAc,CAAC,UAAU,CAAC,CAAC,CAAS,CAAC,IAAI,MAAM,CAAC,CAAC;oBACzF,CAAC;yBAAM,CAAC;wBACN,kEAAkE;wBAClE,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACrE,CAAC;gBACH,CAAC;qBAAM,IAAI,OAAO,cAAc,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;oBACzD,MAAM,SAAS,GAAG,+BAA+B,CAAC,OAAO,CACvD,YAAY,EACZ,sBAAsB,CACvB,CAAC;oBACF,YAAY,CAAC,IAAI,CAAC,GAAG,SAAS,OAAO,cAAc,CAAC,UAAU,MAAM,CAAC,CAAC;gBACxE,CAAC;gBAED,2BAA2B,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACzD,yBAAyB,CACvB,IAAI,CAAC,MAAM,EACX,aAAa,CAAC,IAAI,EAClB,CAAC,EACD,YAAY,EACZ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CACvB,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,YAAY,CAAC,IAAI,CAAC;oBAChB,IAAI,EAAE,sBAAsB;oBAC5B,MAAM,EAAE,eAAe,eAAe,CAAC,KAAK,CAAC,EAAE;iBAChD,CAAC,CAAC;gBACH,yBAAyB,CACvB,IAAI,CAAC,MAAM,EACX,aAAa,CAAC,IAAI,EAClB,CAAC,EACD,YAAY,EACZ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CACvB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,cAAc,GAAG,2CAA2C,IAAI,CAAC,SAAS,SAAS,CAAC;QACxF,IAAI,2BAA2B,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3C,cAAc,IAAI,qDAAqD,2BAA2B,CAAC,MAAM,eAAe,CAAC;YACzH,IAAI,2BAA2B,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;gBAC7C,cAAc,IAAI,0BAA0B,CAAC;gBAC7C,2BAA2B,CAAC,OAAO,CACjC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,cAAc,IAAI,OAAO,CAAC,MAAM,CAAC,CAC1C,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,cAAc,IAAI,2CAA2C,CAAC;gBAC9D,2BAA2B;qBACxB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;qBACZ,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,cAAc,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;gBACtD,cAAc,IAAI,YAAY,2BAA2B,CAAC,MAAM,GAAG,EAAE,UAAU,CAAC;YAClF,CAAC;QACH,CAAC;QAED,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,IAAI,2BAA2B,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7C,cAAc,IAAI,8DAA8D,CAAC;YACnF,CAAC;YACD,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBAC7B,cAAc,IAAI,eAAe,YAAY,CAAC,MAAM,eAAe,CAAC;YACtE,CAAC;iBAAM,CAAC;gBACN,cAAc,IAAI,eAAe,YAAY,CAAC,MAAM,+BAA+B,CAAC;YACtF,CAAC;YACD,YAAY;iBACT,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;iBACX,OAAO,CACN,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,cAAc,IAAI,OAAO,CAAC,CAAC,IAAI,eAAe,CAAC,CAAC,MAAM,KAAK,CAAC,CACrE,CAAC;YACJ,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,cAAc,IAAI,YAAY,YAAY,CAAC,MAAM,GAAG,CAAC,UAAU,CAAC;YAClE,CAAC;QACH,CAAC;aAAM,IACL,2BAA2B,CAAC,MAAM,KAAK,CAAC;YACxC,YAAY,CAAC,MAAM,KAAK,CAAC,EACzB,CAAC;YACD,cAAc,IAAI,8DAA8D,CAAC;QACnF,CAAC;QAED,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,YAAY,CAAC,IAAI,CACf,gEAAgE,CACjE,CAAC;QACJ,CAAC;QACD,OAAO;YACL,UAAU,EAAE,YAAY;YACxB,aAAa,EAAE,cAAc,CAAC,IAAI,EAAE;SACrC,CAAC;IACJ,CAAC"}