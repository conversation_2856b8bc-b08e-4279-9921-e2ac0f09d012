{"version": 3, "file": "useInput.js", "sourceRoot": "", "sources": ["../../src/hooks/useInput.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AAC9C,OAAO,EAAE,QAAQ,IAAI,WAAW,EAAE,MAAM,KAAK,CAAC;AA2B9C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,EACvB,QAAQ,EACR,OAAO,EACP,MAAM,EACN,MAAM,EACN,OAAO,EACP,QAAQ,GAAG,KAAK,EAChB,MAAM,GAAG,KAAK,EACd,QAAQ,GAAG,GAAG,EACd,SAAS,EACT,eAAe,EACf,SAAS,GACO,EAAkB,EAAE;IACpC,MAAM,CAAC,KAAK,EAAE,aAAa,CAAC,GAAG,QAAQ,CAAS,EAAE,CAAC,CAAC;IACpD,MAAM,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,QAAQ,CAAU,KAAK,CAAC,CAAC;IAEvE,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,QAAgB,EAAE,EAAE;QAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,yBAAyB;YACzB,MAAM,YAAY,GAAG,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAEzE,8BAA8B;YAC9B,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC1C,OAAO;YACT,CAAC;YAED,aAAa,CAAC,YAAY,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;IAErC,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE;QAClC,aAAa,CAAC,EAAE,CAAC,CAAC;QAClB,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,0DAA0D;IAC1D,MAAM,sBAAsB,GAAG,WAAW,CAAC,CAAC,IAAY,EAAW,EAAE;QACnE,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACpC,OAAO,QAAQ,IAAI,EAAE,IAAI,QAAQ,IAAI,GAAG,CAAC;IAC3C,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,qBAAqB,GAAG,eAAe,IAAI,sBAAsB,CAAC;IAExE,WAAW,CAAC,CAAC,SAAS,EAAE,GAAG,EAAE,EAAE;QAC7B,IAAI,QAAQ;YAAE,OAAO;QAErB,kCAAkC;QAClC,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;YACb,QAAQ,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC;gBAChC,KAAK,GAAG;oBACN,IAAI,MAAM,EAAE,CAAC;wBACX,MAAM,EAAE,CAAC;oBACX,CAAC;yBAAM,CAAC;wBACN,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBAClB,CAAC;oBACD,OAAO;gBACT,KAAK,GAAG;oBACN,IAAI,OAAO,EAAE,CAAC;wBACZ,OAAO,EAAE,CAAC;oBACZ,CAAC;oBACD,OAAO;gBACT,KAAK,GAAG;oBACN,IAAI,MAAM,EAAE,CAAC;wBACX,MAAM,EAAE,CAAC;oBACX,CAAC;oBACD,OAAO;gBACT,KAAK,GAAG;oBACN,iEAAiE;oBACjE,OAAO;gBACT;oBACE,OAAO;YACX,CAAC;QACH,CAAC;QAED,mBAAmB;QACnB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;gBACjB,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;gBACvB,UAAU,EAAE,CAAC;YACf,CAAC;YACD,OAAO;QACT,CAAC;QAED,0BAA0B;QAC1B,IAAI,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YAChC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC1B,OAAO;QACT,CAAC;QAED,wDAAwD;QACxD,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtC,4BAA4B;YAC5B,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAEzB,4BAA4B;YAC5B,MAAM,eAAe,GAAG,SAAS;iBAC9B,KAAK,CAAC,EAAE,CAAC;iBACT,MAAM,CAAC,qBAAqB,CAAC;iBAC7B,IAAI,CAAC,EAAE,CAAC,CAAC;YAEZ,IAAI,eAAe,EAAE,CAAC;gBACpB,yBAAyB;gBACzB,MAAM,cAAc,GAAG,SAAS;oBAC9B,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC;oBACpD,CAAC,CAAC,eAAe,CAAC;gBAEpB,8BAA8B;gBAC9B,MAAM,QAAQ,GAAG,KAAK,GAAG,cAAc,CAAC;gBACxC,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACtC,OAAO;gBACT,CAAC;gBAED,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,cAAc,CAAC,CAAC;gBAE7C,kCAAkC;gBAClC,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO,CAAC,cAAc,CAAC,CAAC;gBAC1B,CAAC;YACH,CAAC;YACD,OAAO;QACT,CAAC;QAED,oDAAoD;QACpD,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YAClE,IAAI,qBAAqB,CAAC,SAAS,CAAC,EAAE,CAAC;gBACrC,mBAAmB;gBACnB,IAAI,SAAS,IAAI,KAAK,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;oBAC3C,OAAO;gBACT,CAAC;gBAED,8BAA8B;gBAC9B,MAAM,QAAQ,GAAG,KAAK,GAAG,SAAS,CAAC;gBACnC,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACtC,OAAO;gBACT,CAAC;gBAED,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;gBACxC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,4CAA4C;IAC5C,MAAM,YAAY,GAAG,MAAM;QACzB,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;QAC/B,CAAC,CAAC,KAAK,CAAC;IAEV,OAAO;QACL,KAAK;QACL,YAAY;QACZ,QAAQ;QACR,UAAU;QACV,UAAU,EAAE,QAAQ;QACpB,eAAe;KAChB,CAAC;AACJ,CAAC,CAAC"}