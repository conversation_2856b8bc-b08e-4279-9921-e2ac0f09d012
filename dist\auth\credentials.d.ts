/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { ProviderType } from '../providers/manager.js';
export interface StoredCredentials {
    providers: Record<ProviderType, {
        apiKey?: string;
        model?: string;
        baseUrl?: string;
        lastUsed?: number;
    }>;
    defaultProvider?: ProviderType;
    lastUpdated: number;
}
export declare class CredentialsManager {
    private credentialsPath;
    private credentials;
    constructor();
    /**
     * Loads credentials from disk
     */
    private loadCredentials;
    /**
     * Saves credentials to disk
     */
    private saveCredentials;
    /**
     * Sets API key for a provider
     */
    setApiKey(provider: ProviderType, apiKey: string): void;
    /**
     * Gets API key for a provider
     */
    getApiKey(provider: ProviderType): string | undefined;
    /**
     * Sets model for a provider
     */
    setModel(provider: ProviderType, model: string): void;
    /**
     * Gets model for a provider
     */
    getModel(provider: ProviderType): string | undefined;
    /**
     * Sets base URL for a provider
     */
    setBaseUrl(provider: ProviderType, baseUrl: string): void;
    /**
     * Gets base URL for a provider
     */
    getBaseUrl(provider: ProviderType): string | undefined;
    /**
     * Sets default provider
     */
    setDefaultProvider(provider: ProviderType): void;
    /**
     * Gets default provider
     */
    getDefaultProvider(): ProviderType | undefined;
    /**
     * Checks if a provider is configured
     */
    isProviderConfigured(provider: ProviderType): boolean;
    /**
     * Gets all configured providers
     */
    getConfiguredProviders(): ProviderType[];
    /**
     * Gets provider configuration
     */
    getProviderConfig(provider: ProviderType): {
        apiKey?: string;
        model?: string;
        baseUrl?: string;
        lastUsed?: number;
    };
    /**
     * Removes provider configuration
     */
    removeProvider(provider: ProviderType): void;
    /**
     * Clears all credentials
     */
    clearAll(): void;
    /**
     * Gets the most recently used provider
     */
    getMostRecentProvider(): ProviderType | undefined;
    /**
     * Checks if there are any valid credentials configured
     */
    hasValidCredentials(): boolean;
    /**
     * Validates API key format for a provider
     */
    validateApiKey(provider: ProviderType, apiKey: string): boolean;
}
//# sourceMappingURL=credentials.d.ts.map