/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { Config } from '../config/config.js';
import { BaseTool, ToolResult } from './tools.js';
export interface GitBranchParams {
    directory?: string;
    action: 'list' | 'create' | 'delete' | 'switch' | 'merge';
    branch_name?: string;
    source_branch?: string;
    force?: boolean;
    remote?: boolean;
    all?: boolean;
    [key: string]: unknown;
}
export declare class GitBranchTool extends BaseTool<GitBranchParams, ToolResult> {
    private readonly config;
    static Name: string;
    constructor(config: Config);
    validateToolParams(params: GitBranchParams): string | null;
    private executeGitCommand;
    private getCurrentBranch;
    private formatBranchList;
    execute(params: GitBranchParams, abortSignal: AbortSignal): Promise<ToolResult>;
}
//# sourceMappingURL=git-branch.d.ts.map