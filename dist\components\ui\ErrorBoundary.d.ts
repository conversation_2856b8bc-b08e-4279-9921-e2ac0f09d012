/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import React, { Component, ReactNode } from 'react';
import { ThemeManager } from '../../themes/themes.js';
interface ErrorBoundaryProps {
    children: ReactNode;
    themeManager: ThemeManager;
    fallback?: ReactNode;
}
interface ErrorBoundaryState {
    hasError: boolean;
    error?: Error;
}
export declare class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
    constructor(props: ErrorBoundaryProps);
    static getDerivedStateFromError(error: Error): ErrorBoundaryState;
    componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void;
    render(): string | number | boolean | Iterable<React.ReactNode> | import("react/jsx-runtime").JSX.Element | null | undefined;
}
export {};
//# sourceMappingURL=ErrorBoundary.d.ts.map