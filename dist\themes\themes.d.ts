/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
export interface Theme {
    name: string;
    description: string;
    colors: {
        primary: (text: string) => string;
        secondary: (text: string) => string;
        accent: (text: string) => string;
        success: (text: string) => string;
        warning: (text: string) => string;
        error: (text: string) => string;
        info: (text: string) => string;
        muted: (text: string) => string;
        background: (text: string) => string;
        border: (text: string) => string;
        highlight: (text: string) => string;
    };
    symbols: {
        bullet: string;
        arrow: string;
        check: string;
        cross: string;
        info: string;
        warning: string;
        question: string;
        loading: string[];
    };
}
export declare const themes: Record<string, Theme>;
export declare class ThemeManager {
    private currentTheme;
    private themeSelected;
    constructor(themeName?: string);
    /**
     * Gets the current theme
     */
    getCurrentTheme(): Theme;
    /**
     * Sets the current theme
     */
    setTheme(themeName: string): void;
    /**
     * Checks if a theme has been explicitly selected
     */
    isThemeSelected(): boolean;
    /**
     * Gets all available themes
     */
    getAvailableThemes(): Array<{
        name: string;
        theme: Theme;
    }>;
    /**
     * Gets theme names
     */
    getThemeNames(): string[];
    /**
     * Applies theme colors to text
     */
    primary(text: string): string;
    secondary(text: string): string;
    accent(text: string): string;
    success(text: string): string;
    warning(text: string): string;
    error(text: string): string;
    info(text: string): string;
    muted(text: string): string;
    highlight(text: string): string;
    /**
     * Gets theme symbols
     */
    getSymbol(symbol: keyof Theme['symbols']): string | string[];
}
//# sourceMappingURL=themes.d.ts.map