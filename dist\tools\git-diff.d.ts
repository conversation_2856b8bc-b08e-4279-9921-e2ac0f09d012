/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { Config } from '../config/config.js';
import { BaseTool, ToolResult } from './tools.js';
export interface GitDiffParams {
    directory?: string;
    file_path?: string;
    staged?: boolean;
    commit_range?: string;
    context_lines?: number;
    word_diff?: boolean;
    stat_only?: boolean;
    [key: string]: unknown;
}
export declare class GitDiffTool extends BaseTool<GitDiffParams, ToolResult> {
    private readonly config;
    static Name: string;
    constructor(config: Config);
    validateToolParams(params: GitDiffParams): string | null;
    private executeGitCommand;
    private formatDiffOutput;
    private formatStatOutput;
    private formatUnifiedDiff;
    execute(params: GitDiffParams, abortSignal: AbortSignal): Promise<ToolResult>;
}
//# sourceMappingURL=git-diff.d.ts.map