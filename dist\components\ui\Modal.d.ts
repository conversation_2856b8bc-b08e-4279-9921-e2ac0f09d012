/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import React from 'react';
import { ThemeManager } from '../../themes/themes.js';
interface ModalAction {
    label: string;
    key: string;
    handler: () => void;
    variant?: 'primary' | 'secondary' | 'danger';
}
interface ModalProps {
    themeManager: ThemeManager;
    title: string;
    children: React.ReactNode;
    isOpen: boolean;
    onClose?: () => void;
    actions?: ModalAction[];
    width?: number;
    height?: number;
    closable?: boolean;
    variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
}
export declare const Modal: React.FC<ModalProps>;
interface ConfirmModalProps {
    themeManager: ThemeManager;
    title: string;
    message: string;
    isOpen: boolean;
    onConfirm: () => void;
    onCancel: () => void;
    confirmLabel?: string;
    cancelLabel?: string;
    variant?: 'default' | 'danger';
}
export declare const ConfirmModal: React.FC<ConfirmModalProps>;
interface AlertModalProps {
    themeManager: ThemeManager;
    title: string;
    message: string;
    isOpen: boolean;
    onClose: () => void;
    variant?: 'success' | 'warning' | 'error' | 'info';
}
export declare const AlertModal: React.FC<AlertModalProps>;
export {};
//# sourceMappingURL=Modal.d.ts.map