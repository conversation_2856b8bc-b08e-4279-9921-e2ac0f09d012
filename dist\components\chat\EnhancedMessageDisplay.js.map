{"version": 3, "file": "EnhancedMessageDisplay.js", "sourceRoot": "", "sources": ["../../../src/components/chat/EnhancedMessageDisplay.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAG1B,OAAO,EAAE,UAAU,EAAY,MAAM,qBAAqB,CAAC;AAc3D,MAAM,CAAC,MAAM,sBAAsB,GAA0C,CAAC,EAC5E,YAAY,EACZ,QAAQ,EACR,SAAS,GAAG,EAAE,EACd,QAAQ,GAAG,EAAE,EACb,cAAc,GAAG,IAAI,EACrB,eAAe,GAAG,KAAK,EACvB,YAAY,GAAG,IAAI,EACnB,UAAU,GAAG,IAAI,EACjB,KAAK,GAAG,eAAe,GACxB,EAAE,EAAE;IACH,wCAAwC;IACxC,MAAM,0BAA0B,GAAG,GAAe,EAAE;QAClD,MAAM,KAAK,GAAe,EAAE,CAAC;QAE7B,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE;YACzC,kBAAkB;YAClB,KAAK,CAAC,IAAI,CAAC;gBACT,EAAE,EAAE,WAAW,YAAY,SAAS;gBACpC,OAAO,EAAE,GAAG,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,GAAG;gBAC5D,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,KAAK,EAAE,MAAM;gBACb,KAAK,EAAE,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW;gBACvD,QAAQ,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aACpD,CAAC,CAAC;YAEH,mCAAmC;YACnC,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACjD,YAAY,CAAC,OAAO,CAAC,CAAC,IAAY,EAAE,SAAiB,EAAE,EAAE;gBACvD,KAAK,CAAC,IAAI,CAAC;oBACT,EAAE,EAAE,WAAW,YAAY,YAAY,SAAS,EAAE;oBAClD,OAAO,EAAE,IAAI,IAAI,GAAG,EAAE,mCAAmC;oBACzD,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,KAAK,EAAE,QAAQ;oBACf,KAAK,EAAE,eAAe,CAAC,IAAI,CAAC;oBAC5B,QAAQ,EAAE;wBACR,SAAS,EAAE,OAAO,CAAC,EAAE;wBACrB,IAAI,EAAE,SAAS;wBACf,SAAS;qBACV;iBACF,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,gEAAgE;YAChE,IAAI,YAAY,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACvC,KAAK,CAAC,IAAI,CAAC;oBACT,EAAE,EAAE,aAAa,YAAY,EAAE;oBAC/B,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;oBAChD,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,KAAK,EAAE,QAAQ;oBACf,KAAK,EAAE,OAAO;oBACd,QAAQ,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE;iBAChC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,CAAC,IAAY,EAAqB,EAAE;QAC1D,iEAAiE;QACjE,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;YAAE,OAAO,QAAQ,CAAC;QACrE,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YAAE,OAAO,SAAS,CAAC;QAC5C,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;YAAE,OAAO,WAAW,CAAC;QAC/C,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YAAE,OAAO,MAAM,CAAC;QAClE,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,OAAO,CAAC;QACrE,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,SAAS,CAAC;QAC3E,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,SAAS,CAAC;QAC3E,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;YAAE,OAAO,MAAM,CAAC,CAAC,OAAO;QAC3D,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;YAAE,OAAO,QAAQ,CAAC,CAAC,cAAc;QAC1D,OAAO,SAAS,CAAC,CAAC,gBAAgB;IACpC,CAAC,CAAC;IAEF,MAAM,SAAS,GAAG,0BAA0B,EAAE,CAAC;IAE/C,MAAM,qBAAqB,GAAG,CAAC,aAAyB,EAAE,EAAE;QAC1D,uEAAuE;QACvE,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,CAAC,eAAgD,EAAE,EAAE;QACxE,mEAAmE;QACnE,mDAAmD;IACrD,CAAC,CAAC;IAEF,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC1B,OAAO,CACL,KAAC,UAAU,IACT,YAAY,EAAE,YAAY,EAC1B,KAAK,EAAE,EAAE,EACT,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,QAAQ,EAClB,KAAK,EAAE,KAAK,EACZ,YAAY,EAAC,wCAAwC,EACrD,UAAU,EAAE,KAAK,EACjB,cAAc,EAAE,KAAK,EACrB,eAAe,EAAE,KAAK,GACtB,CACH,CAAC;IACJ,CAAC;IAED,OAAO,CACL,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,YACzB,KAAC,UAAU,IACT,YAAY,EAAE,YAAY,EAC1B,KAAK,EAAE,SAAS,EAChB,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,QAAQ,EAClB,cAAc,EAAE,cAAc,EAC9B,eAAe,EAAE,eAAe,EAChC,YAAY,EAAE,YAAY,EAC1B,eAAe,EAAE,IAAI,EACrB,UAAU,EAAE,UAAU,EACtB,UAAU,EAAE,IAAI,EAChB,cAAc,EAAE,IAAI,EACpB,KAAK,EAAE,KAAK,EACZ,iBAAiB,EAAE,qBAAqB,EACxC,QAAQ,EAAE,YAAY,EACtB,OAAO,EAAC,UAAU,EAClB,WAAW,EAAC,OAAO,EACnB,WAAW,EAAC,MAAM,EAClB,OAAO,EAAE,CAAC,EACV,YAAY,EAAC,wBAAwB,EACrC,cAAc,EAAC,qBAAqB,GACpC,GACE,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,iDAAiD;AACjD,MAAM,CAAC,MAAM,qBAAqB,GAAsF,CAAC,KAAK,EAAE,EAAE,CAAC,CACjI,KAAC,sBAAsB,OACjB,KAAK,EACT,cAAc,EAAE,KAAK,EACrB,eAAe,EAAE,KAAK,GACtB,CACH,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAAuG,CAAC,KAAK,EAAE,EAAE,CAAC,CACnJ,KAAC,sBAAsB,OACjB,KAAK,EACT,cAAc,EAAE,IAAI,EACpB,eAAe,EAAE,IAAI,EACrB,YAAY,EAAE,IAAI,GAClB,CACH,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA4E,CAAC,KAAK,EAAE,EAAE,CAAC,CACxH,KAAC,sBAAsB,OACjB,KAAK,EACT,cAAc,EAAE,IAAI,EACpB,KAAK,EAAC,UAAU,GAChB,CACH,CAAC"}