import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useEffect, useRef } from 'react';
import { Box, Text } from 'ink';
import { MessageItem } from './MessageItem.js';
import { LoadingIndicator } from '../ui/LoadingIndicator.js';
import { MaxBoxSized } from '../ui/MaxBoxSized.js';
export const MessageList = ({ messages, themeManager, isLoading, showTimestamps = false, maxHeight, }) => {
    const messagesEndRef = useRef(null);
    // Auto-scroll to bottom when new messages arrive
    useEffect(() => {
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
        }
    }, [messages]);
    const renderEmptyState = () => {
        if (messages.length === 0 && !isLoading) {
            return (_jsx(Box, { justifyContent: "center", alignItems: "center", minHeight: 5, children: _jsx(Text, { children: themeManager.muted('Start a conversation by typing a message...') }) }));
        }
        return null;
    };
    const renderLoadingIndicator = () => {
        if (isLoading && messages.length === 0) {
            return (_jsxs(Box, { children: [_jsx(Text, { children: themeManager.secondary('Arien: ') }), _jsx(LoadingIndicator, { themeManager: themeManager, text: "Thinking", type: "dots" })] }));
        }
        return null;
    };
    const renderMessages = () => {
        return messages.map((message) => (_jsx(MessageItem, { message: message, themeManager: themeManager, showTimestamp: showTimestamps }, message.id)));
    };
    return (_jsxs(MaxBoxSized, { themeManager: themeManager, maxHeight: maxHeight, flexDirection: "column", flexGrow: 1, marginBottom: 1, overflowY: "hidden", responsive: true, autoResize: true, children: [renderEmptyState(), renderMessages(), renderLoadingIndicator(), _jsx("div", { ref: messagesEndRef })] }));
};
//# sourceMappingURL=MessageList.js.map