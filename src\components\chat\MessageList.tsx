/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useEffect, useRef } from 'react';
import { Box, Text } from 'ink';
import { ThemeManager } from '../../themes/themes.js';
import { ChatMessage } from '../../hooks/useChat.js';
import { MessageItem } from './MessageItem.js';
import { LoadingIndicator } from '../ui/LoadingIndicator.js';
import { MaxBoxSized } from '../ui/MaxBoxSized.js';

interface MessageListProps {
  messages: ChatMessage[];
  themeManager: ThemeManager;
  isLoading: boolean;
  showTimestamps?: boolean;
  maxHeight?: number;
}

export const MessageList: React.FC<MessageListProps> = ({
  messages,
  themeManager,
  isLoading,
  showTimestamps = false,
  maxHeight,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  const renderEmptyState = () => {
    if (messages.length === 0 && !isLoading) {
      return (
        <Box justifyContent="center" alignItems="center" minHeight={5}>
          <Text>{themeManager.muted('Start a conversation by typing a message...')}</Text>
        </Box>
      );
    }
    return null;
  };

  const renderLoadingIndicator = () => {
    if (isLoading && messages.length === 0) {
      return (
        <Box>
          <Text>{themeManager.secondary('Arien: ')}</Text>
          <LoadingIndicator themeManager={themeManager} text="Thinking" type="dots" />
        </Box>
      );
    }
    return null;
  };

  const renderMessages = () => {
    return messages.map((message) => (
      <MessageItem
        key={message.id}
        message={message}
        themeManager={themeManager}
        showTimestamp={showTimestamps}
      />
    ));
  };

  return (
    <MaxBoxSized
      themeManager={themeManager}
      maxHeight={maxHeight}
      flexDirection="column"
      flexGrow={1}
      marginBottom={1}
      overflowY="hidden"
      responsive={true}
      autoResize={true}
    >
      {renderEmptyState()}
      {renderMessages()}
      {renderLoadingIndicator()}
      <div ref={messagesEndRef} />
    </MaxBoxSized>
  );
};
