/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { EventEmitter } from 'events';
export interface TerminalInputEvent {
    type: 'character' | 'paste' | 'special';
    data: string;
    key?: {
        ctrl?: boolean;
        meta?: boolean;
        shift?: boolean;
        return?: boolean;
        backspace?: boolean;
        delete?: boolean;
        escape?: boolean;
        upArrow?: boolean;
        downArrow?: boolean;
        leftArrow?: boolean;
        rightArrow?: boolean;
    };
    timestamp: number;
}
export interface TerminalInputOptions {
    masked?: boolean;
    maskChar?: string;
    maxLength?: number;
    validator?: (input: string) => boolean;
    characterFilter?: (char: string) => boolean;
    pasteValidator?: (pastedText: string) => boolean;
    realTimeValidation?: boolean;
}
export interface TerminalInputState {
    value: string;
    displayValue: string;
    isValid: boolean;
    validationMessage: string;
    isPasteDetected: boolean;
    cursorPosition: number;
    isDisabled: boolean;
}
/**
 * Centralized terminal input manager for consistent input handling across the application
 */
export declare class TerminalInputManager extends EventEmitter {
    private state;
    private options;
    private pasteDetectionTimeout;
    constructor(options?: TerminalInputOptions);
    /**
     * Default character filter for printable ASCII characters
     */
    private defaultCharacterFilter;
    /**
     * Process input event from terminal
     */
    processInput(inputChar: string, key: any): boolean;
    /**
     * Determine the type of input event
     */
    private determineInputType;
    /**
     * Handle paste operations
     */
    private handlePaste;
    /**
     * Handle single character input
     */
    private handleCharacter;
    /**
     * Handle special key operations
     */
    private handleSpecialKey;
    /**
     * Update the input value and display value
     */
    private updateValue;
    /**
     * Set paste detection state
     */
    private setPasteDetected;
    /**
     * Validate the current input
     */
    private validateInput;
    /**
     * Get current state
     */
    getState(): Readonly<TerminalInputState>;
    /**
     * Clear the input
     */
    clear(): void;
    /**
     * Set the input value programmatically
     */
    setValue(value: string): void;
    /**
     * Enable or disable the input
     */
    setDisabled(disabled: boolean): void;
    /**
     * Update options
     */
    updateOptions(newOptions: Partial<TerminalInputOptions>): void;
    /**
     * Cleanup resources
     */
    destroy(): void;
}
//# sourceMappingURL=TerminalInputManager.d.ts.map