/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import fs from 'fs';
import path from 'path';
import { BaseTool } from './tools.js';
import { SchemaValidator } from '../utils/schemaValidator.js';
import { getErrorMessage } from '../utils/errors.js';
import { spawn } from 'child_process';
export class GitStatusTool extends BaseTool {
    config;
    static Name = 'git_status';
    constructor(config) {
        super(GitStatusTool.Name, 'Git Status', `Shows the working tree status of a git repository. Displays information about:
- Current branch and tracking information
- Staged files ready for commit
- Unstaged changes in the working directory
- Untracked files
- Optionally ignored files

Returns structured information about the repository state including file statuses and branch information.`, {
            type: 'object',
            properties: {
                directory: {
                    type: 'string',
                    description: '(OPTIONAL) Directory to check git status in. Must be relative to the project root directory. Defaults to project root.',
                },
                porcelain: {
                    type: 'boolean',
                    description: '(OPTIONAL) Use porcelain format for machine-readable output. Defaults to false for human-readable format.',
                },
                show_ignored: {
                    type: 'boolean',
                    description: '(OPTIONAL) Show ignored files in addition to tracked and untracked files. Defaults to false.',
                },
            },
            required: [],
        }, true, // output is markdown
        false);
        this.config = config;
    }
    validateToolParams(params) {
        const validation = SchemaValidator.validate(params, this.parameterSchema);
        if (!validation.isValid) {
            return `Parameters failed schema validation: ${validation.errors.join(', ')}`;
        }
        if (params.directory) {
            if (path.isAbsolute(params.directory)) {
                return 'Directory cannot be absolute. Must be relative to the project root directory.';
            }
            const directory = path.resolve(this.config.getTargetDir(), params.directory);
            if (!fs.existsSync(directory)) {
                return 'Directory must exist.';
            }
        }
        return null;
    }
    async executeGitCommand(args, cwd) {
        return new Promise((resolve) => {
            const process = spawn('git', args, {
                cwd,
                stdio: ['ignore', 'pipe', 'pipe'],
            });
            let stdout = '';
            let stderr = '';
            process.stdout?.on('data', (data) => {
                stdout += data.toString();
            });
            process.stderr?.on('data', (data) => {
                stderr += data.toString();
            });
            process.on('close', (exitCode) => {
                resolve({ stdout, stderr, exitCode: exitCode || 0 });
            });
        });
    }
    parseGitStatus(output) {
        const lines = output.split('\n').filter(line => line.trim());
        const result = {
            branch: 'unknown',
            staged: [],
            unstaged: [],
            untracked: [],
            clean: true,
        };
        for (const line of lines) {
            if (line.startsWith('## ')) {
                // Branch information
                const branchInfo = line.substring(3);
                const parts = branchInfo.split('...');
                result.branch = parts[0];
                if (parts[1]) {
                    const trackingInfo = parts[1];
                    const aheadMatch = trackingInfo.match(/ahead (\d+)/);
                    const behindMatch = trackingInfo.match(/behind (\d+)/);
                    if (aheadMatch)
                        result.ahead = parseInt(aheadMatch[1]);
                    if (behindMatch)
                        result.behind = parseInt(behindMatch[1]);
                }
            }
            else if (line.length >= 3) {
                const stagedStatus = line[0];
                const unstagedStatus = line[1];
                const fileName = line.substring(3);
                if (stagedStatus !== ' ' && stagedStatus !== '?') {
                    result.staged.push({ status: stagedStatus, file: fileName });
                    result.clean = false;
                }
                if (unstagedStatus !== ' ' && unstagedStatus !== '?') {
                    result.unstaged.push({ status: unstagedStatus, file: fileName });
                    result.clean = false;
                }
                if (stagedStatus === '?' && unstagedStatus === '?') {
                    result.untracked.push(fileName);
                    result.clean = false;
                }
            }
        }
        return result;
    }
    formatStatusOutput(status, porcelain) {
        if (porcelain) {
            return JSON.stringify(status, null, 2);
        }
        let output = `# Git Status\n\n`;
        // Branch information
        output += `**Branch:** ${status.branch}`;
        if (status.ahead || status.behind) {
            const tracking = [];
            if (status.ahead)
                tracking.push(`ahead ${status.ahead}`);
            if (status.behind)
                tracking.push(`behind ${status.behind}`);
            output += ` (${tracking.join(', ')})`;
        }
        output += '\n\n';
        if (status.clean) {
            output += '✅ **Working tree clean** - no changes to commit\n';
            return output;
        }
        // Staged changes
        if (status.staged.length > 0) {
            output += '## 📦 Staged Changes (ready to commit)\n\n';
            for (const item of status.staged) {
                const statusIcon = this.getStatusIcon(item.status);
                output += `- ${statusIcon} \`${item.file}\`\n`;
            }
            output += '\n';
        }
        // Unstaged changes
        if (status.unstaged.length > 0) {
            output += '## 📝 Unstaged Changes (not ready to commit)\n\n';
            for (const item of status.unstaged) {
                const statusIcon = this.getStatusIcon(item.status);
                output += `- ${statusIcon} \`${item.file}\`\n`;
            }
            output += '\n';
        }
        // Untracked files
        if (status.untracked.length > 0) {
            output += '## ❓ Untracked Files\n\n';
            for (const file of status.untracked) {
                output += `- 🆕 \`${file}\`\n`;
            }
            output += '\n';
        }
        // Ignored files
        if (status.ignored && status.ignored.length > 0) {
            output += '## 🚫 Ignored Files\n\n';
            for (const file of status.ignored) {
                output += `- 🚫 \`${file}\`\n`;
            }
            output += '\n';
        }
        return output;
    }
    getStatusIcon(status) {
        switch (status) {
            case 'A': return '➕'; // Added
            case 'M': return '✏️'; // Modified
            case 'D': return '🗑️'; // Deleted
            case 'R': return '🔄'; // Renamed
            case 'C': return '📋'; // Copied
            case 'U': return '⚠️'; // Unmerged
            case 'T': return '🔧'; // Type changed
            default: return '❓'; // Unknown
        }
    }
    async execute(params, abortSignal) {
        const validationError = this.validateToolParams(params);
        if (validationError) {
            return {
                llmContent: `Error: ${validationError}`,
                returnDisplay: `Git Status Error: ${validationError}`,
            };
        }
        if (abortSignal.aborted) {
            return {
                llmContent: 'Git status command was cancelled by user.',
                returnDisplay: 'Git status cancelled.',
            };
        }
        try {
            const workingDir = params.directory
                ? path.resolve(this.config.getTargetDir(), params.directory)
                : this.config.getTargetDir();
            // Check if directory is a git repository
            const gitDirCheck = await this.executeGitCommand(['rev-parse', '--git-dir'], workingDir);
            if (gitDirCheck.exitCode !== 0) {
                return {
                    llmContent: `Error: Not a git repository (or any of the parent directories)`,
                    returnDisplay: 'Error: Not a git repository',
                };
            }
            // Get git status
            const args = ['status', '--porcelain=v1', '--branch'];
            if (params.show_ignored) {
                args.push('--ignored');
            }
            const result = await this.executeGitCommand(args, workingDir);
            if (result.exitCode !== 0) {
                return {
                    llmContent: `Git status failed: ${result.stderr}`,
                    returnDisplay: `Git status error: ${result.stderr}`,
                };
            }
            const statusInfo = this.parseGitStatus(result.stdout);
            const formattedOutput = this.formatStatusOutput(statusInfo, params.porcelain || false);
            return {
                llmContent: formattedOutput,
                returnDisplay: `Git status for ${path.basename(workingDir)} - ${statusInfo.clean ? 'Clean' : 'Changes detected'}`,
            };
        }
        catch (error) {
            const errorMessage = `Error executing git status: ${getErrorMessage(error)}`;
            return {
                llmContent: errorMessage,
                returnDisplay: errorMessage,
            };
        }
    }
}
//# sourceMappingURL=git-status.js.map