/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { Config } from '../config/config.js';
import { GitStatusTool } from './git-status.js';
import { GitDiffTool } from './git-diff.js';
import { GitBranchTool } from './git-branch.js';
import { GitCommitTool } from './git-commit.js';
import { GitLogTool } from './git-log.js';
import { ToolRegistry } from './tool-registry.js';
/**
 * Git Tools Collection
 *
 * Comprehensive git integration tools for repository management:
 * - git_status: Show repository status and file changes
 * - git_diff: Display differences between commits, branches, or working tree
 * - git_branch: Manage branches (list, create, delete, switch, merge)
 * - git_commit: Create commits with validation and best practices
 * - git_log: Explore commit history with filtering and formatting options
 */
/**
 * Creates and returns all git tools instances
 */
export declare function createGitTools(config: Config): {
    gitStatus: GitStatusTool;
    gitDiff: GitDiffTool;
    gitBranch: GitBranchTool;
    gitCommit: GitCommitTool;
    gitLog: GitLogTool;
};
/**
 * Registers all git tools with the provided tool registry
 */
export declare function registerGitTools(toolRegistry: ToolRegistry, config: Config): void;
/**
 * Git tool names for easy reference
 */
export declare const GIT_TOOL_NAMES: {
    readonly STATUS: "git_status";
    readonly DIFF: "git_diff";
    readonly BRANCH: "git_branch";
    readonly COMMIT: "git_commit";
    readonly LOG: "git_log";
};
/**
 * Export individual tool classes for direct usage
 */
export { GitStatusTool, GitDiffTool, GitBranchTool, GitCommitTool, GitLogTool, };
/**
 * Export parameter interfaces for type safety
 */
export type { GitStatusParams, GitStatusInfo, } from './git-status.js';
export type { GitDiffParams, } from './git-diff.js';
export type { GitBranchParams, } from './git-branch.js';
export type { GitCommitParams, } from './git-commit.js';
export type { GitLogParams, CommitInfo, } from './git-log.js';
//# sourceMappingURL=git-tools.d.ts.map