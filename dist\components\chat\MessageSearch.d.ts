/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import React from 'react';
import { ThemeManager } from '../../themes/themes.js';
import { ChatMessage } from '../../hooks/useChat.js';
interface MessageSearchProps {
    themeManager: ThemeManager;
    messages: ChatMessage[];
    isOpen: boolean;
    onClose: () => void;
    onSelectMessage?: (messageIndex: number) => void;
}
export declare const MessageSearch: React.FC<MessageSearchProps>;
export {};
//# sourceMappingURL=MessageSearch.d.ts.map