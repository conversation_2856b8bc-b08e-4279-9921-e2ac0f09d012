import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import { Box, Text } from 'ink';
export const ProgressBar = ({ themeManager, progress, width = 40, showPercentage = true, showLabel = false, label, variant = 'default', animated = false, indeterminate = false, }) => {
    // Clamp progress between 0 and 100
    const clampedProgress = Math.max(0, Math.min(100, progress));
    const getVariantColor = () => {
        switch (variant) {
            case 'success':
                return themeManager.success;
            case 'warning':
                return themeManager.warning;
            case 'error':
                return themeManager.error;
            case 'info':
                return themeManager.info;
            default:
                return themeManager.primary;
        }
    };
    const renderProgressBar = () => {
        if (indeterminate) {
            // Indeterminate progress bar with moving pattern
            const pattern = '▓▒░';
            const repeatedPattern = pattern.repeat(Math.ceil(width / pattern.length));
            const truncatedPattern = repeatedPattern.substring(0, width);
            return (_jsxs(Text, { children: ["[", getVariantColor()(truncatedPattern), "]"] }));
        }
        const filledWidth = Math.round((clampedProgress / 100) * width);
        const emptyWidth = width - filledWidth;
        const filledChar = animated ? '█' : '█';
        const emptyChar = '░';
        const filledPart = getVariantColor()(filledChar.repeat(filledWidth));
        const emptyPart = themeManager.muted(emptyChar.repeat(emptyWidth));
        return (_jsxs(Text, { children: ["[", filledPart, emptyPart, "]"] }));
    };
    const renderPercentage = () => {
        if (!showPercentage || indeterminate)
            return null;
        return (_jsxs(Text, { children: [' ', getVariantColor()(`${clampedProgress.toFixed(0)}%`)] }));
    };
    const renderLabel = () => {
        if (!showLabel || !label)
            return null;
        return (_jsx(Box, { marginBottom: 1, children: _jsx(Text, { children: themeManager.secondary(label) }) }));
    };
    const renderProgressText = () => {
        if (indeterminate) {
            return (_jsxs(Text, { children: [' ', themeManager.muted('Processing...')] }));
        }
        return null;
    };
    return (_jsxs(Box, { flexDirection: "column", children: [renderLabel(), _jsxs(Box, { children: [renderProgressBar(), renderPercentage(), renderProgressText()] })] }));
};
// Specialized progress bar variants
export const SuccessProgressBar = (props) => (_jsx(ProgressBar, { ...props, variant: "success" }));
export const WarningProgressBar = (props) => (_jsx(ProgressBar, { ...props, variant: "warning" }));
export const ErrorProgressBar = (props) => (_jsx(ProgressBar, { ...props, variant: "error" }));
export const InfoProgressBar = (props) => (_jsx(ProgressBar, { ...props, variant: "info" }));
export const IndeterminateProgressBar = (props) => (_jsx(ProgressBar, { ...props, indeterminate: true }));
//# sourceMappingURL=ProgressBar.js.map