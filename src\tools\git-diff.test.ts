/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { GitDiffTool, GitDiffParams } from './git-diff.js';
import { Config, ApprovalMode, ConfigParameters } from '../config/config.js';
import fs from 'fs';
import { spawn } from 'child_process';

// Mock fs
vi.mock('fs');
const mockFs = vi.mocked(fs);

// Mock child_process
vi.mock('child_process');
const mockSpawn = vi.mocked(spawn);

// Mock config
const mockConfigParams: ConfigParameters = {
  cwd: '/test/project',
  model: 'test-model',
  embeddingModel: 'test-embedding',
  sandbox: false,
  targetDir: '/test/project',
  debugMode: false,
  userMemory: '',
  arienMdFileCount: 50,
  approvalMode: ApprovalMode.DEFAULT,
  sessionId: 'test-session',
  fileFilteringRespectGitIgnore: true,
  fullContext: false,
};

describe('GitDiffTool', () => {
  let gitDiffTool: GitDiffTool;
  let mockConfig: Config;
  let mockProcess: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockConfig = new Config(mockConfigParams);
    gitDiffTool = new GitDiffTool(mockConfig);

    // Mock process object
    mockProcess = {
      stdout: {
        on: vi.fn(),
      },
      stderr: {
        on: vi.fn(),
      },
      on: vi.fn(),
    };

    mockSpawn.mockReturnValue(mockProcess as any);
    mockFs.existsSync.mockReturnValue(true);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('constructor', () => {
    it('should create GitDiffTool with correct properties', () => {
      expect(gitDiffTool.name).toBe('git_diff');
      expect(gitDiffTool.displayName).toBe('Git Diff');
      expect(gitDiffTool.description).toContain('Shows differences between commits');
      expect(gitDiffTool.isOutputMarkdown).toBe(true);
      expect(gitDiffTool.canUpdateOutput).toBe(false);
    });

    it('should have correct schema structure', () => {
      const schema = gitDiffTool.schema;
      expect(schema.name).toBe('git_diff');
      expect(schema.parameters).toBeDefined();
      expect(schema.parameters.properties).toHaveProperty('directory');
      expect(schema.parameters.properties).toHaveProperty('file_path');
      expect(schema.parameters.properties).toHaveProperty('staged');
      expect(schema.parameters.properties).toHaveProperty('commit_range');
    });
  });

  describe('validateToolParams', () => {
    it('should validate correct parameters', () => {
      const params: GitDiffParams = {
        directory: 'src',
        file_path: 'test.ts',
        staged: true,
        context_lines: 5,
      };

      const result = gitDiffTool.validateToolParams(params);
      expect(result).toBeNull();
    });

    it('should reject absolute directory paths', () => {
      const params: GitDiffParams = {
        directory: '/absolute/path',
      };

      const result = gitDiffTool.validateToolParams(params);
      expect(result).toContain('Directory cannot be absolute');
    });

    it('should validate context lines range', () => {
      const params: GitDiffParams = {
        context_lines: 100,
      };

      const result = gitDiffTool.validateToolParams(params);
      expect(result).toContain('context_lines must be at most 50');
    });

    it('should accept empty parameters', () => {
      const params: GitDiffParams = {};
      const result = gitDiffTool.validateToolParams(params);
      expect(result).toBeNull();
    });
  });

  describe('execute', () => {
    it('should handle no differences', async () => {
      const params: GitDiffParams = {};
      const abortSignal = new AbortController().signal;

      // Mock git commands
      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 0);
        }
      });

      let callCount = 0;
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callCount++;
          if (callCount === 1) {
            // rev-parse output
            setTimeout(() => callback(''), 0);
          } else {
            // empty diff output
            setTimeout(() => callback(''), 0);
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback(''), 0);
        }
      });

      const result = await gitDiffTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('No differences found');
      expect(result.llmContent).toContain('identical');
      expect(result.returnDisplay).toContain('Git diff');
    });

    it('should handle file differences', async () => {
      const params: GitDiffParams = { file_path: 'test.ts' };
      const abortSignal = new AbortController().signal;

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 0);
        }
      });

      let callCount = 0;
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callCount++;
          if (callCount === 1) {
            setTimeout(() => callback(''), 0);
          } else {
            const diffOutput = `diff --git a/test.ts b/test.ts
index 1234567..abcdefg 100644
--- a/test.ts
+++ b/test.ts
@@ -1,3 +1,4 @@
 function test() {
+  console.log('new line');
   return true;
 }`;
            setTimeout(() => callback(diffOutput), 0);
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback(''), 0);
        }
      });

      const result = await gitDiffTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('# Git Diff');
      expect(result.llmContent).toContain('test.ts');
      expect(result.llmContent).toContain('console.log');
      expect(result.returnDisplay).toContain('Git diff for test.ts');
    });

    it('should handle staged diff', async () => {
      const params: GitDiffParams = { staged: true };
      const abortSignal = new AbortController().signal;

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 0);
        }
      });

      let callCount = 0;
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callCount++;
          if (callCount === 1) {
            setTimeout(() => callback(''), 0);
          } else {
            setTimeout(() => callback('diff --git a/staged.ts b/staged.ts\n+new staged content'), 0);
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback(''), 0);
        }
      });

      const result = await gitDiffTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('Staged changes vs last commit');
      expect(result.returnDisplay).toContain('(staged)');
    });

    it('should handle commit range diff', async () => {
      const params: GitDiffParams = { commit_range: 'HEAD~1..HEAD' };
      const abortSignal = new AbortController().signal;

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 0);
        }
      });

      let callCount = 0;
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callCount++;
          if (callCount === 1) {
            setTimeout(() => callback(''), 0);
          } else {
            setTimeout(() => callback('diff --git a/changed.ts b/changed.ts\n+commit range change'), 0);
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback(''), 0);
        }
      });

      const result = await gitDiffTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('HEAD~1..HEAD');
      expect(result.returnDisplay).toContain('(HEAD~1..HEAD)');
    });

    it('should handle stat only output', async () => {
      const params: GitDiffParams = { stat_only: true };
      const abortSignal = new AbortController().signal;

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 0);
        }
      });

      let callCount = 0;
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callCount++;
          if (callCount === 1) {
            setTimeout(() => callback(''), 0);
          } else {
            const statOutput = ` file1.ts | 5 +++++
 file2.ts | 3 ---
 2 files changed, 5 insertions(+), 3 deletions(-)`;
            setTimeout(() => callback(statOutput), 0);
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback(''), 0);
        }
      });

      const result = await gitDiffTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('Git Diff Statistics');
      expect(result.llmContent).toContain('file1.ts');
      expect(result.llmContent).toContain('file2.ts');
      expect(result.llmContent).toContain('2 files changed');
    });

    it('should handle not a git repository error', async () => {
      const params: GitDiffParams = {};
      const abortSignal = new AbortController().signal;

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(1), 0); // Exit code 1
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback('fatal: not a git repository'), 0);
        }
      });

      const result = await gitDiffTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('Not a git repository');
      expect(result.returnDisplay).toContain('Not a git repository');
    });

    it('should handle git diff command failure', async () => {
      const params: GitDiffParams = {};
      const abortSignal = new AbortController().signal;

      let callCount = 0;
      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          callCount++;
          if (callCount === 1) {
            setTimeout(() => callback(0), 0); // rev-parse succeeds
          } else {
            setTimeout(() => callback(1), 0); // diff fails
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback('git diff error'), 0);
        }
      });

      const result = await gitDiffTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('Git diff failed');
      expect(result.returnDisplay).toContain('Git diff error');
    });

    it('should handle cancelled operation', async () => {
      const params: GitDiffParams = {};
      const abortController = new AbortController();
      abortController.abort();

      const result = await gitDiffTool.execute(params, abortController.signal);

      expect(result.llmContent).toContain('cancelled by user');
      expect(result.returnDisplay).toContain('cancelled');
    });
  });
});
