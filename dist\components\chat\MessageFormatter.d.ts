/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import React from 'react';
import { ThemeManager } from '../../themes/themes.js';
interface MessageFormatterProps {
    themeManager: ThemeManager;
    content: string;
    enableMarkdown?: boolean;
    enableCodeHighlighting?: boolean;
    enableLinkDetection?: boolean;
    maxWidth?: number;
}
export declare const MessageFormatter: React.FC<MessageFormatterProps>;
export declare const CodeFormatter: React.FC<Omit<MessageFormatterProps, 'enableMarkdown' | 'enableCodeHighlighting'>>;
export declare const PlainTextFormatter: React.FC<Omit<MessageFormatterProps, 'enableMarkdown' | 'enableCodeHighlighting' | 'enableLinkDetection'>>;
export declare const MarkdownFormatter: React.FC<Omit<MessageFormatterProps, 'enableMarkdown'>>;
export {};
//# sourceMappingURL=MessageFormatter.d.ts.map