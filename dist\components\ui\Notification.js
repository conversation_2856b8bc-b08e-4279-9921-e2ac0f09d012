import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
export const Notification = ({ notification, themeManager, onDismiss, }) => {
    const [isVisible, setIsVisible] = useState(true);
    const [timeLeft, setTimeLeft] = useState(notification.duration || 0);
    useEffect(() => {
        if (notification.duration && notification.duration > 0) {
            const interval = setInterval(() => {
                setTimeLeft(prev => {
                    if (prev <= 1000) {
                        setIsVisible(false);
                        setTimeout(() => onDismiss(notification.id), 300);
                        return 0;
                    }
                    return prev - 1000;
                });
            }, 1000);
            return () => clearInterval(interval);
        }
        return undefined;
    }, [notification.duration, notification.id, onDismiss]);
    useInput((input, key) => {
        if (key.escape && notification.dismissible !== false) {
            onDismiss(notification.id);
        }
        // Handle action keys
        if (notification.actions) {
            const action = notification.actions.find(a => a.key === input);
            if (action) {
                action.handler();
                onDismiss(notification.id);
            }
        }
    });
    if (!isVisible)
        return null;
    const getTypeColor = () => {
        switch (notification.type) {
            case 'success':
                return themeManager.success;
            case 'error':
                return themeManager.error;
            case 'warning':
                return themeManager.warning;
            case 'info':
                return themeManager.info;
            default:
                return themeManager.primary;
        }
    };
    const getTypeIcon = () => {
        const theme = themeManager.getCurrentTheme();
        switch (notification.type) {
            case 'success':
                return theme.symbols.check;
            case 'error':
                return theme.symbols.cross;
            case 'warning':
                return theme.symbols.warning;
            case 'info':
                return theme.symbols.info;
            default:
                return theme.symbols.bullet;
        }
    };
    const getBorderColor = () => {
        switch (notification.type) {
            case 'success':
                return 'green';
            case 'error':
                return 'red';
            case 'warning':
                return 'yellow';
            case 'info':
                return 'blue';
            default:
                return 'gray';
        }
    };
    const renderHeader = () => (_jsxs(Box, { justifyContent: "space-between", children: [_jsxs(Box, { children: [_jsxs(Text, { children: [getTypeColor()(getTypeIcon()), " "] }), _jsx(Text, { children: getTypeColor()(notification.title) })] }), notification.dismissible !== false && (_jsx(Text, { children: themeManager.muted('[ESC to dismiss]') }))] }));
    const renderMessage = () => {
        if (!notification.message)
            return null;
        return (_jsx(Box, { marginTop: 1, children: _jsx(Text, { children: themeManager.primary(notification.message) }) }));
    };
    const renderActions = () => {
        if (!notification.actions || notification.actions.length === 0)
            return null;
        return (_jsx(Box, { marginTop: 1, gap: 2, children: notification.actions.map((action, index) => (_jsxs(Text, { children: [themeManager.accent(`[${action.key}]`), " ", themeManager.secondary(action.label)] }, index))) }));
    };
    const renderTimer = () => {
        if (!notification.duration || notification.duration === 0 || timeLeft === 0)
            return null;
        const seconds = Math.ceil(timeLeft / 1000);
        return (_jsx(Box, { marginTop: 1, children: _jsx(Text, { children: themeManager.muted(`Auto-dismiss in ${seconds}s`) }) }));
    };
    return (_jsxs(Box, { borderStyle: "round", borderColor: getBorderColor(), padding: 1, flexDirection: "column", width: "100%", children: [renderHeader(), renderMessage(), renderActions(), renderTimer()] }));
};
export const NotificationManager = ({ themeManager, notifications, onDismiss, maxVisible = 3, position = 'top', }) => {
    const visibleNotifications = notifications.slice(0, maxVisible);
    if (visibleNotifications.length === 0)
        return null;
    return (_jsxs(Box, { flexDirection: "column", gap: 1, children: [visibleNotifications.map((notification) => (_jsx(Notification, { notification: notification, themeManager: themeManager, onDismiss: onDismiss, position: position }, notification.id))), notifications.length > maxVisible && (_jsx(Box, { justifyContent: "center", children: _jsx(Text, { children: themeManager.muted(`+${notifications.length - maxVisible} more notifications`) }) }))] }));
};
//# sourceMappingURL=Notification.js.map