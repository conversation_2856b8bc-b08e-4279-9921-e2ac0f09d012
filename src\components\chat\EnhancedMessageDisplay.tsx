/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React from 'react';
import { Box } from 'ink';
import { ThemeManager } from '../../themes/themes.js';
import { ChatMessage } from '../../hooks/useChat.js';
import { TextBuffer, TextLine } from '../ui/TextBuffer.js';

interface EnhancedMessageDisplayProps {
  themeManager: ThemeManager;
  messages: ChatMessage[];
  maxHeight?: number;
  maxWidth?: number;
  showTimestamps?: boolean;
  showLineNumbers?: boolean;
  enableSearch?: boolean;
  scrollable?: boolean;
  title?: string;
}

export const EnhancedMessageDisplay: React.FC<EnhancedMessageDisplayProps> = ({
  themeManager,
  messages,
  maxHeight = 20,
  maxWidth = 80,
  showTimestamps = true,
  showLineNumbers = false,
  enableSearch = true,
  scrollable = true,
  title = "Chat Messages",
}) => {
  // Convert messages to TextBuffer format
  const convertMessagesToTextLines = (): TextLine[] => {
    const lines: TextLine[] = [];
    
    messages.forEach((message, messageIndex) => {
      // Add sender line
      lines.push({
        id: `message-${messageIndex}-sender`,
        content: `${message.role === 'user' ? 'You' : 'Assistant'}:`,
        timestamp: message.timestamp,
        style: 'bold',
        color: message.role === 'user' ? 'accent' : 'secondary',
        metadata: { messageId: message.id, type: 'sender' }
      });

      // Split message content into lines
      const contentLines = message.content.split('\n');
      contentLines.forEach((line: string, lineIndex: number) => {
        lines.push({
          id: `message-${messageIndex}-content-${lineIndex}`,
          content: line || ' ', // Empty lines should show as space
          timestamp: message.timestamp,
          style: 'normal',
          color: getContentColor(line),
          metadata: { 
            messageId: message.id, 
            type: 'content',
            lineIndex 
          }
        });
      });

      // Add separator line between messages (except for last message)
      if (messageIndex < messages.length - 1) {
        lines.push({
          id: `separator-${messageIndex}`,
          content: '─'.repeat(Math.min(maxWidth - 10, 50)),
          timestamp: message.timestamp,
          style: 'normal',
          color: 'muted',
          metadata: { type: 'separator' }
        });
      }
    });

    return lines;
  };

  const getContentColor = (line: string): TextLine['color'] => {
    // Detect different types of content and apply appropriate colors
    if (line.startsWith('```') || line.match(/^\s*```/)) return 'accent';
    if (line.startsWith('# ')) return 'primary';
    if (line.startsWith('## ')) return 'secondary';
    if (line.startsWith('- ') || line.startsWith('* ')) return 'info';
    if (line.includes('ERROR') || line.includes('error')) return 'error';
    if (line.includes('WARNING') || line.includes('warning')) return 'warning';
    if (line.includes('SUCCESS') || line.includes('success')) return 'success';
    if (line.match(/https?:\/\/[^\s]+/)) return 'info'; // URLs
    if (line.match(/`[^`]+`/)) return 'accent'; // Inline code
    return undefined; // Default color
  };

  const textLines = convertMessagesToTextLines();

  const handleSelectionChange = (selectedLines: TextLine[]) => {
    // Could implement copy functionality or other selection-based features
    console.log('Selected lines:', selectedLines.map(l => l.content));
  };

  const handleScroll = (_scrollPosition: { top: number; bottom: number }) => {
    // Could implement scroll-based features like loading more messages
    // console.log('Scroll position:', scrollPosition);
  };

  if (messages.length === 0) {
    return (
      <TextBuffer
        themeManager={themeManager}
        lines={[]}
        maxHeight={maxHeight}
        maxWidth={maxWidth}
        title={title}
        emptyMessage="No messages yet. Start a conversation!"
        scrollable={false}
        showTimestamps={false}
        showLineNumbers={false}
      />
    );
  }

  return (
    <Box flexDirection="column">
      <TextBuffer
        themeManager={themeManager}
        lines={textLines}
        maxHeight={maxHeight}
        maxWidth={maxWidth}
        showTimestamps={showTimestamps}
        showLineNumbers={showLineNumbers}
        enableSearch={enableSearch}
        enableSelection={true}
        scrollable={scrollable}
        autoScroll={true}
        scrollToBottom={true}
        title={title}
        onSelectionChange={handleSelectionChange}
        onScroll={handleScroll}
        variant="detailed"
        borderStyle="round"
        borderColor="gray"
        padding={1}
        emptyMessage="No messages to display"
        loadingMessage="Loading messages..."
      />
    </Box>
  );
};

// Convenience components for different use cases
export const CompactMessageDisplay: React.FC<Omit<EnhancedMessageDisplayProps, 'showTimestamps' | 'showLineNumbers'>> = (props) => (
  <EnhancedMessageDisplay 
    {...props} 
    showTimestamps={false} 
    showLineNumbers={false}
  />
);

export const DetailedMessageDisplay: React.FC<Omit<EnhancedMessageDisplayProps, 'showTimestamps' | 'showLineNumbers' | 'enableSearch'>> = (props) => (
  <EnhancedMessageDisplay 
    {...props} 
    showTimestamps={true} 
    showLineNumbers={true}
    enableSearch={true}
  />
);

export const LogStyleMessageDisplay: React.FC<Omit<EnhancedMessageDisplayProps, 'showTimestamps' | 'title'>> = (props) => (
  <EnhancedMessageDisplay 
    {...props} 
    showTimestamps={true}
    title="Chat Log"
  />
);
