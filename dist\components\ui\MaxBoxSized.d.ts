/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import React from 'react';
import { ThemeManager } from '../../themes/themes.js';
export interface MaxBoxSizedProps {
    children: React.ReactNode;
    themeManager?: ThemeManager;
    maxWidth?: number | string;
    maxHeight?: number | string;
    minWidth?: number | string;
    minHeight?: number | string;
    width?: number | string;
    height?: number | string;
    overflowX?: 'visible' | 'hidden';
    overflowY?: 'visible' | 'hidden';
    overflow?: 'visible' | 'hidden';
    responsive?: boolean;
    responsiveBreakpoints?: {
        small?: number;
        medium?: number;
        large?: number;
    };
    autoResize?: boolean;
    maintainAspectRatio?: boolean;
    aspectRatio?: number;
    padding?: number;
    margin?: number;
    marginX?: number;
    marginY?: number;
    marginTop?: number;
    marginBottom?: number;
    marginLeft?: number;
    marginRight?: number;
    paddingX?: number;
    paddingY?: number;
    paddingTop?: number;
    paddingBottom?: number;
    paddingLeft?: number;
    paddingRight?: number;
    borderStyle?: 'single' | 'double' | 'round' | 'bold' | 'singleDouble' | 'doubleSingle' | 'classic';
    borderColor?: string;
    flexDirection?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
    flexWrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
    flexGrow?: number;
    flexShrink?: number;
    flexBasis?: number | string;
    alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
    alignSelf?: 'auto' | 'flex-start' | 'center' | 'flex-end';
    justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
    position?: 'relative' | 'absolute';
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
    onResize?: (dimensions: {
        width: number;
        height: number;
    }) => void;
    onOverflow?: (overflow: {
        x: boolean;
        y: boolean;
    }) => void;
    debug?: boolean;
}
export declare const MaxBoxSized: React.FC<MaxBoxSizedProps>;
export declare const ResponsiveMaxBox: React.FC<Omit<MaxBoxSizedProps, 'responsive'>>;
export declare const FixedMaxBox: React.FC<Omit<MaxBoxSizedProps, 'responsive' | 'autoResize'>>;
export declare const AspectRatioBox: React.FC<Omit<MaxBoxSizedProps, 'maintainAspectRatio'> & {
    aspectRatio: number;
}>;
//# sourceMappingURL=MaxBoxSized.d.ts.map