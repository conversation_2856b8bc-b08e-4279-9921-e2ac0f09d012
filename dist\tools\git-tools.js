/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { GitStatusTool } from './git-status.js';
import { GitDiffTool } from './git-diff.js';
import { GitBranchTool } from './git-branch.js';
import { GitCommitTool } from './git-commit.js';
import { GitLogTool } from './git-log.js';
/**
 * Git Tools Collection
 *
 * Comprehensive git integration tools for repository management:
 * - git_status: Show repository status and file changes
 * - git_diff: Display differences between commits, branches, or working tree
 * - git_branch: Manage branches (list, create, delete, switch, merge)
 * - git_commit: Create commits with validation and best practices
 * - git_log: Explore commit history with filtering and formatting options
 */
/**
 * Creates and returns all git tools instances
 */
export function createGitTools(config) {
    return {
        gitStatus: new GitStatusTool(config),
        gitDiff: new GitDiffTool(config),
        gitBranch: new GitBranchTool(config),
        gitCommit: new GitCommitTool(config),
        gitLog: new GitLogTool(config),
    };
}
/**
 * Registers all git tools with the provided tool registry
 */
export function registerGitTools(toolRegistry, config) {
    const gitTools = createGitTools(config);
    // Register each git tool
    toolRegistry.registerTool(gitTools.gitStatus);
    toolRegistry.registerTool(gitTools.gitDiff);
    toolRegistry.registerTool(gitTools.gitBranch);
    toolRegistry.registerTool(gitTools.gitCommit);
    toolRegistry.registerTool(gitTools.gitLog);
    console.log('✅ Registered 5 git tools: status, diff, branch, commit, log');
}
/**
 * Git tool names for easy reference
 */
export const GIT_TOOL_NAMES = {
    STATUS: 'git_status',
    DIFF: 'git_diff',
    BRANCH: 'git_branch',
    COMMIT: 'git_commit',
    LOG: 'git_log',
};
/**
 * Export individual tool classes for direct usage
 */
export { GitStatusTool, GitDiffTool, GitBranchTool, GitCommitTool, GitLogTool, };
//# sourceMappingURL=git-tools.js.map