{"version": 3, "file": "git-status.js", "sourceRoot": "", "sources": ["../../src/tools/git-status.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,IAAI,MAAM,MAAM,CAAC;AAExB,OAAO,EAAE,QAAQ,EAAc,MAAM,YAAY,CAAC;AAClD,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAC9D,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAoBtC,MAAM,OAAO,aAAc,SAAQ,QAAqC;IAGzC;IAF7B,MAAM,CAAC,IAAI,GAAW,YAAY,CAAC;IAEnC,YAA6B,MAAc;QACzC,KAAK,CACH,aAAa,CAAC,IAAI,EAClB,YAAY,EACZ;;;;;;;0GAOoG,EACpG;YACE,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,SAAS,EAAE;oBACT,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,wHAAwH;iBACtI;gBACD,SAAS,EAAE;oBACT,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,2GAA2G;iBACzH;gBACD,YAAY,EAAE;oBACZ,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,8FAA8F;iBAC5G;aACF;YACD,QAAQ,EAAE,EAAE;SACb,EACD,IAAI,EAAE,qBAAqB;QAC3B,KAAK,CACN,CAAC;QAhCyB,WAAM,GAAN,MAAM,CAAQ;IAiC3C,CAAC;IAED,kBAAkB,CAAC,MAAuB;QACxC,MAAM,UAAU,GAAG,eAAe,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAC1E,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YACxB,OAAO,wCAAwC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAChF,CAAC;QAED,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;gBACtC,OAAO,+EAA+E,CAAC;YACzF,CAAC;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;YAC7E,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,OAAO,uBAAuB,CAAC;YACjC,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,IAAc,EAAE,GAAW;QACzD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE;gBACjC,GAAG;gBACH,KAAK,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;aAClC,CAAC,CAAC;YAEH,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,MAAM,GAAG,EAAE,CAAC;YAEhB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAClC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5B,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAClC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5B,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,EAAE;gBAC/B,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,IAAI,CAAC,EAAE,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,MAAc;QACnC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,GAAkB;YAC5B,MAAM,EAAE,SAAS;YACjB,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE,EAAE;YACb,KAAK,EAAE,IAAI;SACZ,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC3B,qBAAqB;gBACrB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACtC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAEzB,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;oBACb,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;oBAC9B,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;oBACrD,MAAM,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;oBACvD,IAAI,UAAU;wBAAE,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvD,IAAI,WAAW;wBAAE,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5D,CAAC;YACH,CAAC;iBAAM,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBAC5B,MAAM,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC7B,MAAM,cAAc,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAEnC,IAAI,YAAY,KAAK,GAAG,IAAI,YAAY,KAAK,GAAG,EAAE,CAAC;oBACjD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;oBAC7D,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;gBACvB,CAAC;gBAED,IAAI,cAAc,KAAK,GAAG,IAAI,cAAc,KAAK,GAAG,EAAE,CAAC;oBACrD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;oBACjE,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;gBACvB,CAAC;gBAED,IAAI,YAAY,KAAK,GAAG,IAAI,cAAc,KAAK,GAAG,EAAE,CAAC;oBACnD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAChC,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;gBACvB,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,kBAAkB,CAAC,MAAqB,EAAE,SAAkB;QAClE,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,MAAM,GAAG,kBAAkB,CAAC;QAEhC,qBAAqB;QACrB,MAAM,IAAI,eAAe,MAAM,CAAC,MAAM,EAAE,CAAC;QACzC,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClC,MAAM,QAAQ,GAAG,EAAE,CAAC;YACpB,IAAI,MAAM,CAAC,KAAK;gBAAE,QAAQ,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YACzD,IAAI,MAAM,CAAC,MAAM;gBAAE,QAAQ,CAAC,IAAI,CAAC,UAAU,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YAC5D,MAAM,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QACxC,CAAC;QACD,MAAM,IAAI,MAAM,CAAC;QAEjB,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,MAAM,IAAI,mDAAmD,CAAC;YAC9D,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,iBAAiB;QACjB,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,4CAA4C,CAAC;YACvD,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBACjC,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACnD,MAAM,IAAI,KAAK,UAAU,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC;YACjD,CAAC;YACD,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC;QAED,mBAAmB;QACnB,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,kDAAkD,CAAC;YAC7D,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACnC,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACnD,MAAM,IAAI,KAAK,UAAU,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC;YACjD,CAAC;YACD,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC;QAED,kBAAkB;QAClB,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,0BAA0B,CAAC;YACrC,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;gBACpC,MAAM,IAAI,UAAU,IAAI,MAAM,CAAC;YACjC,CAAC;YACD,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC;QAED,gBAAgB;QAChB,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChD,MAAM,IAAI,yBAAyB,CAAC;YACpC,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBAClC,MAAM,IAAI,UAAU,IAAI,MAAM,CAAC;YACjC,CAAC;YACD,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,aAAa,CAAC,MAAc;QAClC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,GAAG,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,QAAQ;YAC9B,KAAK,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,WAAW;YAClC,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,UAAU;YAClC,KAAK,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,UAAU;YACjC,KAAK,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,SAAS;YAChC,KAAK,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,WAAW;YAClC,KAAK,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,eAAe;YACtC,OAAO,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,UAAU;QACjC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,MAAuB,EAAE,WAAwB;QAC7D,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO;gBACL,UAAU,EAAE,UAAU,eAAe,EAAE;gBACvC,aAAa,EAAE,qBAAqB,eAAe,EAAE;aACtD,CAAC;QACJ,CAAC;QAED,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YACxB,OAAO;gBACL,UAAU,EAAE,2CAA2C;gBACvD,aAAa,EAAE,uBAAuB;aACvC,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS;gBACjC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,MAAM,CAAC,SAAS,CAAC;gBAC5D,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAE/B,yCAAyC;YACzC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE,UAAU,CAAC,CAAC;YACzF,IAAI,WAAW,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBAC/B,OAAO;oBACL,UAAU,EAAE,gEAAgE;oBAC5E,aAAa,EAAE,6BAA6B;iBAC7C,CAAC;YACJ,CAAC;YAED,iBAAiB;YACjB,MAAM,IAAI,GAAG,CAAC,QAAQ,EAAE,gBAAgB,EAAE,UAAU,CAAC,CAAC;YACtD,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzB,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAE9D,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBAC1B,OAAO;oBACL,UAAU,EAAE,sBAAsB,MAAM,CAAC,MAAM,EAAE;oBACjD,aAAa,EAAE,qBAAqB,MAAM,CAAC,MAAM,EAAE;iBACpD,CAAC;YACJ,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACtD,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,MAAM,CAAC,SAAS,IAAI,KAAK,CAAC,CAAC;YAEvF,OAAO;gBACL,UAAU,EAAE,eAAe;gBAC3B,aAAa,EAAE,kBAAkB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,kBAAkB,EAAE;aAClH,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,+BAA+B,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7E,OAAO;gBACL,UAAU,EAAE,YAAY;gBACxB,aAAa,EAAE,YAAY;aAC5B,CAAC;QACJ,CAAC;IACH,CAAC"}