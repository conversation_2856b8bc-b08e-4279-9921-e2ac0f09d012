{"version": 3, "file": "TerminalInputManager.js", "sourceRoot": "", "sources": ["../../src/terminal/TerminalInputManager.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAyCtC;;GAEG;AACH,MAAM,OAAO,oBAAqB,SAAQ,YAAY;IAC5C,KAAK,CAAqB;IAC1B,OAAO,CAAuB;IAC9B,qBAAqB,GAA0B,IAAI,CAAC;IAE5D,YAAY,UAAgC,EAAE;QAC5C,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG;YACb,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,GAAG;YACb,SAAS,EAAE,SAAS;YACpB,SAAS,EAAE,SAAS;YACpB,eAAe,EAAE,IAAI,CAAC,sBAAsB;YAC5C,cAAc,EAAE,SAAS;YACzB,kBAAkB,EAAE,IAAI;YACxB,GAAG,OAAO;SACX,CAAC;QAEF,IAAI,CAAC,KAAK,GAAG;YACX,KAAK,EAAE,EAAE;YACT,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,IAAI;YACb,iBAAiB,EAAE,EAAE;YACrB,eAAe,EAAE,KAAK;YACtB,cAAc,EAAE,CAAC;YACjB,UAAU,EAAE,KAAK;SAClB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,sBAAsB,GAAG,CAAC,IAAY,EAAW,EAAE;QACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACpC,OAAO,QAAQ,IAAI,EAAE,IAAI,QAAQ,IAAI,GAAG,CAAC;IAC3C,CAAC,CAAC;IAEF;;OAEG;IACH,YAAY,CAAC,SAAiB,EAAE,GAAQ;QACtC,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YAC1B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,KAAK,GAAuB;YAChC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,GAAG,CAAC;YAC7C,IAAI,EAAE,SAAS;YACf,GAAG;YACH,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAE1B,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YACrC,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YACzC,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YACpC;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,SAAiB,EAAE,GAAQ;QACpD,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM;YAC/E,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC;YACpE,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtC,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxC,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,UAAkB;QACpC,oCAAoC;QACpC,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5E,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,mBAAmB,EAAE,CAAC,CAAC;YAC9E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,4BAA4B;QAC5B,MAAM,eAAe,GAAG,UAAU;aAC/B,KAAK,CAAC,EAAE,CAAC;aACT,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,IAAI,CAAC,sBAAsB,CAAC;aACnE,IAAI,CAAC,EAAE,CAAC,CAAC;QAEZ,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,qBAAqB,EAAE,CAAC,CAAC;YAChF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,yBAAyB;QACzB,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS;YAC3C,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM;YAClD,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC;QAE3B,MAAM,cAAc,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;QAEhE,8BAA8B;QAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,cAAc,CAAC;QACnD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,mBAAmB,EAAE,CAAC,CAAC;YAC9E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,eAAe;QACf,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC3B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAE5B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,YAAY,EAAE,UAAU;YACxB,YAAY,EAAE,cAAc;YAC5B,QAAQ,EAAE,QAAQ;SACnB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,IAAY;QAClC,yBAAyB;QACzB,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;YACxE,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,mBAAmB;QACnB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAChF,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,qBAAqB,EAAE,CAAC,CAAC;YACxE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,8BAA8B;QAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;QACzC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChE,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,mBAAmB,EAAE,CAAC,CAAC;YACtE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,eAAe;QACf,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC3B,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE7B,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;QAEnD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,GAAQ;QAC/B,IAAI,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/C,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC3B,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,4BAA4B;QAC5B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;QACjC,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,QAAgB;QAClC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC;QAC5B,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM;YAC3C,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YACxD,CAAC,CAAC,QAAQ,CAAC;QAEb,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC;QAE5C,0CAA0C;QAC1C,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;YACpC,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;SACtC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAiB;QACxC,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,QAAQ,CAAC;QAEtC,sCAAsC;QACtC,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC/B,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC3C,CAAC;YACD,IAAI,CAAC,qBAAqB,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC3C,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC;gBACnC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACrC,CAAC,EAAE,IAAI,CAAC,CAAC;QACX,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,eAAe,EAAE,QAAQ,EAAE,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS;YACpC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;YAC1C,CAAC,CAAC,IAAI,CAAC;QAET,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,OAAO;YACP,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;SACxB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QACrB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,KAAa;QACpB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,QAAiB;QAC3B,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,UAAyC;QACrD,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,UAAU,EAAE,CAAC;QAElD,iDAAiD;QACjD,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;YACpC,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC/B,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC3C,CAAC;QACD,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;CACF"}