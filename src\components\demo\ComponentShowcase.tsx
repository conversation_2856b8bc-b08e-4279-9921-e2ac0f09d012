/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { ThemeManager } from '../../themes/themes.js';
import { RadioButtons, RadioOption } from '../ui/RadioButtons.js';
import { MaxBoxSized } from '../ui/MaxBoxSized.js';
import { TextBuffer, TextLine } from '../ui/TextBuffer.js';

interface ComponentShowcaseProps {
  themeManager: ThemeManager;
  onExit: () => void;
}

export const ComponentShowcase: React.FC<ComponentShowcaseProps> = ({
  themeManager,
  onExit,
}) => {
  const [currentDemo, setCurrentDemo] = useState<string>('radio');
  const [selectedOption, setSelectedOption] = useState<string>('');
  const [textContent, setTextContent] = useState<string>('');

  // Demo options for RadioButtons
  const demoOptions: RadioOption[] = [
    { value: 'radio', label: 'Radio Buttons Demo', description: 'Interactive selection component' },
    { value: 'maxbox', label: 'Max Box Sized Demo', description: 'Responsive container with size constraints' },
    { value: 'textbuffer', label: 'Text Buffer Demo', description: 'Advanced text display with scrolling' },
    { value: 'combined', label: 'Combined Demo', description: 'All components working together' },
  ];

  // Sample options for radio button demo
  const sampleOptions: RadioOption[] = [
    { value: 'option1', label: 'First Option', description: 'This is the first choice' },
    { value: 'option2', label: 'Second Option', description: 'This is the second choice' },
    { value: 'option3', label: 'Third Option', description: 'This is the third choice' },
    { value: 'disabled', label: 'Disabled Option', description: 'This option is disabled', disabled: true },
  ];

  // Sample text lines for TextBuffer demo
  const sampleTextLines: TextLine[] = [
    { id: '1', content: '# Welcome to the Text Buffer Demo', color: 'primary', style: 'bold' },
    { id: '2', content: 'This component can display formatted text with various features:', color: 'secondary' },
    { id: '3', content: '• Syntax highlighting and color coding', color: 'info' },
    { id: '4', content: '• Line numbers and timestamps', color: 'info' },
    { id: '5', content: '• Scrolling for large content', color: 'info' },
    { id: '6', content: '• Search functionality', color: 'info' },
    { id: '7', content: '', color: 'muted' },
    { id: '8', content: '## Code Example:', color: 'accent', style: 'bold' },
    { id: '9', content: 'const example = "Hello, World!";', color: 'accent' },
    { id: '10', content: 'console.log(example);', color: 'accent' },
    { id: '11', content: '', color: 'muted' },
    { id: '12', content: 'SUCCESS: Component loaded successfully', color: 'success' },
    { id: '13', content: 'WARNING: This is a demo environment', color: 'warning' },
    { id: '14', content: 'ERROR: This is just a sample error message', color: 'error' },
    { id: '15', content: '', color: 'muted' },
    { id: '16', content: 'Use arrow keys to scroll, / to search, ESC to exit search', color: 'muted' },
  ];

  // Handle keyboard input
  useInput((input, key) => {
    if (key.escape || input === 'q' || input === 'Q') {
      onExit();
    }
  });

  const handleDemoSelection = (value: string, _option: RadioOption) => {
    setCurrentDemo(value);
  };

  const handleOptionSelection = (value: string, option: RadioOption) => {
    setSelectedOption(value);
    setTextContent(`Selected: ${option.label} (${value})\nDescription: ${option.description}`);
  };

  const renderRadioButtonsDemo = () => (
    <MaxBoxSized
      themeManager={themeManager}
      maxWidth={60}
      maxHeight={15}
      borderStyle="round"
      borderColor="blue"
      padding={1}
      responsive={true}
    >
      <Box flexDirection="column">
        <Text>{themeManager.primary('Radio Buttons Component Demo')}</Text>
        <Text></Text>
        <RadioButtons
          themeManager={themeManager}
          options={sampleOptions}
          selectedValue={selectedOption}
          onSelectionChange={handleOptionSelection}
          title="Choose an option:"
          showDescriptions={true}
          variant="detailed"
          helpText="Use ↑↓ to navigate, Enter to select, Space to toggle"
        />
        {selectedOption && (
          <Box marginTop={1}>
            <Text>{themeManager.success(`✓ Selected: ${selectedOption}`)}</Text>
          </Box>
        )}
      </Box>
    </MaxBoxSized>
  );

  const renderMaxBoxSizedDemo = () => (
    <Box flexDirection="column">
      <Text>{themeManager.primary('Max Box Sized Component Demo')}</Text>
      <Text></Text>
      <MaxBoxSized
        themeManager={themeManager}
        maxWidth={50}
        maxHeight={10}
        borderStyle="double"
        borderColor="green"
        padding={2}
        responsive={true}
        debug={true}
      >
        <Box flexDirection="column">
          <Text>{themeManager.secondary('Responsive Container')}</Text>
          <Text></Text>
          <Text>This container automatically adjusts to terminal size</Text>
          <Text>while respecting maximum width and height constraints.</Text>
          <Text></Text>
          <Text>{themeManager.muted('Resize your terminal to see the effect!')}</Text>
        </Box>
      </MaxBoxSized>
    </Box>
  );

  const renderTextBufferDemo = () => (
    <Box flexDirection="column">
      <Text>{themeManager.primary('Text Buffer Component Demo')}</Text>
      <Text></Text>
      <TextBuffer
        themeManager={themeManager}
        lines={sampleTextLines}
        maxHeight={12}
        maxWidth={70}
        showTimestamps={false}
        showLineNumbers={true}
        enableSearch={true}
        scrollable={true}
        title="Sample Text Content"
        borderStyle="single"
        borderColor="yellow"
        padding={1}
        variant="detailed"
      />
    </Box>
  );

  const renderCombinedDemo = () => (
    <Box flexDirection="row" gap={2}>
      <MaxBoxSized
        themeManager={themeManager}
        maxWidth={30}
        maxHeight={20}
        borderStyle="round"
        borderColor="cyan"
        padding={1}
      >
        <RadioButtons
          themeManager={themeManager}
          options={sampleOptions}
          selectedValue={selectedOption}
          onSelectionChange={handleOptionSelection}
          title="Options:"
          variant="compact"
          showDescriptions={false}
        />
      </MaxBoxSized>
      
      <MaxBoxSized
        themeManager={themeManager}
        maxWidth={50}
        maxHeight={20}
        borderStyle="round"
        borderColor="magenta"
        padding={1}
      >
        <TextBuffer
          themeManager={themeManager}
          content={textContent || 'Select an option to see details here...'}
          maxHeight={18}
          scrollable={true}
          showTimestamps={false}
          title="Selection Details"
          variant="compact"
        />
      </MaxBoxSized>
    </Box>
  );

  const renderCurrentDemo = () => {
    switch (currentDemo) {
      case 'radio':
        return renderRadioButtonsDemo();
      case 'maxbox':
        return renderMaxBoxSizedDemo();
      case 'textbuffer':
        return renderTextBufferDemo();
      case 'combined':
        return renderCombinedDemo();
      default:
        return renderRadioButtonsDemo();
    }
  };

  return (
    <MaxBoxSized
      themeManager={themeManager}
      maxWidth={100}
      maxHeight={30}
      responsive={true}
      flexDirection="column"
      padding={1}
    >
      <Box flexDirection="column">
        <Text>{themeManager.primary('🎨 Component Showcase')}</Text>
        <Text>{themeManager.muted('Demonstrating the new reusable UI components')}</Text>
        <Text></Text>
        
        <Box marginBottom={2}>
          <RadioButtons
            themeManager={themeManager}
            options={demoOptions}
            selectedValue={currentDemo}
            onSelectionChange={handleDemoSelection}
            title="Select a demo:"
            variant="compact"
            showDescriptions={true}
          />
        </Box>

        <Box flexGrow={1}>
          {renderCurrentDemo()}
        </Box>

        <Box marginTop={1}>
          <Text>{themeManager.muted('Press Q or ESC to exit')}</Text>
        </Box>
      </Box>
    </MaxBoxSized>
  );
};
