/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { FileDiscoveryService } from '../services/fileDiscoveryService.js';
import { GeminiClient } from '../core/client.js';
export declare enum ApprovalMode {
    DEFAULT = "default",
    ALWAYS = "always",
    NEVER = "never",
    AUTO_EDIT = "auto_edit"
}
export interface MCPServerConfig {
    command?: string;
    args?: string[];
    url?: string;
    httpUrl?: string;
    cwd?: string;
    env?: Record<string, string>;
    headers?: Record<string, string>;
    timeout?: number;
    trust?: boolean;
}
export interface ConfigParameters {
    cwd: string;
    model: string;
    embeddingModel: string;
    sandbox: boolean | undefined;
    targetDir: string;
    debugMode: boolean;
    userMemory: string;
    arienMdFileCount: number;
    approvalMode: ApprovalMode;
    sessionId: string;
    apiKey?: string;
    provider?: string;
    temperature?: number;
    maxTokens?: number;
    toolDiscoveryCommand?: string;
    toolCallCommand?: string;
    mcpServerCommand?: string;
    mcpServers?: Record<string, MCPServerConfig>;
    fileFilteringRespectGitIgnore?: boolean;
    fullContext?: boolean;
    question?: string;
}
export declare class Config {
    private params;
    private fileService;
    private geminiClient;
    constructor(params: ConfigParameters);
    getCwd(): string;
    getModel(): string;
    getEmbeddingModel(): string;
    getSandbox(): boolean | undefined;
    getTargetDir(): string;
    getDebugMode(): boolean;
    getUserMemory(): string;
    getArienMdFileCount(): number;
    getApprovalMode(): ApprovalMode;
    setApprovalMode(mode: ApprovalMode): void;
    getSessionId(): string;
    getApiKey(): string | undefined;
    getProvider(): string | undefined;
    getTemperature(): number | undefined;
    getMaxTokens(): number | undefined;
    getToolDiscoveryCommand(): string | undefined;
    getToolCallCommand(): string | undefined;
    getMcpServerCommand(): string | undefined;
    getMcpServers(): Record<string, MCPServerConfig> | undefined;
    getFileFilteringRespectGitIgnore(): boolean;
    getFullContext(): boolean;
    getQuestion(): string | undefined;
    getFileService(): FileDiscoveryService;
    getGeminiClient(): GeminiClient;
    updateParams(updates: Partial<ConfigParameters>): void;
}
//# sourceMappingURL=config.d.ts.map