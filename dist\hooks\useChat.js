/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useCallback } from 'react';
import { getCoreSystemPrompt } from '../system-prompt/prompts.js';
export const useChat = ({ providerManager, toolRegistry, systemPrompt = getCoreSystemPrompt(), }) => {
    const [messages, setMessages] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [lastUserMessage, setLastUserMessage] = useState('');
    const addMessage = useCallback((message) => {
        const newMessage = {
            ...message,
            id: `${Date.now()}-${Math.random()}`,
            timestamp: Date.now(),
        };
        setMessages(prev => [...prev, newMessage]);
        return newMessage.id;
    }, []);
    const updateMessage = useCallback((id, updates) => {
        setMessages(prev => prev.map(msg => msg.id === id ? { ...msg, ...updates } : msg));
    }, []);
    const handleFunctionCalls = useCallback(async (functionCalls) => {
        for (const call of functionCalls) {
            try {
                const result = await toolRegistry.executeTool(call.name, call.args);
                // Add function result as a system message
                addMessage({
                    role: 'system',
                    content: `Tool ${call.name} result: ${JSON.stringify(result)}`,
                });
            }
            catch (error) {
                addMessage({
                    role: 'system',
                    content: `Tool ${call.name} error: ${error instanceof Error ? error.message : 'Unknown error'}`,
                    error: error instanceof Error ? error.message : 'Unknown error',
                });
            }
        }
    }, [toolRegistry, addMessage]);
    const sendMessage = useCallback(async (message) => {
        if (!message.trim() || isLoading)
            return;
        setError(null);
        setLastUserMessage(message);
        // Add user message
        addMessage({
            role: 'user',
            content: message,
        });
        setIsLoading(true);
        try {
            const provider = providerManager.getCurrentProvider();
            const tools = await toolRegistry.getToolDeclarations();
            // Create assistant message for streaming
            const assistantMessageId = addMessage({
                role: 'assistant',
                content: '',
                isStreaming: true,
            });
            // Get conversation history
            const conversationHistory = messages.map(msg => ({
                role: msg.role,
                content: msg.content,
            }));
            conversationHistory.push({ role: 'user', content: message });
            // Stream response
            const generator = provider.generateStreamingResponse(message, {
                tools,
                systemPrompt,
            });
            let fullResponse = '';
            for await (const chunk of generator) {
                if (typeof chunk === 'string') {
                    fullResponse += chunk;
                    updateMessage(assistantMessageId, { content: fullResponse });
                }
                else {
                    // Final response with function calls
                    const response = chunk;
                    if (response.functionCalls && response.functionCalls.length > 0) {
                        await handleFunctionCalls(response.functionCalls);
                    }
                    // Mark as complete
                    updateMessage(assistantMessageId, {
                        content: response.text,
                        isStreaming: false
                    });
                }
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            setError(errorMessage);
            addMessage({
                role: 'assistant',
                content: `Error: ${errorMessage}`,
                error: errorMessage,
            });
        }
        finally {
            setIsLoading(false);
        }
    }, [
        isLoading,
        messages,
        providerManager,
        toolRegistry,
        systemPrompt,
        addMessage,
        updateMessage,
        handleFunctionCalls,
    ]);
    const clearMessages = useCallback(() => {
        setMessages([]);
        setError(null);
    }, []);
    const retryLastMessage = useCallback(async () => {
        if (lastUserMessage) {
            // Remove the last assistant message if it was an error
            setMessages(prev => {
                const lastMessage = prev[prev.length - 1];
                if (lastMessage?.role === 'assistant' && lastMessage.error) {
                    return prev.slice(0, -1);
                }
                return prev;
            });
            await sendMessage(lastUserMessage);
        }
    }, [lastUserMessage, sendMessage]);
    return {
        messages,
        isLoading,
        error,
        sendMessage,
        clearMessages,
        retryLastMessage,
    };
};
//# sourceMappingURL=useChat.js.map