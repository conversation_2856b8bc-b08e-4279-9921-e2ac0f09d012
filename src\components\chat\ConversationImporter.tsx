/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { ThemeManager } from '../../themes/themes.js';
import { ChatMessage } from '../../hooks/useChat.js';
import * as fs from 'fs/promises';
import * as path from 'path';

interface ConversationImporterProps {
  themeManager: ThemeManager;
  isOpen: boolean;
  onClose: () => void;
  onImportComplete?: (messages: ChatMessage[]) => void;
}

interface ImportedData {
  exportedAt?: string;
  messageCount?: number;
  messages: any[];
}

export const ConversationImporter: React.FC<ConversationImporterProps> = ({
  themeManager,
  isOpen,
  onClose,
  onImportComplete,
}) => {
  const [fileName, setFileName] = useState('');
  const [isImporting, setIsImporting] = useState(false);
  const [importStatus, setImportStatus] = useState<string>('');
  const [previewData, setPreviewData] = useState<ImportedData | null>(null);
  const [importMode, setImportMode] = useState<'input' | 'preview' | 'confirm'>('input');

  useInput((input, key) => {
    if (!isOpen || isImporting) return;

    if (key.escape) {
      if (importMode === 'preview' || importMode === 'confirm') {
        setImportMode('input');
        setPreviewData(null);
        return;
      }
      onClose();
      return;
    }

    if (importMode === 'input') {
      if (key.return && fileName.trim()) {
        handlePreview();
        return;
      }

      if (key.backspace || key.delete) {
        setFileName(prev => prev.slice(0, -1));
        return;
      }

      if (input && input.length === 1 && !key.ctrl && !key.meta) {
        setFileName(prev => prev + input);
      }
    }

    if (importMode === 'confirm') {
      if (input === 'y' || input === 'Y') {
        handleImport();
        return;
      }

      if (input === 'n' || input === 'N') {
        setImportMode('input');
        setPreviewData(null);
        return;
      }
    }
  });

  const detectFileFormat = (filePath: string): 'json' | 'csv' | 'unknown' => {
    const ext = path.extname(filePath).toLowerCase();
    switch (ext) {
      case '.json':
        return 'json';
      case '.csv':
        return 'csv';
      default:
        return 'unknown';
    }
  };

  const parseJSONFile = async (content: string): Promise<ImportedData> => {
    try {
      const data = JSON.parse(content);
      
      // Validate structure
      if (!data.messages || !Array.isArray(data.messages)) {
        throw new Error('Invalid JSON format: missing messages array');
      }

      return {
        exportedAt: data.exportedAt,
        messageCount: data.messageCount || data.messages.length,
        messages: data.messages,
      };
    } catch (error) {
      throw new Error(`JSON parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const parseCSVFile = async (content: string): Promise<ImportedData> => {
    try {
      const lines = content.split('\n').filter(line => line.trim());
      if (lines.length < 2) {
        throw new Error('CSV file must have at least a header and one data row');
      }

      const header = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
      const messages: any[] = [];

      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());
        const message: any = {};

        header.forEach((col, index) => {
          if (values[index] !== undefined) {
            switch (col.toLowerCase()) {
              case 'role':
                message.role = values[index];
                break;
              case 'content':
                message.content = values[index];
                break;
              case 'timestamp':
                message.timestamp = new Date(values[index]).getTime();
                break;
              case 'error':
                if (values[index]) message.error = values[index];
                break;
              case 'isstreaming':
                message.isStreaming = values[index].toLowerCase() === 'true';
                break;
            }
          }
        });

        if (message.role && message.content) {
          messages.push(message);
        }
      }

      return {
        messageCount: messages.length,
        messages,
      };
    } catch (error) {
      throw new Error(`CSV parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handlePreview = async () => {
    if (!fileName.trim()) return;

    setIsImporting(true);
    setImportStatus('Reading file...');

    try {
      const filePath = path.resolve(process.cwd(), fileName);
      const content = await fs.readFile(filePath, 'utf-8');
      
      setImportStatus('Parsing content...');
      
      const format = detectFileFormat(filePath);
      let data: ImportedData;

      switch (format) {
        case 'json':
          data = await parseJSONFile(content);
          break;
        case 'csv':
          data = await parseCSVFile(content);
          break;
        default:
          throw new Error('Unsupported file format. Please use .json or .csv files.');
      }

      setPreviewData(data);
      setImportMode('preview');
      setImportStatus('');
      
    } catch (error) {
      setImportStatus(`Preview failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setTimeout(() => {
        setIsImporting(false);
        setImportStatus('');
      }, 3000);
    } finally {
      setIsImporting(false);
    }
  };

  const handleImport = async () => {
    if (!previewData) return;

    setIsImporting(true);
    setImportStatus('Importing messages...');

    try {
      const messages: ChatMessage[] = previewData.messages.map((msg, index) => ({
        id: msg.id || `imported-${Date.now()}-${index}`,
        role: msg.role || 'user',
        content: msg.content || '',
        timestamp: msg.timestamp || Date.now() + index,
        error: msg.error,
        isStreaming: msg.isStreaming || false,
      }));

      setImportStatus(`Successfully imported ${messages.length} messages`);
      onImportComplete?.(messages);
      
      setTimeout(() => {
        onClose();
      }, 2000);
      
    } catch (error) {
      setImportStatus(`Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setTimeout(() => {
        setIsImporting(false);
        setImportStatus('');
        setImportMode('input');
      }, 3000);
    }
  };

  if (!isOpen) return null;

  const renderFileInput = () => (
    <Box flexDirection="column">
      <Text>{themeManager.primary('Enter file path to import:')}</Text>
      <Text></Text>
      <Box marginLeft={2}>
        <Text>{themeManager.secondary('File: ')}</Text>
        <Text>{themeManager.primary(fileName || '...')}</Text>
      </Box>
      <Text></Text>
      <Box marginLeft={2}>
        <Text>{themeManager.muted('Supported formats: .json, .csv')}</Text>
        <Text>{themeManager.muted('Press Enter to preview, Esc to cancel')}</Text>
      </Box>
    </Box>
  );

  const renderPreview = () => {
    if (!previewData) return null;

    return (
      <Box flexDirection="column">
        <Text>{themeManager.primary('Import Preview:')}</Text>
        <Text></Text>
        
        <Box marginLeft={2} flexDirection="column">
          <Text>{themeManager.secondary('File: ')}{themeManager.info(fileName)}</Text>
          <Text>{themeManager.secondary('Messages: ')}{themeManager.info(previewData.messageCount?.toString() || '0')}</Text>
          {previewData.exportedAt && (
            <Text>{themeManager.secondary('Exported: ')}{themeManager.muted(new Date(previewData.exportedAt).toLocaleString())}</Text>
          )}
        </Box>
        
        <Text></Text>
        <Text>{themeManager.secondary('Sample Messages:')}</Text>
        
        {previewData.messages.slice(0, 3).map((msg, index) => (
          <Box key={index} marginLeft={2} marginTop={1} borderStyle="single" borderColor="gray" padding={1}>
            <Box flexDirection="column">
              <Text>{themeManager.accent(`${msg.role || 'unknown'}:`)} {themeManager.muted(new Date(msg.timestamp || 0).toLocaleString())}</Text>
              <Text>{themeManager.primary((msg.content || '').substring(0, 100) + (msg.content?.length > 100 ? '...' : ''))}</Text>
            </Box>
          </Box>
        ))}
        
        {previewData.messages.length > 3 && (
          <Box marginLeft={2} marginTop={1}>
            <Text>{themeManager.muted(`... and ${previewData.messages.length - 3} more messages`)}</Text>
          </Box>
        )}
        
        <Text></Text>
        <Text>{themeManager.warning('Import these messages? [y/N]')}</Text>
      </Box>
    );
  };

  const renderImportProgress = () => (
    <Box flexDirection="column">
      <Text>{themeManager.primary('Importing Conversation...')}</Text>
      <Text></Text>
      <Box marginLeft={2}>
        <Text>{themeManager.info(importStatus)}</Text>
      </Box>
    </Box>
  );

  return (
    <Box flexDirection="column" padding={1}>
      <Box borderStyle="round" borderColor="blue" padding={1}>
        <Box flexDirection="column" width="100%">
          <Text>{themeManager.primary('📥 Import Conversation')}</Text>
          <Text></Text>
          
          {isImporting ? (
            renderImportProgress()
          ) : importMode === 'input' ? (
            renderFileInput()
          ) : importMode === 'preview' ? (
            renderPreview()
          ) : null}
        </Box>
      </Box>
    </Box>
  );
};
