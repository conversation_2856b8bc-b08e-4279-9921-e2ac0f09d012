import { jsx as _jsx } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useEffect } from 'react';
import { Text } from 'ink';
export const Spinner = ({ themeManager, type = 'dots', color = 'primary', speed = 'normal', }) => {
    const [frame, setFrame] = useState(0);
    const getSpeedInterval = () => {
        switch (speed) {
            case 'slow':
                return 300;
            case 'fast':
                return 80;
            case 'normal':
            default:
                return 150;
        }
    };
    const getColorFunction = () => {
        switch (color) {
            case 'primary':
                return themeManager.primary;
            case 'secondary':
                return themeManager.secondary;
            case 'accent':
                return themeManager.accent;
            case 'success':
                return themeManager.success;
            case 'warning':
                return themeManager.warning;
            case 'error':
                return themeManager.error;
            case 'info':
                return themeManager.info;
            case 'muted':
                return themeManager.muted;
            default:
                return themeManager.primary;
        }
    };
    const getSpinnerFrames = () => {
        switch (type) {
            case 'dots':
                return ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
            case 'line':
                return ['|', '/', '-', '\\'];
            case 'pipe':
                return ['┤', '┘', '┴', '└', '├', '┌', '┬', '┐'];
            case 'star':
                return ['✶', '✸', '✹', '✺', '✹', '✷'];
            case 'bounce':
                return ['⠁', '⠂', '⠄', '⠂'];
            case 'toggle':
                return ['⊶', '⊷'];
            case 'balloon':
                return [' ', '.', 'o', 'O', '@', '*', ' '];
            case 'noise':
                return ['▓', '▒', '░'];
            case 'runner':
                return ['🚶', '🏃'];
            case 'pong':
                return ['▐⠂       ▌', '▐⠈       ▌', '▐ ⠂      ▌', '▐ ⠠      ▌', '▐  ⡀     ▌', '▐  ⠠     ▌', '▐   ⠂    ▌', '▐   ⠈    ▌', '▐    ⠂   ▌', '▐    ⠠   ▌', '▐     ⡀  ▌', '▐     ⠠  ▌', '▐      ⠂ ▌', '▐      ⠈ ▌', '▐       ⠂▌', '▐       ⠠▌', '▐       ⡀▌', '▐      ⠠ ▌', '▐      ⠂ ▌', '▐     ⠈  ▌', '▐     ⠂  ▌', '▐    ⠠   ▌', '▐    ⡀   ▌', '▐   ⠠    ▌', '▐   ⠂    ▌', '▐  ⠈     ▌', '▐  ⠂     ▌', '▐ ⠠      ▌', '▐ ⡀      ▌', '▐⠠       ▌'];
            default:
                return themeManager.getCurrentTheme().symbols.loading;
        }
    };
    useEffect(() => {
        const interval = setInterval(() => {
            setFrame(prev => (prev + 1) % getSpinnerFrames().length);
        }, getSpeedInterval());
        return () => clearInterval(interval);
    }, [type, speed]);
    const frames = getSpinnerFrames();
    const currentFrame = frames[frame];
    const colorFunction = getColorFunction();
    return _jsx(Text, { children: colorFunction(currentFrame) });
};
// Specialized spinner variants
export const LoadingSpinner = (props) => (_jsx(Spinner, { ...props, type: "dots" }));
export const ProcessingSpinner = (props) => (_jsx(Spinner, { ...props, type: "line" }));
export const SuccessSpinner = (props) => (_jsx(Spinner, { ...props, color: "success" }));
export const ErrorSpinner = (props) => (_jsx(Spinner, { ...props, color: "error" }));
export const WarningSpinner = (props) => (_jsx(Spinner, { ...props, color: "warning" }));
export const InfoSpinner = (props) => (_jsx(Spinner, { ...props, color: "info" }));
//# sourceMappingURL=Spinner.js.map