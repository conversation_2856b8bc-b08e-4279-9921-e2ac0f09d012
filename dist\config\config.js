/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { FileDiscoveryService } from '../services/fileDiscoveryService.js';
import { GeminiClient } from '../core/client.js';
export var ApprovalMode;
(function (ApprovalMode) {
    ApprovalMode["DEFAULT"] = "default";
    ApprovalMode["ALWAYS"] = "always";
    ApprovalMode["NEVER"] = "never";
    ApprovalMode["AUTO_EDIT"] = "auto_edit";
})(ApprovalMode || (ApprovalMode = {}));
export class Config {
    params;
    fileService;
    geminiClient = null;
    constructor(params) {
        this.params = params;
        this.fileService = new FileDiscoveryService(params.targetDir);
    }
    // Core getters
    getCwd() {
        return this.params.cwd;
    }
    getModel() {
        return this.params.model;
    }
    getEmbeddingModel() {
        return this.params.embeddingModel;
    }
    getSandbox() {
        return this.params.sandbox;
    }
    getTargetDir() {
        return this.params.targetDir;
    }
    getDebugMode() {
        return this.params.debugMode;
    }
    getUserMemory() {
        return this.params.userMemory;
    }
    getArienMdFileCount() {
        return this.params.arienMdFileCount;
    }
    getApprovalMode() {
        return this.params.approvalMode;
    }
    setApprovalMode(mode) {
        this.params.approvalMode = mode;
    }
    getSessionId() {
        return this.params.sessionId;
    }
    getApiKey() {
        return this.params.apiKey;
    }
    getProvider() {
        return this.params.provider;
    }
    getTemperature() {
        return this.params.temperature;
    }
    getMaxTokens() {
        return this.params.maxTokens;
    }
    // Tool discovery
    getToolDiscoveryCommand() {
        return this.params.toolDiscoveryCommand;
    }
    getToolCallCommand() {
        return this.params.toolCallCommand;
    }
    // MCP configuration
    getMcpServerCommand() {
        return this.params.mcpServerCommand;
    }
    getMcpServers() {
        return this.params.mcpServers;
    }
    // File filtering
    getFileFilteringRespectGitIgnore() {
        return this.params.fileFilteringRespectGitIgnore ?? true;
    }
    getFullContext() {
        return this.params.fullContext ?? false;
    }
    getQuestion() {
        return this.params.question;
    }
    // Services
    getFileService() {
        return this.fileService;
    }
    getGeminiClient() {
        if (!this.geminiClient) {
            this.geminiClient = new GeminiClient(this);
        }
        return this.geminiClient;
    }
    // Update methods
    updateParams(updates) {
        this.params = { ...this.params, ...updates };
    }
}
//# sourceMappingURL=config.js.map