/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { ThemeManager } from '../../themes/themes.js';

export interface NotificationData {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number; // in milliseconds, 0 for persistent
  dismissible?: boolean;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  key: string;
  handler: () => void;
}

interface NotificationProps {
  notification: NotificationData;
  themeManager: ThemeManager;
  onDismiss: (id: string) => void;
  position?: 'top' | 'bottom';
}

export const Notification: React.FC<NotificationProps> = ({
  notification,
  themeManager,
  onDismiss,
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [timeLeft, setTimeLeft] = useState(notification.duration || 0);

  useEffect(() => {
    if (notification.duration && notification.duration > 0) {
      const interval = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1000) {
            setIsVisible(false);
            setTimeout(() => onDismiss(notification.id), 300);
            return 0;
          }
          return prev - 1000;
        });
      }, 1000);

      return () => clearInterval(interval);
    }
    return undefined;
  }, [notification.duration, notification.id, onDismiss]);

  useInput((input, key) => {
    if (key.escape && notification.dismissible !== false) {
      onDismiss(notification.id);
    }

    // Handle action keys
    if (notification.actions) {
      const action = notification.actions.find(a => a.key === input);
      if (action) {
        action.handler();
        onDismiss(notification.id);
      }
    }
  });

  if (!isVisible) return null;

  const getTypeColor = () => {
    switch (notification.type) {
      case 'success':
        return themeManager.success;
      case 'error':
        return themeManager.error;
      case 'warning':
        return themeManager.warning;
      case 'info':
        return themeManager.info;
      default:
        return themeManager.primary;
    }
  };

  const getTypeIcon = () => {
    const theme = themeManager.getCurrentTheme();
    switch (notification.type) {
      case 'success':
        return theme.symbols.check;
      case 'error':
        return theme.symbols.cross;
      case 'warning':
        return theme.symbols.warning;
      case 'info':
        return theme.symbols.info;
      default:
        return theme.symbols.bullet;
    }
  };

  const getBorderColor = () => {
    switch (notification.type) {
      case 'success':
        return 'green';
      case 'error':
        return 'red';
      case 'warning':
        return 'yellow';
      case 'info':
        return 'blue';
      default:
        return 'gray';
    }
  };

  const renderHeader = () => (
    <Box justifyContent="space-between">
      <Box>
        <Text>{getTypeColor()(getTypeIcon())} </Text>
        <Text>{getTypeColor()(notification.title)}</Text>
      </Box>
      {notification.dismissible !== false && (
        <Text>{themeManager.muted('[ESC to dismiss]')}</Text>
      )}
    </Box>
  );

  const renderMessage = () => {
    if (!notification.message) return null;

    return (
      <Box marginTop={1}>
        <Text>{themeManager.primary(notification.message)}</Text>
      </Box>
    );
  };

  const renderActions = () => {
    if (!notification.actions || notification.actions.length === 0) return null;

    return (
      <Box marginTop={1} gap={2}>
        {notification.actions.map((action, index) => (
          <Text key={index}>
            {themeManager.accent(`[${action.key}]`)} {themeManager.secondary(action.label)}
          </Text>
        ))}
      </Box>
    );
  };

  const renderTimer = () => {
    if (!notification.duration || notification.duration === 0 || timeLeft === 0) return null;

    const seconds = Math.ceil(timeLeft / 1000);
    return (
      <Box marginTop={1}>
        <Text>{themeManager.muted(`Auto-dismiss in ${seconds}s`)}</Text>
      </Box>
    );
  };

  return (
    <Box
      borderStyle="round"
      borderColor={getBorderColor()}
      padding={1}
      flexDirection="column"
      width="100%"
    >
      {renderHeader()}
      {renderMessage()}
      {renderActions()}
      {renderTimer()}
    </Box>
  );
};

interface NotificationManagerProps {
  themeManager: ThemeManager;
  notifications: NotificationData[];
  onDismiss: (id: string) => void;
  maxVisible?: number;
  position?: 'top' | 'bottom';
}

export const NotificationManager: React.FC<NotificationManagerProps> = ({
  themeManager,
  notifications,
  onDismiss,
  maxVisible = 3,
  position = 'top',
}) => {
  const visibleNotifications = notifications.slice(0, maxVisible);

  if (visibleNotifications.length === 0) return null;

  return (
    <Box flexDirection="column" gap={1}>
      {visibleNotifications.map((notification) => (
        <Notification
          key={notification.id}
          notification={notification}
          themeManager={themeManager}
          onDismiss={onDismiss}
          position={position}
        />
      ))}
      {notifications.length > maxVisible && (
        <Box justifyContent="center">
          <Text>{themeManager.muted(`+${notifications.length - maxVisible} more notifications`)}</Text>
        </Box>
      )}
    </Box>
  );
};
