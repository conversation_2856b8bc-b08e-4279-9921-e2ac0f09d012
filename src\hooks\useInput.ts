/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { useState, useCallback } from 'react';
import { useInput as useInkInput } from 'ink';

export interface UseInputOptions {
  onSubmit: (input: string) => void;
  onClear?: () => void;
  onHelp?: () => void;
  onExit?: () => void;
  onPaste?: (pastedText: string) => void;
  disabled?: boolean;
  multiline?: boolean;
  placeholder?: string;
  masked?: boolean;
  maskChar?: string;
  validator?: (input: string) => boolean;
  characterFilter?: (char: string) => boolean;
  maxLength?: number;
}

export interface UseInputReturn {
  input: string;
  displayValue: string;
  setInput: (input: string) => void;
  clearInput: () => void;
  isDisabled: boolean;
  isPasteDetected: boolean;
}

export const useInput = ({
  onSubmit,
  onClear,
  onHelp,
  onExit,
  onPaste,
  disabled = false,
  masked = false,
  maskChar = '*',
  validator,
  characterFilter,
  maxLength,
}: UseInputOptions): UseInputReturn => {
  const [input, setInputState] = useState<string>('');
  const [isPasteDetected, setIsPasteDetected] = useState<boolean>(false);

  const setInput = useCallback((newInput: string) => {
    if (!disabled) {
      // Apply max length limit
      const limitedInput = maxLength ? newInput.slice(0, maxLength) : newInput;

      // Apply validator if provided
      if (validator && !validator(limitedInput)) {
        return;
      }

      setInputState(limitedInput);
    }
  }, [disabled, maxLength, validator]);

  const clearInput = useCallback(() => {
    setInputState('');
    setIsPasteDetected(false);
  }, []);

  // Default character filter for printable ASCII characters
  const defaultCharacterFilter = useCallback((char: string): boolean => {
    const charCode = char.charCodeAt(0);
    return charCode >= 32 && charCode <= 126;
  }, []);

  const activeCharacterFilter = characterFilter || defaultCharacterFilter;

  useInkInput((inputChar, key) => {
    if (disabled) return;

    // Handle special key combinations
    if (key.ctrl) {
      switch (inputChar.toLowerCase()) {
        case 'c':
          if (onExit) {
            onExit();
          } else {
            process.exit(0);
          }
          return;
        case 'l':
          if (onClear) {
            onClear();
          }
          return;
        case 'h':
          if (onHelp) {
            onHelp();
          }
          return;
        case 'v':
          // Ctrl+V paste detection (though Ink handles this automatically)
          return;
        default:
          return;
      }
    }

    // Handle Enter key
    if (key.return) {
      if (input.trim()) {
        onSubmit(input.trim());
        clearInput();
      }
      return;
    }

    // Handle Backspace/Delete
    if (key.backspace || key.delete) {
      setInputState(prev => prev.slice(0, -1));
      setIsPasteDetected(false);
      return;
    }

    // Handle paste operations (multiple characters at once)
    if (inputChar && inputChar.length > 1) {
      // This is a paste operation
      setIsPasteDetected(true);

      // Filter the pasted content
      const filteredContent = inputChar
        .split('')
        .filter(activeCharacterFilter)
        .join('');

      if (filteredContent) {
        // Apply max length limit
        const limitedContent = maxLength
          ? filteredContent.slice(0, maxLength - input.length)
          : filteredContent;

        // Apply validator if provided
        const newInput = input + limitedContent;
        if (validator && !validator(newInput)) {
          return;
        }

        setInputState(prev => prev + limitedContent);

        // Call paste callback if provided
        if (onPaste) {
          onPaste(limitedContent);
        }
      }
      return;
    }

    // Handle regular character input (single character)
    if (inputChar && inputChar.length === 1 && !key.ctrl && !key.meta) {
      if (activeCharacterFilter(inputChar)) {
        // Check max length
        if (maxLength && input.length >= maxLength) {
          return;
        }

        // Apply validator if provided
        const newInput = input + inputChar;
        if (validator && !validator(newInput)) {
          return;
        }

        setInputState(prev => prev + inputChar);
        setIsPasteDetected(false);
      }
    }
  });

  // Generate display value (masked or normal)
  const displayValue = masked
    ? maskChar.repeat(input.length)
    : input;

  return {
    input,
    displayValue,
    setInput,
    clearInput,
    isDisabled: disabled,
    isPasteDetected,
  };
};
