/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { UseInputOptions, UseInputReturn } from './useInput.js';
import { ProviderType } from '../providers/manager.js';
export interface UseApiKeyInputOptions extends Omit<UseInputOptions, 'validator' | 'characterFilter' | 'masked'> {
    provider: ProviderType;
    onValidationChange?: (isValid: boolean) => void;
}
export interface UseApiKeyInputReturn extends UseInputReturn {
    isValidFormat: boolean;
    validationMessage: string;
}
/**
 * Hook for API key input with validation and masking
 */
export declare const useApiKeyInput: ({ provider, onValidationChange, onPaste, ...options }: UseApiKeyInputOptions) => UseApiKeyInputReturn;
//# sourceMappingURL=useApiKeyInput.d.ts.map