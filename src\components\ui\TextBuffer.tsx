/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Box, Text, useInput } from 'ink';
import { ThemeManager } from '../../themes/themes.js';

export interface TextLine {
  id: string;
  content: string;
  timestamp?: number;
  metadata?: Record<string, any>;
  style?: 'normal' | 'bold' | 'italic' | 'underline' | 'strikethrough';
  color?: 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'error' | 'info' | 'muted';
}

export interface TextBufferProps {
  themeManager: ThemeManager;
  lines?: TextLine[];
  content?: string;
  maxLines?: number;
  maxWidth?: number;
  maxHeight?: number;
  scrollable?: boolean;
  showLineNumbers?: boolean;
  showTimestamps?: boolean;
  enableSearch?: boolean;
  enableSelection?: boolean;
  wordWrap?: boolean;
  autoScroll?: boolean;
  scrollToBottom?: boolean;
  highlightPattern?: string | RegExp;
  filterPattern?: string | RegExp;
  onLineClick?: (line: TextLine, index: number) => void;
  onSelectionChange?: (selectedLines: TextLine[]) => void;
  onScroll?: (scrollPosition: { top: number; bottom: number }) => void;
  variant?: 'default' | 'compact' | 'detailed' | 'code';
  borderStyle?: 'single' | 'double' | 'round' | 'bold';
  borderColor?: string;
  padding?: number;
  margin?: number;
  title?: string;
  footer?: string;
  emptyMessage?: string;
  loadingMessage?: string;
  isLoading?: boolean;
  disabled?: boolean;
}

export const TextBuffer: React.FC<TextBufferProps> = ({
  themeManager,
  lines = [],
  content,
  maxLines = 1000,
  maxWidth,
  maxHeight = 20,
  scrollable = true,
  showLineNumbers = false,
  showTimestamps = false,
  enableSearch = false,
  enableSelection = false,
  autoScroll = true,
  scrollToBottom = false,
  highlightPattern,
  filterPattern,
  onSelectionChange,
  onScroll,
  borderStyle,
  borderColor,
  padding = 0,
  margin = 0,
  title,
  footer,
  emptyMessage = 'No content to display',
  loadingMessage = 'Loading...',
  isLoading = false,
  disabled = false,
}) => {
  const [scrollOffset, setScrollOffset] = useState(0);
  const [selectedLines, setSelectedLines] = useState<Set<number>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [searchMode, setSearchMode] = useState(false);
  const containerRef = useRef<any>(null);

  // Convert content string to lines if provided
  const processedLines = useMemo(() => {
    let allLines: TextLine[] = [];

    if (content) {
      const contentLines = content.split('\n').map((line, index) => ({
        id: `content-${index}`,
        content: line,
        timestamp: Date.now(),
      }));
      allLines = [...contentLines];
    }

    if (lines.length > 0) {
      allLines = [...allLines, ...lines];
    }

    // Apply filtering
    if (filterPattern) {
      const regex = typeof filterPattern === 'string' ? new RegExp(filterPattern, 'i') : filterPattern;
      allLines = allLines.filter(line => regex.test(line.content));
    }

    // Apply search filtering
    if (searchTerm) {
      const searchRegex = new RegExp(searchTerm, 'i');
      allLines = allLines.filter(line => searchRegex.test(line.content));
    }

    // Limit lines to maxLines
    if (allLines.length > maxLines) {
      allLines = allLines.slice(-maxLines);
    }

    return allLines;
  }, [content, lines, maxLines, filterPattern, searchTerm]);

  // Calculate visible lines based on scroll and height
  const visibleLines = useMemo(() => {
    if (!scrollable) return processedLines;
    
    const startIndex = scrollOffset;
    const endIndex = Math.min(processedLines.length, scrollOffset + maxHeight);
    return processedLines.slice(startIndex, endIndex);
  }, [processedLines, scrollOffset, maxHeight, scrollable]);

  // Auto-scroll to bottom when new content is added
  useEffect(() => {
    if (autoScroll || scrollToBottom) {
      const maxScroll = Math.max(0, processedLines.length - maxHeight);
      setScrollOffset(maxScroll);
    }
  }, [processedLines.length, autoScroll, scrollToBottom, maxHeight]);

  // Handle keyboard input for scrolling and search
  useInput((input, key) => {
    if (disabled) return;

    if (searchMode) {
      if (key.escape) {
        setSearchMode(false);
        setSearchTerm('');
      } else if (key.return) {
        setSearchMode(false);
      } else if (key.backspace) {
        setSearchTerm(prev => prev.slice(0, -1));
      } else if (input && input.length === 1) {
        setSearchTerm(prev => prev + input);
      }
      return;
    }

    if (scrollable) {
      if (key.upArrow) {
        setScrollOffset(prev => Math.max(0, prev - 1));
      } else if (key.downArrow) {
        setScrollOffset(prev => Math.min(processedLines.length - maxHeight, prev + 1));
      } else if (key.pageUp) {
        setScrollOffset(prev => Math.max(0, prev - maxHeight));
      } else if (key.pageDown) {
        setScrollOffset(prev => Math.min(processedLines.length - maxHeight, prev + maxHeight));
      } else if (input === 'g') {
        setScrollOffset(0); // Go to top
      } else if (input === 'G') {
        setScrollOffset(Math.max(0, processedLines.length - maxHeight)); // Go to bottom
      }
    }

    if (enableSearch && input === '/') {
      setSearchMode(true);
      setSearchTerm('');
    }

    // Handle line selection
    if (enableSelection && input >= '1' && input <= '9') {
      const lineIndex = parseInt(input) - 1;
      if (lineIndex < visibleLines.length) {
        const actualIndex = scrollOffset + lineIndex;
        const newSelected = new Set(selectedLines);
        if (newSelected.has(actualIndex)) {
          newSelected.delete(actualIndex);
        } else {
          newSelected.add(actualIndex);
        }
        setSelectedLines(newSelected);
        
        if (onSelectionChange) {
          const selectedLineObjects = Array.from(newSelected).map(index => processedLines[index]);
          onSelectionChange(selectedLineObjects);
        }
      }
    }
  });

  // Notify scroll changes
  useEffect(() => {
    if (onScroll) {
      onScroll({
        top: scrollOffset,
        bottom: scrollOffset + visibleLines.length,
      });
    }
  }, [scrollOffset, visibleLines.length, onScroll]);

  const formatTimestamp = (timestamp?: number) => {
    if (!timestamp || !showTimestamps) return '';
    const date = new Date(timestamp);
    return `[${date.toLocaleTimeString()}] `;
  };

  const formatLineNumber = (index: number) => {
    if (!showLineNumbers) return '';
    const lineNum = (scrollOffset + index + 1).toString().padStart(3, ' ');
    return `${lineNum}: `;
  };

  const applyHighlighting = (content: string) => {
    if (!highlightPattern) return content;

    // For terminal, we'll just return the content as-is since we can't do complex highlighting
    // In a real implementation, you might want to use different colors for matches
    return content;
  };

  const getLineStyle = (line: TextLine, isSelected: boolean) => {
    if (isSelected) {
      return themeManager.highlight;
    }

    if (line.color) {
      return themeManager[line.color];
    }

    return (text: string) => text;
  };

  const renderLine = (line: TextLine, index: number) => {
    const actualIndex = scrollOffset + index;
    const isSelected = selectedLines.has(actualIndex);
    const styleFunction = getLineStyle(line, isSelected);

    const timestamp = formatTimestamp(line.timestamp);
    const lineNumber = formatLineNumber(index);
    const highlightedContent = applyHighlighting(line.content);

    let styledContent = highlightedContent;
    if (line.style === 'bold') {
      styledContent = highlightedContent; // Terminal styling would be applied here
    }

    const fullContent = `${timestamp}${lineNumber}${styledContent}`;

    // Note: Ink Box doesn't support onClick, so we'll handle clicks through keyboard input
    return (
      <Box key={line.id}>
        <Text>{styleFunction(fullContent)}</Text>
      </Box>
    );
  };

  const renderTitle = () => {
    if (!title) return null;
    return (
      <Box marginBottom={1}>
        <Text>{themeManager.primary(title)}</Text>
      </Box>
    );
  };

  const renderFooter = () => {
    if (!footer && !scrollable) return null;
    
    const scrollInfo = scrollable ? 
      ` (${scrollOffset + 1}-${scrollOffset + visibleLines.length} of ${processedLines.length})` : '';
    const footerText = footer || `Lines${scrollInfo}`;
    
    return (
      <Box marginTop={1}>
        <Text>{themeManager.muted(footerText)}</Text>
      </Box>
    );
  };

  const renderSearchBar = () => {
    if (!searchMode) return null;
    
    return (
      <Box marginBottom={1}>
        <Text>{themeManager.primary('Search: ')}</Text>
        <Text>{searchTerm}</Text>
        <Text>{themeManager.accent('▋')}</Text>
      </Box>
    );
  };

  const renderScrollIndicators = () => {
    if (!scrollable || processedLines.length <= maxHeight) return null;
    
    const hasMore = scrollOffset + maxHeight < processedLines.length;
    const hasPrevious = scrollOffset > 0;
    
    return (
      <Box justifyContent="space-between">
        <Text>{hasPrevious ? themeManager.muted('↑ More above') : ''}</Text>
        <Text>{hasMore ? themeManager.muted('↓ More below') : ''}</Text>
      </Box>
    );
  };

  if (isLoading) {
    return (
      <Box flexDirection="column" padding={padding} margin={margin}>
        {renderTitle()}
        <Text>{themeManager.muted(loadingMessage)}</Text>
      </Box>
    );
  }

  if (processedLines.length === 0) {
    return (
      <Box flexDirection="column" padding={padding} margin={margin}>
        {renderTitle()}
        <Text>{themeManager.muted(emptyMessage)}</Text>
      </Box>
    );
  }

  return (
    <Box
      ref={containerRef}
      flexDirection="column"
      width={maxWidth}
      height={maxHeight}
      borderStyle={borderStyle}
      borderColor={borderColor}
      padding={padding}
      margin={margin}
    >
      {renderTitle()}
      {renderSearchBar()}
      {renderScrollIndicators()}
      <Box flexDirection="column" flexGrow={1}>
        {visibleLines.map((line, index) => renderLine(line, index))}
      </Box>
      {renderScrollIndicators()}
      {renderFooter()}
    </Box>
  );
};

// Convenience components for common use cases
export const CodeTextBuffer: React.FC<Omit<TextBufferProps, 'variant' | 'showLineNumbers'>> = (props) => (
  <TextBuffer {...props} variant="code" showLineNumbers={true} />
);

export const LogTextBuffer: React.FC<Omit<TextBufferProps, 'variant' | 'showTimestamps'>> = (props) => (
  <TextBuffer {...props} variant="detailed" showTimestamps={true} />
);

export const CompactTextBuffer: React.FC<Omit<TextBufferProps, 'variant'>> = (props) => (
  <TextBuffer {...props} variant="compact" />
);

export const ScrollableTextBuffer: React.FC<Omit<TextBufferProps, 'scrollable'>> = (props) => (
  <TextBuffer {...props} scrollable={true} />
);
