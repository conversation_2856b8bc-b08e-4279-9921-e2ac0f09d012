/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import React from 'react';
import { ThemeManager } from '../../themes/themes.js';
export interface NotificationData {
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message?: string;
    duration?: number;
    dismissible?: boolean;
    actions?: NotificationAction[];
}
export interface NotificationAction {
    label: string;
    key: string;
    handler: () => void;
}
interface NotificationProps {
    notification: NotificationData;
    themeManager: ThemeManager;
    onDismiss: (id: string) => void;
    position?: 'top' | 'bottom';
}
export declare const Notification: React.FC<NotificationProps>;
interface NotificationManagerProps {
    themeManager: ThemeManager;
    notifications: NotificationData[];
    onDismiss: (id: string) => void;
    maxVisible?: number;
    position?: 'top' | 'bottom';
}
export declare const NotificationManager: React.FC<NotificationManagerProps>;
export {};
//# sourceMappingURL=Notification.d.ts.map