import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import * as fs from 'fs/promises';
import * as path from 'path';
export const ConversationExporter = ({ themeManager, messages, isOpen, onClose, onExportComplete, }) => {
    const [selectedFormat, setSelectedFormat] = useState('markdown');
    const [fileName, setFileName] = useState('');
    const [isExporting, setIsExporting] = useState(false);
    const [exportStatus, setExportStatus] = useState('');
    const [inputMode, setInputMode] = useState('format');
    const formats = [
        { format: 'markdown', label: 'Markdown', description: 'Human-readable format with formatting' },
        { format: 'json', label: 'JSON', description: 'Structured data format for import' },
        { format: 'txt', label: 'Plain Text', description: 'Simple text format' },
        { format: 'csv', label: 'CSV', description: 'Spreadsheet-compatible format' },
    ];
    useInput((input, key) => {
        if (!isOpen || isExporting)
            return;
        if (key.escape) {
            onClose();
            return;
        }
        if (inputMode === 'format') {
            if (key.upArrow) {
                const currentIndex = formats.findIndex(f => f.format === selectedFormat);
                const newIndex = currentIndex > 0 ? currentIndex - 1 : formats.length - 1;
                setSelectedFormat(formats[newIndex].format);
                return;
            }
            if (key.downArrow) {
                const currentIndex = formats.findIndex(f => f.format === selectedFormat);
                const newIndex = currentIndex < formats.length - 1 ? currentIndex + 1 : 0;
                setSelectedFormat(formats[newIndex].format);
                return;
            }
            if (key.return) {
                setInputMode('filename');
                if (!fileName) {
                    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                    setFileName(`conversation-${timestamp}.${selectedFormat}`);
                }
                return;
            }
        }
        if (inputMode === 'filename') {
            if (key.return && fileName.trim()) {
                handleExport();
                return;
            }
            if (key.backspace || key.delete) {
                setFileName(prev => prev.slice(0, -1));
                return;
            }
            if (input && input.length === 1 && !key.ctrl && !key.meta) {
                setFileName(prev => prev + input);
            }
        }
    });
    const generateMarkdown = () => {
        let content = `# Conversation Export\n\n`;
        content += `**Exported:** ${new Date().toLocaleString()}\n`;
        content += `**Messages:** ${messages.length}\n\n`;
        content += `---\n\n`;
        messages.forEach((message, index) => {
            const timestamp = new Date(message.timestamp).toLocaleString();
            const role = message.role.charAt(0).toUpperCase() + message.role.slice(1);
            content += `## Message ${index + 1} - ${role}\n\n`;
            content += `**Time:** ${timestamp}\n\n`;
            if (message.error) {
                content += `**Error:** ${message.error}\n\n`;
            }
            content += `${message.content}\n\n`;
            content += `---\n\n`;
        });
        return content;
    };
    const generateJSON = () => {
        const exportData = {
            exportedAt: new Date().toISOString(),
            messageCount: messages.length,
            messages: messages.map((message, index) => ({
                index,
                role: message.role,
                content: message.content,
                timestamp: message.timestamp,
                error: message.error || null,
                isStreaming: message.isStreaming || false,
            })),
        };
        return JSON.stringify(exportData, null, 2);
    };
    const generatePlainText = () => {
        let content = `Conversation Export\n`;
        content += `Exported: ${new Date().toLocaleString()}\n`;
        content += `Messages: ${messages.length}\n\n`;
        content += `${'='.repeat(50)}\n\n`;
        messages.forEach((message) => {
            const timestamp = new Date(message.timestamp).toLocaleString();
            const role = message.role.toUpperCase();
            content += `[${timestamp}] ${role}:\n`;
            if (message.error) {
                content += `ERROR: ${message.error}\n`;
            }
            content += `${message.content}\n\n`;
            content += `${'-'.repeat(30)}\n\n`;
        });
        return content;
    };
    const generateCSV = () => {
        let content = 'Index,Role,Timestamp,Content,Error,IsStreaming\n';
        messages.forEach((message, index) => {
            const escapedContent = `"${message.content.replace(/"/g, '""')}"`;
            const error = message.error ? `"${message.error.replace(/"/g, '""')}"` : '';
            const isStreaming = message.isStreaming ? 'true' : 'false';
            content += `${index},"${message.role}","${new Date(message.timestamp).toISOString()}",${escapedContent},"${error}",${isStreaming}\n`;
        });
        return content;
    };
    const handleExport = async () => {
        if (!fileName.trim())
            return;
        setIsExporting(true);
        setExportStatus('Generating export data...');
        try {
            let content;
            switch (selectedFormat) {
                case 'markdown':
                    content = generateMarkdown();
                    break;
                case 'json':
                    content = generateJSON();
                    break;
                case 'txt':
                    content = generatePlainText();
                    break;
                case 'csv':
                    content = generateCSV();
                    break;
                default:
                    throw new Error(`Unsupported format: ${selectedFormat}`);
            }
            setExportStatus('Writing file...');
            const filePath = path.resolve(process.cwd(), fileName);
            await fs.writeFile(filePath, content, 'utf-8');
            setExportStatus(`Successfully exported to: ${filePath}`);
            onExportComplete?.(filePath);
            setTimeout(() => {
                onClose();
            }, 2000);
        }
        catch (error) {
            setExportStatus(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            setTimeout(() => {
                setIsExporting(false);
                setExportStatus('');
            }, 3000);
        }
    };
    if (!isOpen)
        return null;
    const renderFormatSelection = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary('Select Export Format:') }), _jsx(Text, {}), formats.map((format) => (_jsx(Box, { marginLeft: 2, children: _jsxs(Text, { children: [selectedFormat === format.format ? themeManager.highlight(' ▶ ') : '   ', themeManager.primary(format.label), themeManager.muted(` - ${format.description}`)] }) }, format.format)))] }));
    const renderFilenameInput = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary('Enter filename:') }), _jsx(Text, {}), _jsxs(Box, { marginLeft: 2, children: [_jsx(Text, { children: themeManager.secondary('File: ') }), _jsx(Text, { children: themeManager.primary(fileName || '...') })] }), _jsx(Text, {}), _jsx(Box, { marginLeft: 2, children: _jsx(Text, { children: themeManager.muted('Press Enter to export, Esc to cancel') }) })] }));
    const renderExportProgress = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary('Exporting Conversation...') }), _jsx(Text, {}), _jsx(Box, { marginLeft: 2, children: _jsx(Text, { children: themeManager.info(exportStatus) }) })] }));
    const renderSummary = () => (_jsxs(Box, { flexDirection: "column", marginTop: 1, borderStyle: "single", borderColor: "gray", padding: 1, children: [_jsx(Text, { children: themeManager.secondary('Export Summary:') }), _jsx(Text, { children: themeManager.muted(`Messages: ${messages.length}`) }), _jsx(Text, { children: themeManager.muted(`Format: ${selectedFormat.toUpperCase()}`) }), fileName && _jsx(Text, { children: themeManager.muted(`File: ${fileName}`) })] }));
    return (_jsx(Box, { flexDirection: "column", padding: 1, children: _jsx(Box, { borderStyle: "round", borderColor: "blue", padding: 1, children: _jsxs(Box, { flexDirection: "column", width: "100%", children: [_jsx(Text, { children: themeManager.primary('📤 Export Conversation') }), _jsx(Text, {}), isExporting ? (renderExportProgress()) : inputMode === 'format' ? (renderFormatSelection()) : (renderFilenameInput()), !isExporting && renderSummary()] }) }) }));
};
//# sourceMappingURL=ConversationExporter.js.map