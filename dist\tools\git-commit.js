/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import fs from 'fs';
import path from 'path';
import { BaseTool } from './tools.js';
import { SchemaValidator } from '../utils/schemaValidator.js';
import { getErrorMessage } from '../utils/errors.js';
import { spawn } from 'child_process';
export class GitCommitTool extends BaseTool {
    config;
    static Name = 'git_commit';
    constructor(config) {
        super(GitCommitTool.Name, 'Git Commit', `Creates git commits with comprehensive options:
- Commit staged changes with a message
- Optionally add files before committing
- Support for commit message templates and best practices
- Amend previous commits
- Author override and verification bypass options

Follows conventional commit message formats and provides validation for commit quality.`, {
            type: 'object',
            properties: {
                directory: {
                    type: 'string',
                    description: '(OPTIONAL) Directory to run git commit in. Must be relative to the project root directory. Defaults to project root.',
                },
                message: {
                    type: 'string',
                    description: 'Commit message. Should be clear and descriptive. First line should be 50 chars or less, followed by blank line and detailed description if needed.',
                },
                description: {
                    type: 'string',
                    description: '(OPTIONAL) Extended commit description. Will be added after the main message with a blank line separator.',
                },
                add_all: {
                    type: 'boolean',
                    description: '(OPTIONAL) Add all modified and deleted files before committing (git add -A). Defaults to false.',
                },
                add_files: {
                    type: 'array',
                    items: { type: 'string' },
                    description: '(OPTIONAL) Specific files to add before committing. Paths relative to repository root.',
                },
                amend: {
                    type: 'boolean',
                    description: '(OPTIONAL) Amend the previous commit instead of creating a new one. Defaults to false.',
                },
                no_verify: {
                    type: 'boolean',
                    description: '(OPTIONAL) Bypass pre-commit and commit-msg hooks. Use with caution. Defaults to false.',
                },
                author: {
                    type: 'string',
                    description: '(OPTIONAL) Override commit author in format "Name <email>". If not provided, uses git config.',
                },
            },
            required: ['message'],
        }, true, // output is markdown
        false);
        this.config = config;
    }
    validateToolParams(params) {
        const validation = SchemaValidator.validate(params, this.parameterSchema);
        if (!validation.isValid) {
            return `Parameters failed schema validation: ${validation.errors.join(', ')}`;
        }
        if (params.directory) {
            if (path.isAbsolute(params.directory)) {
                return 'Directory cannot be absolute. Must be relative to the project root directory.';
            }
            const directory = path.resolve(this.config.getTargetDir(), params.directory);
            if (!fs.existsSync(directory)) {
                return 'Directory must exist.';
            }
        }
        // Validate commit message
        if (!params.message.trim()) {
            return 'Commit message cannot be empty.';
        }
        if (params.message.length > 500) {
            return 'Commit message is too long. Consider using the description field for longer explanations.';
        }
        // Validate author format if provided
        if (params.author) {
            const authorRegex = /^.+ <.+@.+\..+>$/;
            if (!authorRegex.test(params.author)) {
                return 'Author must be in format "Name <<EMAIL>>".';
            }
        }
        return null;
    }
    async executeGitCommand(args, cwd) {
        return new Promise((resolve) => {
            const process = spawn('git', args, {
                cwd,
                stdio: ['ignore', 'pipe', 'pipe'],
            });
            let stdout = '';
            let stderr = '';
            process.stdout?.on('data', (data) => {
                stdout += data.toString();
            });
            process.stderr?.on('data', (data) => {
                stderr += data.toString();
            });
            process.on('close', (exitCode) => {
                resolve({ stdout, stderr, exitCode: exitCode || 0 });
            });
        });
    }
    formatCommitMessage(message, description) {
        let fullMessage = message.trim();
        if (description && description.trim()) {
            fullMessage += '\n\n' + description.trim();
        }
        return fullMessage;
    }
    analyzeCommitMessage(message) {
        const warnings = [];
        const suggestions = [];
        const firstLine = message.split('\n')[0];
        // Check first line length
        if (firstLine.length > 50) {
            warnings.push('First line is longer than 50 characters');
            suggestions.push('Consider shortening the summary line');
        }
        // Check for imperative mood
        const imperativeWords = ['add', 'fix', 'update', 'remove', 'refactor', 'improve', 'implement'];
        const startsWithImperative = imperativeWords.some(word => firstLine.toLowerCase().startsWith(word));
        if (!startsWithImperative && !firstLine.match(/^[A-Z]/)) {
            suggestions.push('Consider starting with an imperative verb (add, fix, update, etc.)');
        }
        // Check for conventional commit format
        const conventionalPattern = /^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .+/;
        if (!conventionalPattern.test(firstLine)) {
            suggestions.push('Consider using conventional commit format: type(scope): description');
        }
        return { warnings, suggestions };
    }
    async execute(params, abortSignal) {
        const validationError = this.validateToolParams(params);
        if (validationError) {
            return {
                llmContent: `Error: ${validationError}`,
                returnDisplay: `Git Commit Error: ${validationError}`,
            };
        }
        if (abortSignal.aborted) {
            return {
                llmContent: 'Git commit command was cancelled by user.',
                returnDisplay: 'Git commit cancelled.',
            };
        }
        try {
            const workingDir = params.directory
                ? path.resolve(this.config.getTargetDir(), params.directory)
                : this.config.getTargetDir();
            // Check if directory is a git repository
            const gitDirCheck = await this.executeGitCommand(['rev-parse', '--git-dir'], workingDir);
            if (gitDirCheck.exitCode !== 0) {
                return {
                    llmContent: `Error: Not a git repository (or any of the parent directories)`,
                    returnDisplay: 'Error: Not a git repository',
                };
            }
            let output = '# Git Commit\n\n';
            // Add files if requested
            if (params.add_all) {
                const addResult = await this.executeGitCommand(['add', '-A'], workingDir);
                if (addResult.exitCode !== 0) {
                    return {
                        llmContent: `Failed to add files: ${addResult.stderr}`,
                        returnDisplay: `Git add error: ${addResult.stderr}`,
                    };
                }
                output += '✅ Added all modified and deleted files\n\n';
            }
            else if (params.add_files && params.add_files.length > 0) {
                const addArgs = ['add', ...params.add_files];
                const addResult = await this.executeGitCommand(addArgs, workingDir);
                if (addResult.exitCode !== 0) {
                    return {
                        llmContent: `Failed to add specified files: ${addResult.stderr}`,
                        returnDisplay: `Git add error: ${addResult.stderr}`,
                    };
                }
                output += `✅ Added files: ${params.add_files.join(', ')}\n\n`;
            }
            // Check if there are staged changes (unless amending)
            if (!params.amend) {
                const statusResult = await this.executeGitCommand(['diff', '--cached', '--name-only'], workingDir);
                if (statusResult.exitCode === 0 && !statusResult.stdout.trim()) {
                    return {
                        llmContent: 'Error: No staged changes to commit. Use add_all or add_files to stage changes first.',
                        returnDisplay: 'No staged changes to commit',
                    };
                }
            }
            // Analyze commit message
            const fullMessage = this.formatCommitMessage(params.message, params.description);
            const analysis = this.analyzeCommitMessage(fullMessage);
            // Build commit command
            const commitArgs = ['commit', '-m', fullMessage];
            if (params.amend) {
                commitArgs.push('--amend');
            }
            if (params.no_verify) {
                commitArgs.push('--no-verify');
            }
            if (params.author) {
                commitArgs.push('--author', params.author);
            }
            // Execute commit
            const commitResult = await this.executeGitCommand(commitArgs, workingDir);
            if (commitResult.exitCode !== 0) {
                return {
                    llmContent: `Failed to create commit: ${commitResult.stderr}`,
                    returnDisplay: `Git commit error: ${commitResult.stderr}`,
                };
            }
            // Format success output
            if (params.amend) {
                output += '✅ **Amended previous commit successfully**\n\n';
            }
            else {
                output += '✅ **Commit created successfully**\n\n';
            }
            output += `**Message:** ${params.message}\n`;
            if (params.description) {
                output += `**Description:** ${params.description}\n`;
            }
            // Show commit details
            if (commitResult.stdout.trim()) {
                output += '\n**Commit details:**\n```\n' + commitResult.stdout + '\n```\n';
            }
            // Add warnings and suggestions
            if (analysis.warnings.length > 0) {
                output += '\n## ⚠️ Warnings\n\n';
                for (const warning of analysis.warnings) {
                    output += `- ${warning}\n`;
                }
            }
            if (analysis.suggestions.length > 0) {
                output += '\n## 💡 Suggestions for future commits\n\n';
                for (const suggestion of analysis.suggestions) {
                    output += `- ${suggestion}\n`;
                }
            }
            const displayMessage = params.amend
                ? 'Amended previous commit'
                : `Created commit: ${params.message.split('\n')[0].substring(0, 50)}${params.message.length > 50 ? '...' : ''}`;
            return {
                llmContent: output,
                returnDisplay: displayMessage,
            };
        }
        catch (error) {
            const errorMessage = `Error executing git commit: ${getErrorMessage(error)}`;
            return {
                llmContent: errorMessage,
                returnDisplay: errorMessage,
            };
        }
    }
}
//# sourceMappingURL=git-commit.js.map