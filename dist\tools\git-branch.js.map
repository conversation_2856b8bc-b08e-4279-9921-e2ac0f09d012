{"version": 3, "file": "git-branch.js", "sourceRoot": "", "sources": ["../../src/tools/git-branch.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,IAAI,MAAM,MAAM,CAAC;AAExB,OAAO,EAAE,QAAQ,EAAc,MAAM,YAAY,CAAC;AAClD,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAC9D,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAatC,MAAM,OAAO,aAAc,SAAQ,QAAqC;IAGzC;IAF7B,MAAM,CAAC,IAAI,GAAW,YAAY,CAAC;IAEnC,YAA6B,MAAc;QACzC,KAAK,CACH,aAAa,CAAC,IAAI,EAClB,YAAY,EACZ;;;;;;;wFAOkF,EAClF;YACE,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,SAAS,EAAE;oBACT,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,iIAAiI;iBAC/I;gBACD,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC;oBACrD,WAAW,EAAE,qJAAqJ;iBACnK;gBACD,WAAW,EAAE;oBACX,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,2GAA2G;iBACzH;gBACD,aAAa,EAAE;oBACb,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,2HAA2H;iBACzI;gBACD,KAAK,EAAE;oBACL,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,wJAAwJ;iBACtK;gBACD,MAAM,EAAE;oBACN,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,oHAAoH;iBAClI;gBACD,GAAG,EAAE;oBACH,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,kGAAkG;iBAChH;aACF;YACD,QAAQ,EAAE,CAAC,QAAQ,CAAC;SACrB,EACD,IAAI,EAAE,qBAAqB;QAC3B,KAAK,CACN,CAAC;QAjDyB,WAAM,GAAN,MAAM,CAAQ;IAkD3C,CAAC;IAED,kBAAkB,CAAC,MAAuB;QACxC,MAAM,UAAU,GAAG,eAAe,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAC1E,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YACxB,OAAO,wCAAwC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAChF,CAAC;QAED,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;gBACtC,OAAO,+EAA+E,CAAC;YACzF,CAAC;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;YAC7E,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,OAAO,uBAAuB,CAAC;YACjC,CAAC;QACH,CAAC;QAED,oDAAoD;QACpD,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YACpD,OAAO,+BAA+B,MAAM,CAAC,MAAM,UAAU,CAAC;QAChE,CAAC;QAED,8BAA8B;QAC9B,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YACvB,IAAI,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChH,OAAO,0FAA0F,CAAC;YACpG,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,IAAc,EAAE,GAAW;QACzD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE;gBACjC,GAAG;gBACH,KAAK,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;aAClC,CAAC,CAAC;YAEH,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,MAAM,GAAG,EAAE,CAAC;YAEhB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAClC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5B,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAClC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5B,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,EAAE;gBAC/B,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,IAAI,CAAC,EAAE,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,UAAkB;QAC/C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,CAAC,QAAQ,EAAE,gBAAgB,CAAC,EAAE,UAAU,CAAC,CAAC;QACtF,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,MAAM,CAAC;IACxC,CAAC;IAEO,gBAAgB,CAAC,MAAc,EAAE,UAAmB,EAAE,OAAgB;QAC5E,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAC7D,IAAI,MAAM,GAAG,oBAAoB,CAAC;QAElC,MAAM,aAAa,GAAa,EAAE,CAAC;QACnC,MAAM,cAAc,GAAa,EAAE,CAAC;QACpC,IAAI,aAAa,GAAG,EAAE,CAAC;QAEvB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAC5B,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC7B,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBACrC,aAAa,CAAC,IAAI,CAAC,KAAK,aAAa,cAAc,CAAC,CAAC;YACvD,CAAC;iBAAM,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC1C,MAAM,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB;gBAC/D,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACpC,CAAC;iBAAM,IAAI,OAAO,EAAE,CAAC;gBACnB,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;QAED,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE,CAAC;YAC5B,MAAM,IAAI,0BAA0B,CAAC;YACrC,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,MAAM,IAAI,CAAC;YAC5B,CAAC;QACH,CAAC;aAAM,IAAI,UAAU,IAAI,CAAC,OAAO,EAAE,CAAC;YAClC,MAAM,IAAI,2BAA2B,CAAC;YACtC,KAAK,MAAM,MAAM,IAAI,cAAc,EAAE,CAAC;gBACpC,MAAM,IAAI,KAAK,MAAM,IAAI,CAAC;YAC5B,CAAC;QACH,CAAC;aAAM,IAAI,OAAO,EAAE,CAAC;YACnB,MAAM,IAAI,0BAA0B,CAAC;YACrC,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,MAAM,IAAI,CAAC;YAC5B,CAAC;YACD,MAAM,IAAI,6BAA6B,CAAC;YACxC,KAAK,MAAM,MAAM,IAAI,cAAc,EAAE,CAAC;gBACpC,MAAM,IAAI,KAAK,MAAM,IAAI,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,IAAI,yBAAyB,aAAa,IAAI,CAAC;QACvD,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,MAAuB,EAAE,WAAwB;QAC7D,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO;gBACL,UAAU,EAAE,UAAU,eAAe,EAAE;gBACvC,aAAa,EAAE,qBAAqB,eAAe,EAAE;aACtD,CAAC;QACJ,CAAC;QAED,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YACxB,OAAO;gBACL,UAAU,EAAE,2CAA2C;gBACvD,aAAa,EAAE,uBAAuB;aACvC,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS;gBACjC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,MAAM,CAAC,SAAS,CAAC;gBAC5D,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAE/B,yCAAyC;YACzC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE,UAAU,CAAC,CAAC;YACzF,IAAI,WAAW,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBAC/B,OAAO;oBACL,UAAU,EAAE,gEAAgE;oBAC5E,aAAa,EAAE,6BAA6B;iBAC7C,CAAC;YACJ,CAAC;YAED,IAAI,MAA4D,CAAC;YACjE,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,cAAc,GAAG,EAAE,CAAC;YAExB,QAAQ,MAAM,CAAC,MAAM,EAAE,CAAC;gBACtB,KAAK,MAAM;oBACT,MAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACxB,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC;wBACf,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAClB,CAAC;yBAAM,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;wBACzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAClB,CAAC;oBAED,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;oBACxD,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;wBAC1B,OAAO;4BACL,UAAU,EAAE,4BAA4B,MAAM,CAAC,MAAM,EAAE;4BACvD,aAAa,EAAE,sBAAsB,MAAM,CAAC,MAAM,EAAE;yBACrD,CAAC;oBACJ,CAAC;oBAED,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,KAAK,EAAE,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC;oBAC3F,cAAc,GAAG,qBAAqB,CAAC;oBACvC,MAAM;gBAER,KAAK,QAAQ;oBACX,MAAM,UAAU,GAAG,CAAC,UAAU,EAAE,IAAI,EAAE,MAAM,CAAC,WAAY,CAAC,CAAC;oBAC3D,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;wBACzB,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;oBACxC,CAAC;oBACD,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;wBACjB,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,+BAA+B;oBAChE,CAAC;oBAED,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;oBAC9D,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;wBAC1B,OAAO;4BACL,UAAU,EAAE,4BAA4B,MAAM,CAAC,MAAM,EAAE;4BACvD,aAAa,EAAE,0BAA0B,MAAM,CAAC,MAAM,EAAE;yBACzD,CAAC;oBACJ,CAAC;oBAED,MAAM,GAAG,uEAAuE,MAAM,CAAC,WAAW,IAAI,CAAC;oBACvG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;wBACzB,MAAM,IAAI,WAAW,MAAM,CAAC,aAAa,IAAI,CAAC;oBAChD,CAAC;oBACD,MAAM,IAAI,IAAI,CAAC;oBACf,cAAc,GAAG,mBAAmB,MAAM,CAAC,WAAW,EAAE,CAAC;oBACzD,MAAM;gBAER,KAAK,QAAQ;oBACX,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;oBAC9C,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,CAAC,WAAY,CAAC,EAAE,UAAU,CAAC,CAAC;oBAC/F,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;wBAC1B,OAAO;4BACL,UAAU,EAAE,4BAA4B,MAAM,CAAC,MAAM,EAAE;4BACvD,aAAa,EAAE,0BAA0B,MAAM,CAAC,MAAM,EAAE;yBACzD,CAAC;oBACJ,CAAC;oBAED,MAAM,GAAG,yDAAyD,MAAM,CAAC,WAAW,MAAM,CAAC;oBAC3F,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;wBACjB,MAAM,IAAI,iFAAiF,CAAC;oBAC9F,CAAC;oBACD,cAAc,GAAG,mBAAmB,MAAM,CAAC,WAAW,EAAE,CAAC;oBACzD,MAAM;gBAER,KAAK,QAAQ;oBACX,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,WAAY,CAAC,EAAE,UAAU,CAAC,CAAC;oBACrF,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;wBAC1B,OAAO;4BACL,UAAU,EAAE,4BAA4B,MAAM,CAAC,MAAM,EAAE;4BACvD,aAAa,EAAE,wBAAwB,MAAM,CAAC,MAAM,EAAE;yBACvD,CAAC;oBACJ,CAAC;oBAED,MAAM,GAAG,6DAA6D,MAAM,CAAC,WAAW,MAAM,CAAC;oBAC/F,cAAc,GAAG,uBAAuB,MAAM,CAAC,WAAW,EAAE,CAAC;oBAC7D,MAAM;gBAER,KAAK,OAAO;oBACV,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;oBAC9D,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,WAAY,CAAC,EAAE,UAAU,CAAC,CAAC;oBAElF,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;wBAC1B,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;4BACvC,MAAM,GAAG,gFAAgF,MAAM,CAAC,WAAW,aAAa,aAAa,QAAQ,CAAC;4BAC9I,MAAM,IAAI,uBAAuB,GAAG,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC;4BAChE,MAAM,IAAI,6DAA6D,CAAC;4BACxE,cAAc,GAAG,sBAAsB,MAAM,CAAC,WAAW,MAAM,aAAa,EAAE,CAAC;wBACjF,CAAC;6BAAM,CAAC;4BACN,OAAO;gCACL,UAAU,EAAE,2BAA2B,MAAM,CAAC,MAAM,EAAE;gCACtD,aAAa,EAAE,uBAAuB,MAAM,CAAC,MAAM,EAAE;6BACtD,CAAC;wBACJ,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,MAAM,GAAG,gDAAgD,MAAM,CAAC,WAAW,aAAa,aAAa,QAAQ,CAAC;wBAC9G,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;4BACzB,MAAM,IAAI,2BAA2B,GAAG,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;wBACpE,CAAC;wBACD,cAAc,GAAG,UAAU,MAAM,CAAC,WAAW,SAAS,aAAa,EAAE,CAAC;oBACxE,CAAC;oBACD,MAAM;gBAER;oBACE,OAAO;wBACL,UAAU,EAAE,0BAA0B,MAAM,CAAC,MAAM,GAAG;wBACtD,aAAa,EAAE,0BAA0B,MAAM,CAAC,MAAM,EAAE;qBACzD,CAAC;YACN,CAAC;YAED,OAAO;gBACL,UAAU,EAAE,MAAM;gBAClB,aAAa,EAAE,cAAc;aAC9B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,yCAAyC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;YACvF,OAAO;gBACL,UAAU,EAAE,YAAY;gBACxB,aAAa,EAAE,YAAY;aAC5B,CAAC;QACJ,CAAC;IACH,CAAC"}