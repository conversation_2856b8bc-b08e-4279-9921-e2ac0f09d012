/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput } from 'ink';
import { ThemeManager } from '../../themes/themes.js';

export interface RadioOption {
  value: string;
  label: string;
  description?: string;
  disabled?: boolean;
}

export interface RadioButtonsProps {
  themeManager: ThemeManager;
  options: RadioOption[];
  selectedValue?: string;
  onSelectionChange?: (value: string, option: RadioOption) => void;
  onSubmit?: (value: string, option: RadioOption) => void;
  disabled?: boolean;
  showDescriptions?: boolean;
  showIndicators?: boolean;
  allowDeselect?: boolean;
  variant?: 'default' | 'compact' | 'detailed';
  title?: string;
  helpText?: string;
  width?: number;
  maxHeight?: number;
  scrollable?: boolean;
}

export const RadioButtons: React.FC<RadioButtonsProps> = ({
  themeManager,
  options,
  selectedValue,
  onSelectionChange,
  onSubmit,
  disabled = false,
  showDescriptions = true,
  showIndicators = true,
  allowDeselect = false,
  variant = 'default',
  title,
  helpText,
  width,
  maxHeight,
  scrollable = true,
}) => {
  const [selectedIndex, setSelectedIndex] = useState<number>(() => {
    if (selectedValue) {
      const index = options.findIndex(option => option.value === selectedValue);
      return index >= 0 ? index : -1;
    }
    return -1;
  });

  const [scrollOffset, setScrollOffset] = useState(0);

  // Update selected index when selectedValue prop changes
  useEffect(() => {
    if (selectedValue) {
      const index = options.findIndex(option => option.value === selectedValue);
      setSelectedIndex(index >= 0 ? index : -1);
    } else {
      setSelectedIndex(-1);
    }
  }, [selectedValue, options]);

  // Handle scrolling for large lists
  const visibleOptions = maxHeight && scrollable ? 
    options.slice(scrollOffset, scrollOffset + maxHeight) : 
    options;

  const handleSelectionChange = useCallback((index: number) => {
    if (index >= 0 && index < options.length) {
      const option = options[index];
      if (!option.disabled) {
        const newIndex = allowDeselect && selectedIndex === index ? -1 : index;
        setSelectedIndex(newIndex);
        
        if (newIndex >= 0 && onSelectionChange) {
          onSelectionChange(option.value, option);
        } else if (newIndex === -1 && onSelectionChange && allowDeselect) {
          onSelectionChange('', {} as RadioOption);
        }
      }
    }
  }, [selectedIndex, options, onSelectionChange, allowDeselect]);

  const handleSubmit = useCallback(() => {
    if (selectedIndex >= 0 && selectedIndex < options.length) {
      const option = options[selectedIndex];
      if (!option.disabled && onSubmit) {
        onSubmit(option.value, option);
      }
    }
  }, [selectedIndex, options, onSubmit]);

  useInput((input, key) => {
    if (disabled || options.length === 0) return;

    if (key.upArrow) {
      const newIndex = selectedIndex > 0 ? selectedIndex - 1 : options.length - 1;
      handleSelectionChange(newIndex);
      
      // Handle scrolling
      if (scrollable && maxHeight && newIndex < scrollOffset) {
        setScrollOffset(Math.max(0, newIndex));
      }
    } else if (key.downArrow) {
      const newIndex = selectedIndex < options.length - 1 ? selectedIndex + 1 : 0;
      handleSelectionChange(newIndex);
      
      // Handle scrolling
      if (scrollable && maxHeight && newIndex >= scrollOffset + maxHeight) {
        setScrollOffset(Math.min(options.length - maxHeight, newIndex - maxHeight + 1));
      }
    } else if (key.return) {
      handleSubmit();
    } else if (input === ' ') {
      // Space bar toggles selection
      handleSelectionChange(selectedIndex);
    }

    // Handle number key selection (1-9)
    const numKey = parseInt(input);
    if (!isNaN(numKey) && numKey >= 1 && numKey <= Math.min(9, options.length)) {
      handleSelectionChange(numKey - 1);
    }
  });

  const getIndicator = (isSelected: boolean, option: RadioOption) => {
    if (!showIndicators) return '';

    if (option.disabled) {
      return themeManager.muted('○ ');
    }

    if (isSelected) {
      return themeManager.primary('● ');
    }

    return themeManager.muted('○ ');
  };

  const getOptionText = (option: RadioOption, isSelected: boolean, isFocused: boolean) => {
    let text = option.label;
    
    if (option.disabled) {
      return themeManager.muted(text);
    }
    
    if (isSelected) {
      return themeManager.primary(text);
    }
    
    if (isFocused) {
      return themeManager.highlight(text);
    }
    
    return text;
  };

  const renderOption = (option: RadioOption, actualIndex: number) => {
    const isSelected = selectedIndex === actualIndex;
    const isFocused = selectedIndex === actualIndex;
    const indicator = getIndicator(isSelected, option);
    const optionText = getOptionText(option, isSelected, isFocused);
    
    const optionContent = (
      <Box key={actualIndex} flexDirection="column">
        <Box>
          <Text>{indicator}</Text>
          <Text>{optionText}</Text>
          {variant !== 'compact' && showDescriptions && option.description && (
            <Text> - {themeManager.muted(option.description)}</Text>
          )}
        </Box>
        {variant === 'detailed' && showDescriptions && option.description && (
          <Box marginLeft={4}>
            <Text>{themeManager.muted(option.description)}</Text>
          </Box>
        )}
      </Box>
    );

    return optionContent;
  };

  const renderTitle = () => {
    if (!title) return null;
    return (
      <Box marginBottom={1}>
        <Text>{themeManager.primary(title)}</Text>
      </Box>
    );
  };

  const renderHelpText = () => {
    if (!helpText) return null;
    return (
      <Box marginTop={1}>
        <Text>{themeManager.muted(helpText)}</Text>
      </Box>
    );
  };

  const renderScrollIndicators = () => {
    if (!scrollable || !maxHeight || options.length <= maxHeight) return null;
    
    const hasMore = scrollOffset + maxHeight < options.length;
    const hasPrevious = scrollOffset > 0;
    
    return (
      <Box justifyContent="space-between">
        <Text>{hasPrevious ? themeManager.muted('↑ More above') : ''}</Text>
        <Text>{hasMore ? themeManager.muted('↓ More below') : ''}</Text>
      </Box>
    );
  };

  if (options.length === 0) {
    return (
      <Box flexDirection="column">
        {renderTitle()}
        <Text>{themeManager.muted('No options available')}</Text>
        {renderHelpText()}
      </Box>
    );
  }

  return (
    <Box flexDirection="column" width={width}>
      {renderTitle()}
      {renderScrollIndicators()}
      <Box flexDirection="column">
        {visibleOptions.map((option, index) => {
          const actualIndex = scrollable && maxHeight ? scrollOffset + index : index;
          return renderOption(option, actualIndex);
        })}
      </Box>
      {renderScrollIndicators()}
      {renderHelpText()}
    </Box>
  );
};

// Convenience components for common use cases
export const CompactRadioButtons: React.FC<Omit<RadioButtonsProps, 'variant'>> = (props) => (
  <RadioButtons {...props} variant="compact" />
);

export const DetailedRadioButtons: React.FC<Omit<RadioButtonsProps, 'variant'>> = (props) => (
  <RadioButtons {...props} variant="detailed" />
);
