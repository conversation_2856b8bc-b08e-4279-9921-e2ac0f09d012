/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { ThemeManager } from '../../themes/themes.js';

interface ProviderConfig {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  apiKey: string;
  baseUrl?: string;
  models: string[];
  defaultModel: string;
  maxTokens: number;
  temperature: number;
  settings: Record<string, any>;
}

interface ProviderSettingsProps {
  themeManager: ThemeManager;
  isOpen: boolean;
  onClose: () => void;
  onSave?: (providers: ProviderConfig[]) => void;
}

export const ProviderSettings: React.FC<ProviderSettingsProps> = ({
  themeManager,
  isOpen,
  onClose,
  onSave,
}) => {
  const [selectedProviderIndex, setSelectedProviderIndex] = useState(0);
  const [selectedFieldIndex, setSelectedFieldIndex] = useState(0);
  const [editingValue, setEditingValue] = useState<string>('');
  const [mode, setMode] = useState<'providers' | 'fields' | 'editing'>('providers');
  const [hasChanges, setHasChanges] = useState(false);

  const [providers, setProviders] = useState<ProviderConfig[]>([
    {
      id: 'openai',
      name: 'OpenAI',
      description: 'OpenAI GPT models',
      enabled: true,
      apiKey: '',
      baseUrl: 'https://api.openai.com/v1',
      models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
      defaultModel: 'gpt-4',
      maxTokens: 4096,
      temperature: 0.7,
      settings: {
        organization: '',
        project: '',
      },
    },
    {
      id: 'anthropic',
      name: 'Anthropic',
      description: 'Claude models by Anthropic',
      enabled: false,
      apiKey: '',
      baseUrl: 'https://api.anthropic.com',
      models: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
      defaultModel: 'claude-3-sonnet',
      maxTokens: 4096,
      temperature: 0.7,
      settings: {
        version: '2023-06-01',
      },
    },
    {
      id: 'google',
      name: 'Google AI',
      description: 'Google Gemini models',
      enabled: false,
      apiKey: '',
      baseUrl: 'https://generativelanguage.googleapis.com/v1',
      models: ['gemini-pro', 'gemini-pro-vision'],
      defaultModel: 'gemini-pro',
      maxTokens: 2048,
      temperature: 0.7,
      settings: {},
    },
    {
      id: 'deepseek',
      name: 'DeepSeek',
      description: 'DeepSeek AI models',
      enabled: false,
      apiKey: '',
      baseUrl: 'https://api.deepseek.com/v1',
      models: ['deepseek-chat', 'deepseek-coder'],
      defaultModel: 'deepseek-chat',
      maxTokens: 4096,
      temperature: 0.7,
      settings: {},
    },
  ]);

  useInput((input, key) => {
    if (!isOpen) return;

    if (key.escape) {
      if (mode === 'editing') {
        setMode('fields');
        setEditingValue('');
        return;
      }
      if (mode === 'fields') {
        setMode('providers');
        return;
      }
      onClose();
      return;
    }

    if (mode === 'providers') {
      if (key.upArrow && selectedProviderIndex > 0) {
        setSelectedProviderIndex(selectedProviderIndex - 1);
        return;
      }
      if (key.downArrow && selectedProviderIndex < providers.length - 1) {
        setSelectedProviderIndex(selectedProviderIndex + 1);
        return;
      }
      if (key.return) {
        setMode('fields');
        setSelectedFieldIndex(0);
        return;
      }
      if (input === ' ') {
        toggleProviderEnabled(selectedProviderIndex);
        return;
      }
    }

    if (mode === 'fields') {
      const fieldCount = getFieldCount();
      
      if (key.upArrow && selectedFieldIndex > 0) {
        setSelectedFieldIndex(selectedFieldIndex - 1);
        return;
      }
      if (key.downArrow && selectedFieldIndex < fieldCount - 1) {
        setSelectedFieldIndex(selectedFieldIndex + 1);
        return;
      }
      if (key.return) {
        const fieldName = getFieldName(selectedFieldIndex);
        if (fieldName === 'enabled') {
          toggleProviderEnabled(selectedProviderIndex);
        } else {
          setMode('editing');
          setEditingValue(getFieldValue(selectedFieldIndex)?.toString() || '');
        }
        return;
      }
    }

    if (mode === 'editing') {
      if (key.return) {
        updateFieldValue(selectedFieldIndex, editingValue);
        setMode('fields');
        setEditingValue('');
        return;
      }

      if (key.backspace || key.delete) {
        setEditingValue(prev => prev.slice(0, -1));
        return;
      }

      if (input && input.length === 1 && !key.ctrl && !key.meta) {
        setEditingValue(prev => prev + input);
      }
    }

    // Global shortcuts
    if (key.ctrl && input === 's') {
      handleSave();
      return;
    }
  });

  const getFieldCount = () => {
    return 8; // enabled, apiKey, baseUrl, defaultModel, maxTokens, temperature, test, save
  };

  const getFieldName = (index: number): string => {
    const fields = ['enabled', 'apiKey', 'baseUrl', 'defaultModel', 'maxTokens', 'temperature', 'test', 'save'];
    return fields[index] || '';
  };

  const getFieldValue = (index: number): any => {
    const provider = providers[selectedProviderIndex];
    const fieldName = getFieldName(index);
    
    switch (fieldName) {
      case 'enabled':
        return provider.enabled;
      case 'apiKey':
        return provider.apiKey;
      case 'baseUrl':
        return provider.baseUrl;
      case 'defaultModel':
        return provider.defaultModel;
      case 'maxTokens':
        return provider.maxTokens;
      case 'temperature':
        return provider.temperature;
      default:
        return '';
    }
  };

  const updateFieldValue = (fieldIndex: number, value: string) => {
    const fieldName = getFieldName(fieldIndex);
    
    setProviders(prev => prev.map((provider, index) => {
      if (index === selectedProviderIndex) {
        const updatedProvider = { ...provider };
        
        switch (fieldName) {
          case 'apiKey':
            updatedProvider.apiKey = value;
            break;
          case 'baseUrl':
            updatedProvider.baseUrl = value;
            break;
          case 'defaultModel':
            updatedProvider.defaultModel = value;
            break;
          case 'maxTokens':
            updatedProvider.maxTokens = parseInt(value) || 0;
            break;
          case 'temperature':
            updatedProvider.temperature = parseFloat(value) || 0;
            break;
        }
        
        return updatedProvider;
      }
      return provider;
    }));
    
    setHasChanges(true);
  };

  const toggleProviderEnabled = (providerIndex: number) => {
    setProviders(prev => prev.map((provider, index) => 
      index === providerIndex 
        ? { ...provider, enabled: !provider.enabled }
        : provider
    ));
    setHasChanges(true);
  };

  const handleSave = () => {
    onSave?.(providers);
    setHasChanges(false);
  };



  if (!isOpen) return null;

  const renderProvidersList = () => (
    <Box flexDirection="column">
      <Text>{themeManager.primary('AI Providers:')}</Text>
      <Text></Text>
      {providers.map((provider, index) => (
        <Box key={provider.id} marginLeft={2}>
          <Text>
            {selectedProviderIndex === index ? themeManager.highlight(' ▶ ') : '   '}
            {provider.enabled ? themeManager.success('●') : themeManager.muted('○')}
            {' '}
            {themeManager.primary(provider.name)}
            {' - '}
            {themeManager.muted(provider.description)}
          </Text>
        </Box>
      ))}
      <Text></Text>
      <Box marginLeft={2}>
        <Text>{themeManager.muted('Space: Toggle • Enter: Configure')}</Text>
      </Box>
    </Box>
  );

  const renderProviderFields = () => {
    const provider = providers[selectedProviderIndex];
    
    return (
      <Box flexDirection="column">
        <Text>{themeManager.primary(`Configure: ${provider.name}`)}</Text>
        <Text></Text>
        
        {[
          { name: 'enabled', label: 'Enabled', value: provider.enabled ? 'Yes' : 'No', type: 'boolean' },
          { name: 'apiKey', label: 'API Key', value: provider.apiKey ? '*'.repeat(provider.apiKey.length) : 'Not set', type: 'password' },
          { name: 'baseUrl', label: 'Base URL', value: provider.baseUrl || 'Not set', type: 'string' },
          { name: 'defaultModel', label: 'Default Model', value: provider.defaultModel, type: 'select' },
          { name: 'maxTokens', label: 'Max Tokens', value: provider.maxTokens.toString(), type: 'number' },
          { name: 'temperature', label: 'Temperature', value: provider.temperature.toString(), type: 'number' },
          { name: 'test', label: 'Test Connection', value: 'Click to test', type: 'action' },
          { name: 'save', label: 'Save Changes', value: hasChanges ? 'Unsaved changes' : 'No changes', type: 'action' },
        ].map((field, index) => (
          <Box key={field.name} marginLeft={2} marginBottom={1}>
            <Box>
              <Text>
                {selectedFieldIndex === index ? themeManager.highlight(' ▶ ') : '   '}
                {themeManager.primary(field.label)}:
              </Text>
            </Box>
            <Box marginLeft={3}>
              <Text>
                {field.type === 'boolean' && provider.enabled ? themeManager.success(field.value) : 
                 field.type === 'boolean' && !provider.enabled ? themeManager.muted(field.value) :
                 field.type === 'password' ? themeManager.accent(field.value) :
                 field.type === 'action' && field.name === 'save' && hasChanges ? themeManager.warning(field.value) :
                 field.type === 'action' ? themeManager.info(field.value) :
                 themeManager.accent(field.value)}
              </Text>
            </Box>
          </Box>
        ))}
        
        <Text></Text>
        <Box marginLeft={2}>
          <Text>{themeManager.secondary('Available Models: ')}</Text>
          <Text>{themeManager.muted(provider.models.join(', '))}</Text>
        </Box>
      </Box>
    );
  };

  const renderEditingInterface = () => {
    const provider = providers[selectedProviderIndex];
    const fieldName = getFieldName(selectedFieldIndex);
    
    return (
      <Box flexDirection="column">
        <Text>{themeManager.primary(`Editing: ${fieldName}`)}</Text>
        <Text></Text>
        <Box marginLeft={2}>
          <Text>{themeManager.secondary('Provider: ')}</Text>
          <Text>{themeManager.info(provider.name)}</Text>
        </Box>
        <Text></Text>
        <Box marginLeft={2}>
          <Text>{themeManager.secondary('Current Value: ')}</Text>
          <Text>{themeManager.primary(editingValue || '...')}</Text>
        </Box>
        
        {fieldName === 'defaultModel' && (
          <Box marginLeft={2} marginTop={1}>
            <Text>{themeManager.secondary('Available Models: ')}</Text>
            <Text>{themeManager.muted(provider.models.join(', '))}</Text>
          </Box>
        )}
        
        {fieldName === 'temperature' && (
          <Box marginLeft={2} marginTop={1}>
            <Text>{themeManager.muted('Range: 0.0 to 2.0 (0.7 recommended)')}</Text>
          </Box>
        )}
      </Box>
    );
  };

  const renderControls = () => (
    <Box marginTop={1} borderStyle="single" borderColor="gray" padding={1}>
      <Box flexDirection="column">
        <Text>{themeManager.muted('Controls:')}</Text>
        {mode === 'providers' && (
          <Text>{themeManager.muted('  ↑↓ Navigate • Enter Configure • Space Toggle • Esc Close')}</Text>
        )}
        {mode === 'fields' && (
          <Text>{themeManager.muted('  ↑↓ Navigate • Enter Edit • Esc Back • Ctrl+S Save')}</Text>
        )}
        {mode === 'editing' && (
          <Text>{themeManager.muted('  Type value • Enter Save • Esc Cancel')}</Text>
        )}
        {hasChanges && (
          <Text>{themeManager.warning('  Unsaved changes!')}</Text>
        )}
      </Box>
    </Box>
  );

  return (
    <Box flexDirection="column" padding={1}>
      <Box borderStyle="round" borderColor="blue" padding={1}>
        <Box flexDirection="column" width="100%">
          <Text>{themeManager.primary('🤖 Provider Settings')}</Text>
          <Text></Text>
          
          {mode === 'providers' && renderProvidersList()}
          {mode === 'fields' && renderProviderFields()}
          {mode === 'editing' && renderEditingInterface()}
          
          {renderControls()}
        </Box>
      </Box>
    </Box>
  );
};
