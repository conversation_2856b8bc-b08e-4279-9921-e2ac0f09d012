/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React from 'react';
import { Box, Text } from 'ink';
import { ThemeManager } from '../../themes/themes.js';

interface MessageFormatterProps {
  themeManager: ThemeManager;
  content: string;
  enableMarkdown?: boolean;
  enableCodeHighlighting?: boolean;
  enableLinkDetection?: boolean;
  maxWidth?: number;
}

interface FormattedSegment {
  type: 'text' | 'bold' | 'italic' | 'code' | 'codeblock' | 'link' | 'header' | 'list';
  content: string;
  language?: string;
  level?: number;
}

export const MessageFormatter: React.FC<MessageFormatterProps> = ({
  themeManager,
  content,
  enableMarkdown = true,
  enableLinkDetection = true,
  maxWidth = 80,
}) => {
  const parseMarkdown = (text: string): FormattedSegment[] => {
    if (!enableMarkdown) {
      return [{ type: 'text', content: text }];
    }

    const segments: FormattedSegment[] = [];
    const lines = text.split('\n');
    let inCodeBlock = false;
    let codeBlockLanguage = '';

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Code blocks
      if (line.startsWith('```')) {
        if (inCodeBlock) {
          inCodeBlock = false;
          codeBlockLanguage = '';
        } else {
          inCodeBlock = true;
          codeBlockLanguage = line.substring(3).trim();
        }
        continue;
      }

      if (inCodeBlock) {
        segments.push({
          type: 'codeblock',
          content: line,
          language: codeBlockLanguage,
        });
        continue;
      }

      // Headers
      if (line.startsWith('#')) {
        const level = line.match(/^#+/)?.[0].length || 1;
        const headerContent = line.substring(level).trim();
        segments.push({
          type: 'header',
          content: headerContent,
          level,
        });
        continue;
      }

      // Lists
      if (line.match(/^\s*[-*+]\s/)) {
        const listContent = line.replace(/^\s*[-*+]\s/, '');
        segments.push({
          type: 'list',
          content: listContent,
        });
        continue;
      }

      // Parse inline formatting
      const inlineSegments = parseInlineFormatting(line);
      segments.push(...inlineSegments);

      // Add line break if not the last line
      if (i < lines.length - 1) {
        segments.push({ type: 'text', content: '\n' });
      }
    }

    return segments;
  };

  const parseInlineFormatting = (text: string): FormattedSegment[] => {
    const segments: FormattedSegment[] = [];

    // Regular expressions for different formatting
    const patterns = [
      { type: 'code' as const, regex: /`([^`]+)`/g },
      { type: 'bold' as const, regex: /\*\*([^*]+)\*\*/g },
      { type: 'italic' as const, regex: /\*([^*]+)\*/g },
      { type: 'link' as const, regex: /\[([^\]]+)\]\(([^)]+)\)/g },
    ];

    const matches: Array<{ type: FormattedSegment['type']; start: number; end: number; content: string; url?: string }> = [];

    // Find all matches
    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.regex.exec(text)) !== null) {
        matches.push({
          type: pattern.type,
          start: match.index,
          end: match.index + match[0].length,
          content: match[1],
          url: pattern.type === 'link' ? match[2] : undefined,
        });
      }
    });

    // Sort matches by position
    matches.sort((a, b) => a.start - b.start);

    let lastEnd = 0;
    matches.forEach(match => {
      // Add text before the match
      if (match.start > lastEnd) {
        const beforeText = text.substring(lastEnd, match.start);
        if (beforeText) {
          segments.push({ type: 'text', content: beforeText });
        }
      }

      // Add the formatted segment
      segments.push({
        type: match.type,
        content: match.content,
      });

      lastEnd = match.end;
    });

    // Add remaining text
    if (lastEnd < text.length) {
      const remainingText = text.substring(lastEnd);
      if (remainingText) {
        segments.push({ type: 'text', content: remainingText });
      }
    }

    // If no matches found, return the original text
    if (matches.length === 0) {
      segments.push({ type: 'text', content: text });
    }

    return segments;
  };

  const detectLinks = (text: string): string => {
    if (!enableLinkDetection) return text;

    const urlRegex = /(https?:\/\/[^\s]+)/g;
    return text.replace(urlRegex, (url) => `[${url}](${url})`);
  };



  const renderSegment = (segment: FormattedSegment, index: number): React.ReactNode => {
    switch (segment.type) {
      case 'text':
        return <Text key={index}>{segment.content}</Text>;

      case 'bold':
        return <Text key={index}>{themeManager.primary(segment.content)}</Text>;

      case 'italic':
        return <Text key={index}>{themeManager.secondary(segment.content)}</Text>;

      case 'code':
        return <Text key={index}>{themeManager.accent(`\`${segment.content}\``)}</Text>;

      case 'codeblock':
        const language = segment.language || 'text';
        return (
          <Box key={index} marginLeft={2} borderStyle="single" borderColor="gray">
            <Box flexDirection="column" padding={1}>
              {language && (
                <Text>{themeManager.muted(`// ${language}`)}</Text>
              )}
              <Text>{themeManager.accent(segment.content)}</Text>
            </Box>
          </Box>
        );

      case 'header':
        const headerColor = segment.level === 1 ? themeManager.primary : 
                           segment.level === 2 ? themeManager.secondary : 
                           themeManager.accent;
        const headerPrefix = '#'.repeat(segment.level || 1);
        return (
          <Text key={index}>
            {headerColor(`${headerPrefix} ${segment.content}`)}
          </Text>
        );

      case 'list':
        return (
          <Box key={index} marginLeft={2}>
            <Text>{themeManager.accent('•')} {segment.content}</Text>
          </Box>
        );

      case 'link':
        return <Text key={index}>{themeManager.info(segment.content)}</Text>;

      default:
        return <Text key={index}>{segment.content}</Text>;
    }
  };

  const processedContent = enableLinkDetection ? detectLinks(content) : content;
  const segments = parseMarkdown(processedContent);

  return (
    <Box flexDirection="column" width={maxWidth}>
      {segments.map((segment, index) => renderSegment(segment, index))}
    </Box>
  );
};

// Specialized formatter variants
export const CodeFormatter: React.FC<Omit<MessageFormatterProps, 'enableMarkdown' | 'enableCodeHighlighting'>> = (props) => (
  <MessageFormatter {...props} enableMarkdown={false} enableCodeHighlighting={true} />
);

export const PlainTextFormatter: React.FC<Omit<MessageFormatterProps, 'enableMarkdown' | 'enableCodeHighlighting' | 'enableLinkDetection'>> = (props) => (
  <MessageFormatter {...props} enableMarkdown={false} enableCodeHighlighting={false} enableLinkDetection={false} />
);

export const MarkdownFormatter: React.FC<Omit<MessageFormatterProps, 'enableMarkdown'>> = (props) => (
  <MessageFormatter {...props} enableMarkdown={true} />
);
